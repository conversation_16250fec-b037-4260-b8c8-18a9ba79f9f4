{
  "compilerOptions": {
    "baseUrl": ".",
    "target": "ES2021",
    "useDefineForClassFields": true,
    "lib": ["ES2021", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "allowSyntheticDefaultImports": true,
    "paths": {
      "@editor/*": [
        "./src/editor-src/*"
      ],
      "@public/*": [
        "./public/*"
      ],
      "@editor-editor/*": [
        "./editor-src/editor/*"
      ]
    },
    "typeRoots": [
      "./node_modules/@types",
      "./typings"
    ]
  },
  "include": [
    "src",
    "**/*.ts",
    "**/*.tsx",
    "**/*.js"
  ],
  "references": [{ "path": "./tsconfig.node.json" }]
}
