{"env": {"browser": true, "node": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "parser": "babel-es<PERSON>"}, "plugins": ["@typescript-eslint", "react"], "rules": {"no-console": "warn", "react/react-in-jsx-scope": "off", "no-unused-vars": "off", "react/prop-types": "off", "@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "warn", "ban-ts-comment": "off"}}