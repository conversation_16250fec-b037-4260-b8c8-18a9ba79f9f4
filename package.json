{"name": "book-guard", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --mode dev", "build": "vite build --mode prod", "preview": "vite preview", "tauri": "tauri", "tauri dev": "tauri dev", "tauri build": "tauri build"}, "dependencies": {"@ant-design/icons": "^5.3.7", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@excalidraw/excalidraw": "^0.17.6", "@fontsource/roboto": "^5.0.13", "@lexical/headless": "^0.18.0", "@lexical/react": "^0.18.0", "@mui/icons-material": "^5.15.20", "@mui/material": "^5.15.20", "@mui/x-charts": "^7.6.2", "@next-auth/prisma-adapter": "^1.0.7", "@next/bundle-analyzer": "14.2.4", "@next/third-parties": "14.2.4", "@prisma/client": "^5.15.0", "@reduxjs/toolkit": "^2.2.5", "@tauri-apps/api": "^2.0.0", "@tauri-apps/plugin-dialog": "~2", "@tauri-apps/plugin-fs": "~2", "@tauri-apps/plugin-http": "~2", "@tauri-apps/plugin-notification": "~2", "@tauri-apps/plugin-shell": "~2", "@tauri-apps/plugin-opener": "~2", "@tauri-apps/plugin-upload": "~2", "@types/node": "20.14.2", "@types/nprogress": "^0.2.3", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@types/uuid": "^9.0.7", "ai": "^3.1.33", "antd": "^5.19.2", "axios": "^1.8.3", "better-react-mathjax": "^2.0.3", "classnames": "^2.5.1", "compressorjs": "^1.2.1", "docx": "^8.5.0", "eslint-config-next": "14.2.4", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "htmr": "^1.0.2", "jspdf": "^2.5.2", "lexical": "^0.18.0", "linkedom": "^0.18.3", "localforage": "^1.10.0", "match-sorter": "^6.3.1", "mathlive": "^0.100.0", "mathml-to-latex": "^1.3.0", "next": "14.2.4", "next-auth": "^4.24.7", "nprogress": "^0.2.0", "openai": "^4.51.0", "prismjs": "^1.29.0", "puppeteer": "^22.11.0", "react": "18.3.1", "react-dom": "18.3.1", "react-helmet": "^6.1.0", "react-highlight-words": "^0.20.0", "react-intersection-observer": "^9.10.3", "react-redux": "^9.1.2", "react-router-dom": "^6.20.1", "sort-by": "^0.0.2", "ts-pattern": "^5.2.0", "typescript": "^5.4.5", "uuid": "^10.0.0", "vite-plugin-prismjs": "^0.0.11"}, "devDependencies": {"@tauri-apps/cli": "^2.0.0", "@types/file-saver": "^2.0.7", "@types/prismjs": "^1.26.4", "@types/react-highlight-words": "^0.20.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "vite": "npm:rolldown-vite@latest"}}