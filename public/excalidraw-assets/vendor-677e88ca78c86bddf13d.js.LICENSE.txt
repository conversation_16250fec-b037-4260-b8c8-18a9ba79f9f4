/*
object-assign
(c) <PERSON><PERSON> So<PERSON>
@license MIT
*/

/*!

pica
https://github.com/nodeca/pica

*/

/*!
   * Wait for document loaded before starting the execution
   */

/*!
  Embeddable Minimum Strictly-Compliant Promises/A+ 1.1.1 Thenable
  Copyright (c) 2013-2014 <PERSON><PERSON> (http://engelschall.com)
  Licensed under The MIT License (http://opensource.org/licenses/MIT)
  */

/*! @license DOMPurify 3.0.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE */

/*! Bezier curve function generator. Copyright Gaetan Renaudeau. MIT License: http://en.wikipedia.org/wiki/MIT_License */

/*! Check if previously processed */

/*! Runge-Kutta spring physics function generator. Adapted from Framer.js, copyright Koen Bok. MIT License: http://en.wikipedia.org/wiki/MIT_License */

/*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT */
