{"hi/messages/alphabets.min": {"kind": "alphabets", "locale": "hi", "messages": {"latinSmall": ["ए", "बी", "सी", "डी", "ई", "एफ", "जी", "एच", "आय", "जे", "के", "एल", "एम", "एन", "ओ", "पी", "क्यू", "आर", "एस", "टी", "यू", "वी", "डब्ल्यू", "एक्स", "वाई", "जेड"], "latinCap": ["ए", "बी", "सी", "डी", "ई", "एफ", "जी", "एच", "आय", "जे", "के", "एल", "एम", "एन", "ओ", "पी", "क्यू", "आर", "एस", "टी", "यू", "वी", "डब्ल्यू", "एक्स", "वाई", "जेड"], "greekSmall": ["नाबला", "आल्फा", "बीटा", "गामा", "डेल्टा", "एप्सिलॉन", "ज़ेटा", "एटा", "थीटा", "आयोटा", "कप्पा", "लैम्ब्डा", "मु", "नू", "ग्जाए", "ओमिक्रॉन", "पाइ", "रो", "अंतिम सिग्मा ", "सिग्मा", "टाउ", "अपसिलं", "फाई", "काई", "साई", "ओमेगा", "आंशिक अवकलन", "ल्यूनेट एप्सिलॉन ", "थीटा ", "कप्पा ", "फाई", "रो ", "पोमेगा"], "greekCap": ["आल्फा", "बीटा", "गामा", "डेल्टा", "एप्सिलॉन", "ज़ेटा", "एटा", "थीटा", "आयोटा", "कप्पा", "लैम्ब्डा", "मु", "नू", "ग्जाए", "ओमिक्रॉन", "पाइ", "रो", "थीटा", "सिग्मा", "टाउ", "अपसिलं", "फाई", "काई", "साई", "ओमेगा"], "capPrefix": {"default": "कैपिटल"}, "smallPrefix": {"default": ""}, "digitPrefix": {"default": ""}}}, "hi/messages/messages.min": {"kind": "messages", "locale": "hi", "messages": {"MS": {"START": "आर<PERSON>भ", "FRAC_V": "भिन्न", "FRAC_B": "भिन्न", "FRAC_S": "भिन्न", "END": "अंत", "FRAC_OVER": "के हर में", "TWICE": "द<PERSON><PERSON>ा<PERSON>ा", "NEST_FRAC": "नीड़ित भिन्न", "ENDFRAC": "भिन्न समाप्त", "SUPER": "उर्ध्व", "SUB": "अधो", "SUP": "उर्ध्व", "SUPERSCRIPT": "उर्ध्वान्क", "SUBSCRIPT": "पादा<PERSON>क", "BASELINE": "आधार रेखा", "BASE": "<PERSON><PERSON><PERSON><PERSON>", "NESTED": "नीड़ित", "NEST_ROOT": "नीड़ित मूल", "STARTROOT": "मूल आरंभ", "ENDROOT": "मूल समाप्त", "ROOTINDEX": "मूलांक", "ROOT": "मूल", "INDEX": "मूलांक", "UNDER": "निचे", "UNDERSCRIPT": "निम्नांक", "OVER": "ऊपर", "OVERSCRIPT": "उर्ध्व लिपि", "ENDSCRIPTS": "लिपि समाप्त"}, "MSroots": {}, "font": {"bold": "बोल्ड", "bold-fraktur": "बोल्ड फ़्रेक्टुर", "bold-italic": "बोल्ड इटैलिक", "bold-script": "बोल्ड स्क्रिप्ट", "caligraphic": "caligraphic", "caligraphic-bold": "caligraphic-bold", "double-struck": "डबल-स्ट्रक", "double-struck-italic": "डबल-स्ट्रक इटैलिक", "fraktur": "फ़्राक्टुर", "fullwidth": "पूर्णता", "italic": "इटैलिक", "monospace": "मोनोपेस", "normal": "सामान्य", "oldstyle": "oldstyle", "oldstyle-bold": "oldstyle-bold", "script": "लिपि", "sans-serif": "संस-सेरिफ़", "sans-serif-bold": "संस-सेरिफ़ बोल्ड", "sans-serif-italic": "सैंस-सेरिफ़ इटैलिक", "sans-serif-bold-italic": "संस-सेरिफ़ बोल्ड इटैलिक", "unknown": "unknown"}, "embellish": {"super": "सुपरस्क्रिप्ट", "sub": "सबस्क्रिप्ट", "circled": "सर्किल", "parenthesized": "कोष्ठक", "period": ["पूर्ण विराम", "postfix<PERSON><PERSON><PERSON>"], "negative-circled": "नेगेटिव सर्किल", "double-circled": "डबल सर्किल", "circled-sans-serif": "सर्किल सैंस-सेरिफ़", "negative-circled-sans-serif": "नेगेटिव सर्किल सैंस-सेरिफ़", "comma": ["अल्प विराम", "postfix<PERSON><PERSON><PERSON>"], "squared": "चुकता", "negative-squared": "नकारात्मक वर्ग"}, "role": {"addition": "योग", "multiplication": "गुणन", "subtraction": "व्यवकलन", "division": "विभाजन", "equality": "समता", "inequality": "असमता", "element": "अवयव", "arrow": "तीर", "determinant": "सारणिक", "rowvector": "पंक्ति सदिश", "binomial": "द्विपद", "squarematrix": "वर्ग आव्यूह", "set empty": "रिक्त समुच्चय", "set extended": "समुच्चय विस्तार", "set singleton": "वर्ग आव्यूह", "set collection": "संग्रह", "label": "सूचक पर्चा", "multiline": "बहु रेखाएं", "matrix": "आव्यूह", "vector": "सदिश", "cases": "फलन उपशर्त", "table": "सारणी", "unknown": "अज्ञात"}, "enclose": {"longdiv": "विस्तृत विभाजन", "actuarial": "बीमां<PERSON><PERSON>क प्रतिक", "radical": "वर्ग मूल", "box": "कोष्ठ", "roundedbox": "भोथरा कोष्ठ", "circle": "वर्तुल", "left": "वाम ऊर्ध्वाधर रेखा", "right": "दक्षिण ऊर्ध्वाधर रेखा", "top": "क्षैतिज ऊर्ध्वरेखा", "bottom": "क्षैतिज पादरेखा", "updiagonalstrike": "उर्ध्वगामी विकर्ण छेदिका", "downdiagonalstrike": "अधोगामी विकर्ण छेदिका", "verticalstrike": "ऊर्ध्वाधर छेदिका", "horizontalstrike": "छेदन रेखा", "madruwb": "Arabic factorial symbol", "updiagonalarrow": "उर्ध्वगामी विकर्ण तीर", "phasorangle": "फेजर कोण", "unknown": "लंबा विभाजन"}, "navigate": {"COLLAPSIBLE": "निपातीय", "EXPANDABLE": "विस्तारनीय", "LEVEL": "स्तर"}, "regexp": {"TEXT": "a-zA-Z", "NUMBER": "((\\d{1,3})(?=(,| ))((,| )\\d{3})*(\\.\\d+)?)|^\\d*\\.\\d+|^\\d+", "DECIMAL_MARK": "\\.", "DIGIT_GROUP": ",", "JOINER_SUBSUPER": " ", "JOINER_FRAC": ""}, "unitTimes": ""}}, "hi/messages/numbers.min": {"kind": "numbers", "locale": "hi", "messages": {"zero": "शून्य", "ones": ["", "एक", "दो", "तीन", "<PERSON><PERSON><PERSON>", "पाँच", "छः", "सात", "आठ", "नौ", "दस", "ग्यारह", "ब<PERSON><PERSON><PERSON>", "तेरह", "चौदह", "पंद्रह", "सोलह", "सत्रह", "अठारह", "उन्नीस", "बीस", "इक्कीस", "बाईस", "तेइस", "चौबीस", "पच्चीस", "छब्बीस", "सताइस", "अट्ठाइस", "उनतीस", "तीस", "इकतीस", "बतीस", "तैंतीस", "चौंतीस", "पैंतीस", "छतीस", "सैंतीस", "अड़तीस", "उनतालीस", "चालीस", "इकतालीस", "बयालीस", "तैतालीस", "चवालीस", "पैंतालीस", "छयालिस", "सैंतालीस", "अड़तालीस", "उनचास", "पचास", "इक्यावन", "बावन", "तिरपन", "चौवन", "पचपन", "छप्पन", "सतावन", "अठावन", "उनसठ", "साठ", "इकसठ", "बासठ", "तिरसठ", "चौंसठ", "पैंसठ", "छियासठ", "सड़सठ", "अड़सठ", "उनहतर", "सत्तर", "इकहतर", "बह<PERSON>र", "तिहतर", "चौहतर", "पच<PERSON><PERSON>र", "छिहतर", "सतहतर", "अठहतर", "उन्नासी", "अस्सी", "इक्यासी", "बया<PERSON>ी", "तिरा<PERSON>ी", "चौरासी", "पचा<PERSON>ी", "छियासी", "सतासी", "अट्ठासी", "नवा<PERSON>ी", "नब्बे", "इक्यानवे", "ब्यानवे", "तिरानवे", "चौरानवे", "पचानवे", "छियानवे", "सतानवे", "अट्ठानवे", "निन्यानवे"], "large": ["<PERSON><PERSON><PERSON><PERSON>", "लाख", "करोड़", "अरब", "खरब", "नील", "पद्मा", "शंख", "महाशंख", "महाउपाध", "जल्द", "परार्ध", "अंत", "शिष्ट"], "special": {"hundred": "सौ", "smallDenominators": ["", "एकांश", "द्वितीयांश", "तृतीयांश", "चतुर्थांश", "पंच<PERSON><PERSON><PERSON>श", "षष्टांश", "सप्तमांश", "अष्टांश", "नव<PERSON>ा<PERSON>श", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "ordinalsMasculine": ["", "पहला", "दूसरा", "तीसरा", "चौथा", "पांचवाँ", "छठा", "सातवाँ", "आठवाँ", "नौवाँ"], "ordinalsFeminine": ["", "पह<PERSON>ी", "दूसरी", "तीसरी", "चौथी", "पाँचवीं", "छठी", "सातवीं", "आठवीं", "नौवीं"], "simpleNumbers": ["०", "१", "२", "३", "४", "५", "६", "७", "८", "९"], "simpleSmallOrdinalsMasculine": ["", "१ला", "२रा", "३रा", "४था", "५वाँ", "६ठा", "७वाँ", "८वाँ", "९वाँ"], "simpleSmallOrdinalsFeminine": ["", "१ली", "२री", "३री", "४थी", "५वीं", "६ठी", "७वीं", "८वीं", "९वीं"]}, "vulgarSep": " ", "numSep": " "}}, "hi/si/prefixes.min": [{"Y": "योट्टा", "Z": "जेट्टा", "E": "एक्सा", "P": "पेटा", "T": "टेरा", "G": "जिगा", "M": "मेगा", "k": "किलो", "h": "हेक्टो", "d": "डेसी", "c": "सेन्टी", "m": "मिली", "μ": "माइक्रो", "µ": "माइक्रो", "n": "नैनो", "p": "पिको", "f": "फेम्टो", "a": "आट्टो", "z": "जेप्टो", "y": "योक्टो"}], "hi/functions/algebra.min": [{"locale": "hi"}, {"locale": "hi"}, {"key": "deg", "category": "Algebra", "names": ["deg"], "mappings": {"default": {"default": "अंश"}}}, {"key": "det", "category": "Algebra", "names": ["det"], "mappings": {"default": {"default": "सारणिक"}}}, {"key": "dim", "category": "Algebra", "names": ["dim"], "mappings": {"default": {"default": "आयाम"}}}, {"key": "hom", "category": "Algebra", "names": ["hom", "Hom"], "mappings": {"default": {"default": "समरूपी"}}}, {"key": "ker", "category": "Algebra", "names": ["ker"], "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "Tr", "category": "Algebra", "names": ["Tr", "tr"], "mappings": {"default": {"default": "अनुरेख"}}}], "hi/functions/elementary.min": [{"locale": "hi"}, {"key": "log", "category": "Logarithm", "names": ["log"], "mappings": {"default": {"default": "लघुगणक"}}}, {"key": "ln", "category": "Logarithm", "names": ["ln"], "mappings": {"default": {"default": "प्राकृतिक लघुगणक"}, "clearspeak": {"default": "एल एन", "Log_LnAsNaturalLog": "प्राकृतिक लघुगणक"}}}, {"key": "lg", "category": "Logarithm", "names": ["lg"], "mappings": {"default": {"default": "दस आधारित लघुगणक"}}}, {"key": "exp", "category": "Elementary", "names": ["exp", "expt"], "mappings": {"default": {"default": "चरघातांकी"}}}, {"key": "gcd", "category": "Elementary", "names": ["gcd"], "mappings": {"default": {"default": "महत्तम समापवर्तक"}}}, {"key": "lcm", "category": "Elementary", "names": ["lcm"], "mappings": {"default": {"default": "लघुत्तम समापवर्त्य"}}}, {"key": "arg", "category": "Complex", "names": ["arg"], "mappings": {"default": {"default": "तर्क (Argument)"}}}, {"key": "im", "category": "Complex", "names": ["im"], "mappings": {"default": {"default": "काल्पनिक (Imaginary)"}}}, {"key": "re", "category": "Complex", "names": ["re"], "mappings": {"default": {"default": "वास्तविक (Real)"}}}, {"key": "inf", "category": "Limits", "names": ["inf"], "mappings": {"default": {"default": "अधिकतम निम्न परिबद्ध"}}}, {"key": "lim", "category": "Limits", "names": ["lim"], "mappings": {"default": {"default": "सीमा"}}}, {"key": "liminf", "category": "Limits", "names": ["lim inf", "liminf"], "mappings": {"default": {"default": "निम्न सीमा"}}}, {"key": "limsup", "category": "Limits", "names": ["lim sup", "limsup"], "mappings": {"default": {"default": "उच्च सीमा"}}}, {"key": "max", "category": "Limits", "names": ["max"], "mappings": {"default": {"default": "महत्तम"}}}, {"key": "min", "category": "Limits", "names": ["min"], "mappings": {"default": {"default": "न्यूनतम"}}}, {"key": "sup", "category": "Limits", "names": ["sup"], "mappings": {"default": {"default": "लघुत्तम उच्च परिबद्ध"}}}, {"key": "<PERSON><PERSON><PERSON>", "category": "Limits", "names": ["<PERSON><PERSON><PERSON>", "inj lim"], "mappings": {"default": {"default": "सह सीमा"}}}, {"key": "proj<PERSON>", "category": "Limits", "names": ["proj<PERSON>", "proj lim"], "mappings": {"default": {"default": "व्युत्क्रम सीमा"}}}, {"key": "mod", "category": "Elementary", "names": ["mod"], "mappings": {"default": {"default": "मापांक (modulus)"}}}, {"key": "Pr", "category": "Probability", "names": ["Pr"], "mappings": {"default": {"default": "प्रायिकता"}}}], "hi/functions/hyperbolic.min": [{"locale": "hi"}, {"key": "cosh", "category": "Hyperbolic", "names": ["cosh"], "mappings": {"default": {"default": "हाइपरबोलिक कोसाइन"}}}, {"key": "coth", "category": "Hyperbolic", "names": ["coth"], "mappings": {"default": {"default": "हाइपरबोलिक कॉट"}}}, {"key": "csch", "category": "Hyperbolic", "names": ["csch"], "mappings": {"default": {"default": "हाइपरबोलिक कोसेक"}}}, {"key": "sech", "category": "Hyperbolic", "names": ["sech"], "mappings": {"default": {"default": "हाइपरबोलिक सेक"}}}, {"key": "sinh", "category": "Hyperbolic", "names": ["sinh"], "mappings": {"default": {"default": "हाइपरबोलिक साइन"}}}, {"key": "tanh", "category": "Hyperbolic", "names": ["tanh"], "mappings": {"default": {"default": "हाइपरबोलिक टेन"}}}, {"key": "arcosh", "category": "Area", "names": ["arcosh", "arccosh"], "mappings": {"default": {"default": "प्रतिलोम हाइपरबोलिक कोसाइन"}}}, {"key": "arcoth", "category": "Area", "names": ["arcoth", "arccoth"], "mappings": {"default": {"default": "प्रतिलोम हाइपरबोलिक कॉट"}}}, {"key": "<PERSON><PERSON>", "category": "Area", "names": ["<PERSON><PERSON>", "arc<PERSON>ch"], "mappings": {"default": {"default": "प्रतिलोम हाइपरबोलिक कोसेक"}}}, {"key": "arsech", "category": "Area", "names": ["arsech", "arcsech"], "mappings": {"default": {"default": "प्रतिलोम हाइपरबोलिक सेक"}}}, {"key": "a<PERSON><PERSON><PERSON>", "category": "Area", "names": ["a<PERSON><PERSON><PERSON>", "arcsinh"], "mappings": {"default": {"default": "प्रतिलोम हाइपरबोलिक साइन"}}}, {"key": "artanh", "category": "Area", "names": ["artanh", "arctanh"], "mappings": {"default": {"default": "प्रतिलोम हाइपरबोलिक टेन"}}}], "hi/functions/trigonometry.min": [{"locale": "hi"}, {"key": "cos", "category": "Trigonometric", "names": ["cos", "cosine"], "mappings": {"default": {"default": "कोस"}}}, {"key": "cot", "category": "Trigonometric", "names": ["cot"], "mappings": {"default": {"default": "कॉट"}}}, {"key": "csc", "category": "Trigonometric", "names": ["csc"], "mappings": {"default": {"default": "कोसेक"}}}, {"key": "sec", "category": "Trigonometric", "names": ["sec"], "mappings": {"default": {"default": "सेक"}}}, {"key": "sin", "category": "Trigonometric", "names": ["sin", "sine"], "mappings": {"default": {"default": "साइन"}}}, {"key": "tan", "category": "Trigonometric", "names": ["tan"], "mappings": {"default": {"default": "टेन"}}}, {"key": "arccos", "category": "Cyclometric", "names": ["arccos"], "mappings": {"default": {"default": "प्रतिलोम कोस"}}}, {"key": "<PERSON><PERSON>", "category": "Cyclometric", "names": ["<PERSON><PERSON>"], "mappings": {"default": {"default": "प्रतिलोम कॉट"}}}, {"key": "arccsc", "category": "Cyclometric", "names": ["arccsc"], "mappings": {"default": {"default": "प्रतिलोम कोसेक"}}}, {"key": "arcsec", "category": "Cyclometric", "names": ["arcsec"], "mappings": {"default": {"default": "प्रतिलोम सेक"}}}, {"key": "arcsin", "category": "Cyclometric", "names": ["arcsin"], "mappings": {"default": {"default": "प्रतिलोम साइन"}}}, {"key": "arctan", "category": "Cyclometric", "names": ["arctan"], "mappings": {"default": {"default": "प्रतिलोम टेन"}}}], "hi/symbols/digits_rest.min": [{"locale": "hi"}, {"category": "No", "mappings": {"default": {"default": "चुकता"}, "mathspeak": {"default": "चुकता"}, "clearspeak": {"default": "चुकता"}}, "key": "00B2"}, {"category": "No", "mappings": {"default": {"default": "घन"}, "mathspeak": {"default": "घन"}, "clearspeak": {"default": "घन"}}, "key": "00B3"}, {"category": "No", "key": "00BC", "mappings": {"default": {"default": "एक चतुर्थांश"}}}, {"category": "No", "key": "00BD", "mappings": {"default": {"default": "एक द्वितीयांश"}}}, {"category": "No", "key": "00BE", "mappings": {"default": {"default": "तीन चतुर्थांश"}}}, {"category": "No", "key": "2150", "mappings": {"default": {"default": "एक सप्तमांश"}}}, {"category": "No", "key": "2151", "mappings": {"default": {"default": "एक नवमांश"}}}, {"category": "No", "key": "2152", "mappings": {"default": {"default": "एक दशांश"}}}, {"category": "No", "key": "2153", "mappings": {"default": {"default": "एक तृतीयांश"}}}, {"category": "No", "key": "2154", "mappings": {"default": {"default": "दो तृतीयांश"}}}, {"category": "No", "key": "2155", "mappings": {"default": {"default": "एक पंचमांश"}}}, {"category": "No", "key": "2156", "mappings": {"default": {"default": "दो पंचमांश"}}}, {"category": "No", "key": "2157", "mappings": {"default": {"default": "तीन पंचमांश"}}}, {"category": "No", "key": "2158", "mappings": {"default": {"default": "<PERSON>ार पंचमा<PERSON>"}}}, {"category": "No", "key": "2159", "mappings": {"default": {"default": "एक षष्टांश"}}}, {"category": "No", "key": "215A", "mappings": {"default": {"default": "पांच षष्टांश"}}}, {"category": "No", "key": "215B", "mappings": {"default": {"default": "एक अष्टांश"}}}, {"category": "No", "key": "215C", "mappings": {"default": {"default": "तीन अष्टांश"}}}, {"category": "No", "key": "215D", "mappings": {"default": {"default": "पांच अष्टांश"}}}, {"category": "No", "key": "215E", "mappings": {"default": {"default": "सात अष्टांश"}}}, {"category": "No", "key": "215F", "mappings": {"default": {"default": "अंश में एक और हर में"}}}, {"category": "No", "key": "2189", "mappings": {"default": {"default": "शून्य तृतीयांश"}}}, {"category": "No", "key": "3248", "mappings": {"default": {"default": "काले वर्ग पर श्वेत वर्तुल में दस"}}}, {"category": "No", "key": "3249", "mappings": {"default": {"default": "काले वर्ग पर श्वेत वर्तुल में बीस"}}}, {"category": "No", "key": "324A", "mappings": {"default": {"default": "काले वर्ग पर श्वेत वर्तुल में तिस"}}}, {"category": "No", "key": "324B", "mappings": {"default": {"default": "काले वर्ग पर श्वेत वर्तुल में चालीस"}}}, {"category": "No", "key": "324C", "mappings": {"default": {"default": "काले वर्ग पर श्वेत वर्तुल में पचास"}}}, {"category": "No", "key": "324D", "mappings": {"default": {"default": "काले वर्ग पर श्वेत वर्तुल में साठ"}}}, {"category": "No", "key": "324E", "mappings": {"default": {"default": "काले वर्ग पर श्वेत वर्तुल में सत्तर"}}}, {"category": "No", "key": "324F", "mappings": {"default": {"default": "काले वर्ग पर श्वेत वर्तुल में अस्सी"}}}], "hi/symbols/greek-rest.min": [{"locale": "hi"}, {"category": "<PERSON>", "key": "0394", "mappings": {"clearspeak": {"default": "त्रिकोण", "TriangleSymbol_Delta": "ग्रीक कैपिटल लेटर डेल्टा"}}}], "hi/symbols/greek-scripts.min": [{"locale": "hi"}, {"category": "Ll", "key": "1D26", "mappings": {"default": {"default": "छोटा केपिटल गामा"}}}, {"category": "Ll", "key": "1D27", "mappings": {"default": {"default": "छोटा केपिटल लैम्ब्डा"}}}, {"category": "Ll", "key": "1D28", "mappings": {"default": {"default": "छोटा केपिटल पाई"}}}, {"category": "Ll", "key": "1D29", "mappings": {"default": {"default": "छोटा केपिटल रो"}}}, {"category": "Ll", "key": "1D2A", "mappings": {"default": {"default": "छोटा केपिटल साई"}}}, {"category": "Lm", "key": "1D5E", "mappings": {"default": {"default": "उर्ध्वाक्षर गामा"}}}, {"category": "Lm", "key": "1D60", "mappings": {"default": {"default": "उर्ध्वाक्षर फाई"}}}, {"category": "Lm", "key": "1D66", "mappings": {"default": {"default": "पादाक्षर बीटा"}}}, {"category": "Lm", "key": "1D67", "mappings": {"default": {"default": "पादाक्षर गामा"}}}, {"category": "Lm", "key": "1D68", "mappings": {"default": {"default": "पादा<PERSON><PERSON><PERSON><PERSON> रो"}}}, {"category": "Lm", "key": "1D69", "mappings": {"default": {"default": "पादाक्षर फाई"}}}, {"category": "Lm", "key": "1D6A", "mappings": {"default": {"default": "पादाक्षर काई"}}}], "hi/symbols/greek-symbols.min": [{"locale": "hi"}, {"category": "Ll", "key": "03D0", "mappings": {"default": {"default": "वक्री बीटा"}}}, {"category": "Ll", "key": "03D7", "mappings": {"default": {"default": "आशुलिपि काई"}}}, {"category": "Sm", "key": "03F6", "mappings": {"default": {"default": "उत्क्रमित एप्सिलोन"}}}, {"category": "<PERSON>", "key": "1D7CA", "mappings": {"default": {"default": "बोल्ड कैपिटल दिगम्मा"}}}, {"category": "Ll", "key": "1D7CB", "mappings": {"default": {"default": "बोल्ड लघु दिगम्मा"}}}], "hi/symbols/hebrew_letters.min": [{"locale": "hi"}, {"category": "Lo", "key": "2135", "mappings": {"default": {"default": "अलेफ प्रतीक"}}}, {"category": "Lo", "key": "2136", "mappings": {"default": {"default": "बेट प्रतीक"}}}, {"category": "Lo", "key": "2137", "mappings": {"default": {"default": "गिमेल प्रतीक"}}}, {"category": "Lo", "key": "2138", "mappings": {"default": {"default": "दलित प्रतीक"}}}], "hi/symbols/latin-lower-double-accent.min": [{"locale": "hi"}, {"category": "Ll", "key": "01D6", "mappings": {"default": {"default": "डायरिसिस और मैक्रोन के साथ यू"}}}, {"category": "Ll", "key": "01D8", "mappings": {"default": {"default": "डायसिस और एक्यूट के साथ लैटिन स्माल लेटर यू"}}}, {"category": "Ll", "key": "01DA", "mappings": {"default": {"default": "डायरिसिस और कैरन के साथ यू"}}}, {"category": "Ll", "key": "01DC", "mappings": {"default": {"default": "डायरिसिस और ग्रेव के साथ लैटिन स्माल लेटर यू"}}}, {"category": "Ll", "key": "01DF", "mappings": {"default": {"default": "डायरिसिस और मैक्रोन के साथ लैटिन स्मॉल लेटर"}}}, {"category": "Ll", "key": "01E1", "mappings": {"default": {"default": "लैटिन छोटा पत्र डॉट एबव और मैक्रॉन के साथ"}}}, {"category": "Ll", "key": "01ED", "mappings": {"default": {"default": "ओगोनक और मैक्रोन के साथ ओ"}}}, {"category": "Ll", "key": "01FB", "mappings": {"default": {"default": "रिंग एबव और एक्यूट के साथ लैटिन स्माल लेटर"}}}, {"category": "Ll", "key": "022B", "mappings": {"default": {"default": "डायरिसिस और मैक्रोन के साथ लैटिन स्मॉल लेटर ओ"}}}, {"category": "Ll", "key": "022D", "mappings": {"default": {"default": "टिल्डे और मैक्रॉन के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "0231", "mappings": {"default": {"default": "लैटिन स्मॉल लेटर ओ विथ डॉट एबव और मैक्रोन"}}}, {"category": "Ll", "key": "1E09", "mappings": {"default": {"default": "सेडिला और एक्यूट के साथ लैटिन स्माल लेटर सी"}}}, {"category": "Ll", "key": "1E15", "mappings": {"default": {"default": "मैक्रॉन और ग्रेव के साथ लैटिन स्माल लेटर ई"}}}, {"category": "Ll", "key": "1E17", "mappings": {"default": {"default": "मैक्रॉन और एक्यूट के साथ लैटिन स्माल लेटर ई"}}}, {"category": "Ll", "key": "1E1D", "mappings": {"default": {"default": "सेडिला और ब्रेव के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "1E2F", "mappings": {"default": {"default": "लैटिन स्माल लेटर I विद डायरेसिस एंड एक्यूट"}}}, {"category": "Ll", "key": "1E39", "mappings": {"default": {"default": "एल नीचे डॉट और मैक्रॉन के साथ"}}}, {"category": "Ll", "key": "1E4D", "mappings": {"default": {"default": "टिल्डे और एक्यूट के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "1E4F", "mappings": {"default": {"default": "टिल्डे और डायरेसिस के साथ लैटिन स्मॉल लेटर ओ"}}}, {"category": "Ll", "key": "1E51", "mappings": {"default": {"default": "मैक्रॉन और ग्रेव के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "1E53", "mappings": {"default": {"default": "मैक्रॉन और एक्यूट के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "1E5D", "mappings": {"default": {"default": "आर नीचे डॉट और मैक्रॉन के साथ"}}}, {"category": "Ll", "key": "1E65", "mappings": {"default": {"default": "लैटिन स्माल लेटर एस के साथ एक्यूट और डॉट एबव"}}}, {"category": "Ll", "key": "1E67", "mappings": {"default": {"default": "कैरन और डॉट एबव के साथ लैटिन स्माल लेटर एस"}}}, {"category": "Ll", "key": "1E69", "mappings": {"default": {"default": "लैटिन छोटा पत्र एस के साथ डॉट नीचे और डॉट के ऊपर"}}}, {"category": "Ll", "key": "1E79", "mappings": {"default": {"default": "टिल्डे और एक्यूट के साथ लैटिन स्माल लेटर यू"}}}, {"category": "Ll", "key": "1E7B", "mappings": {"default": {"default": "मैक्रॉन और डायसिस के साथ यू"}}}, {"category": "Ll", "key": "1EA5", "mappings": {"default": {"default": "सर्कमफ्लेक्स और एक्यूट के साथ लैटिन स्माल लेटर"}}}, {"category": "Ll", "key": "1EA7", "mappings": {"default": {"default": "सर्कमफ्लेक्स और ग्रेव के साथ लैटिन स्मॉल लेटर"}}}, {"category": "Ll", "key": "1EA9", "mappings": {"default": {"default": "सर्कमफ्लेक्स और हुक एबव के साथ लैटिन स्माल लेटर"}}}, {"category": "Ll", "key": "1EAB", "mappings": {"default": {"default": "सर्कमफ्लेक्स और टिल्डे के साथ लैटिन छोटा पत्र"}}}, {"category": "Ll", "key": "1EAD", "mappings": {"default": {"default": "सर्कमफ्लेक्स और डॉट नीचे के साथ लैटिन छोटा पत्र"}}}, {"category": "Ll", "key": "1EAF", "mappings": {"default": {"default": "ब्रेव और एक्यूट के साथ लैटिन स्माल लेटर"}}}, {"category": "Ll", "key": "1EB1", "mappings": {"default": {"default": "ब्रेव और ग्रेव के साथ लैटिन स्माल लेटर"}}}, {"category": "Ll", "key": "1EB3", "mappings": {"default": {"default": "ब्रेव और हुक एबव के साथ लैटिन स्माल लेटर"}}}, {"category": "Ll", "key": "1EB5", "mappings": {"default": {"default": "ब्रेव और टिल्ड के साथ लैटिन स्माल लेटर"}}}, {"category": "Ll", "key": "1EB7", "mappings": {"default": {"default": "ब्रेव और डॉट नीचे के साथ लैटिन छोटा पत्र"}}}, {"category": "Ll", "key": "1EBF", "mappings": {"default": {"default": "सर्कमफ्लेक्स और एक्यूट के साथ लैटिन स्माल लेटर ई"}}}, {"category": "Ll", "key": "1EC1", "mappings": {"default": {"default": "सर्कमफ्लेक्स और ग्रेव के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "1EC3", "mappings": {"default": {"default": "सर्कमफ्लेक्स और हुक एबव के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "1EC5", "mappings": {"default": {"default": "सर्कमफ्लेक्स और टिल्डे के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "1EC7", "mappings": {"default": {"default": "सर्कमफ्लेक्स और डॉट नीचे के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "1ED1", "mappings": {"default": {"default": "सर्कमफ्लेक्स और एक्यूट के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "1ED3", "mappings": {"default": {"default": "सर्कमफ्लेक्स और ग्रेव के साथ लैटिन स्मॉल लेटर ओ"}}}, {"category": "Ll", "key": "1ED5", "mappings": {"default": {"default": "सर्कमफ्लेक्स और हुक के ऊपर लैटिन छोटा पत्र ओ"}}}, {"category": "Ll", "key": "1ED7", "mappings": {"default": {"default": "सर्कमफ्लेक्स और टिल्डे के साथ लैटिन छोटा पत्र ओ"}}}, {"category": "Ll", "key": "1ED9", "mappings": {"default": {"default": "सर्कमफ्लेक्स और डॉट नीचे के साथ लैटिन छोटा पत्र ओ"}}}, {"category": "Ll", "key": "1EDB", "mappings": {"default": {"default": "हॉर्न और एक्यूट के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "1EDD", "mappings": {"default": {"default": "हॉर्न और ग्रेव के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "1EDF", "mappings": {"default": {"default": "सींग और हुक के साथ लैटिन छोटा पत्र ओ"}}}, {"category": "Ll", "key": "1EE1", "mappings": {"default": {"default": "हॉर्न और टिल्ड के साथ लैटिन छोटा पत्र ओ"}}}, {"category": "Ll", "key": "1EE3", "mappings": {"default": {"default": "लैटिन हॉर्न लेटर ओ विथ हॉर्न एंड डॉट नीचे"}}}, {"category": "Ll", "key": "1EE9", "mappings": {"default": {"default": "हॉर्न और एक्यूट के साथ लैटिन स्माल लेटर यू"}}}, {"category": "Ll", "key": "1EEB", "mappings": {"default": {"default": "हॉर्न और ग्रेव के साथ लैटिन स्माल लेटर यू"}}}, {"category": "Ll", "key": "1EED", "mappings": {"default": {"default": "हॉर्न और हुक के साथ लैटिन छोटा पत्र यू"}}}, {"category": "Ll", "key": "1EEF", "mappings": {"default": {"default": "हॉर्न और टिल्ड के साथ लैटिन छोटा पत्र यू"}}}, {"category": "Ll", "key": "1EF1", "mappings": {"default": {"default": "हॉर्न और डॉट नीचे के साथ लैटिन छोटा पत्र यू"}}}], "hi/symbols/latin-lower-phonetic.min": [{"locale": "hi"}, {"category": "Ll", "key": "00F8", "mappings": {"default": {"default": "स्ट्रोक के साथ लैटिन छोटा पत्र ओ"}}}, {"category": "Ll", "key": "0111", "mappings": {"default": {"default": "स्ट्रोक के साथ लैटिन छोटा पत्र डी"}}}, {"category": "Ll", "key": "0127", "mappings": {"default": {"default": "स्ट्रोक के साथ लैटिन छोटा पत्र एच"}}}, {"category": "Ll", "key": "0142", "mappings": {"default": {"default": "लैटिन छोटा पत्र एल स्ट्रोक के साथ"}}}, {"category": "Ll", "key": "0167", "mappings": {"default": {"default": "स्ट्रोक के साथ लैटिन छोटा पत्र टी"}}}, {"category": "Ll", "key": "0180", "mappings": {"default": {"default": "स्ट्रोक के साथ लैटिन छोटा पत्र बी"}}}, {"category": "Ll", "key": "019B", "mappings": {"default": {"default": "स्ट्रोक के साथ लैटिन छोटा पत्र लैम्बडा"}}}, {"category": "Ll", "key": "01B6", "mappings": {"default": {"default": "स्ट्रोक के साथ लैटिन छोटा पत्र जेड"}}}, {"category": "Ll", "key": "01BE", "mappings": {"default": {"default": "लैटिन लेटर इनवर्टेड ग्लोटल स्टॉप विथ स्ट्रोक"}}}, {"category": "Ll", "key": "01E5", "mappings": {"default": {"default": "स्ट्रोक के साथ लैटिन छोटा पत्र जी"}}}, {"category": "Ll", "key": "01FF", "mappings": {"default": {"default": "स्ट्रॉक और एक्यूट के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "023C", "mappings": {"default": {"default": "स्ट्रोक के साथ लैटिन छोटा पत्र सी"}}}, {"category": "Ll", "key": "0247", "mappings": {"default": {"default": "स्ट्रोक के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "0249", "mappings": {"default": {"default": "स्ट्रोक के साथ लैटिन छोटा पत्र जे"}}}, {"category": "Ll", "key": "024D", "mappings": {"default": {"default": "लैटिन छोटे पत्र आर स्ट्रोक के साथ"}}}, {"category": "Ll", "key": "024F", "mappings": {"default": {"default": "लैटिन छोटा पत्र वाई स्ट्रोक के साथ"}}}, {"category": "Ll", "key": "025F", "mappings": {"default": {"default": "स्ट्रोक के साथ लैटिन छोटा पत्र डॉटलेस जे"}}}, {"category": "Ll", "key": "0268", "mappings": {"default": {"default": "लैटिन छोटा पत्र मैं स्ट्रोक के साथ"}}}, {"category": "Ll", "key": "0284", "mappings": {"default": {"default": "स्ट्रोक और हुक के साथ लैटिन छोटा पत्र डॉटलेस जे"}}}, {"category": "Ll", "key": "02A1", "mappings": {"default": {"default": "लैटिन लेटर ग्लोटल स्टॉप विथ स्ट्रोक"}}}, {"category": "Ll", "key": "02A2", "mappings": {"default": {"default": "लैटिन पत्र स्ट्रोक के साथ ग्लोटल स्टॉप को उलट देता है"}}}, {"category": "Ll", "key": "1D13", "mappings": {"default": {"default": "लैटिन छोटे पत्र बग़ल में ओ स्ट्रोक के साथ"}}}, {"category": "Ll", "key": "1D7C", "mappings": {"default": {"default": "लैटिन छोटे पत्र Iota स्ट्रोक के साथ"}}}, {"category": "Ll", "key": "1D7D", "mappings": {"default": {"default": "स्ट्रोक के साथ लैटिन छोटा पत्र पी"}}}, {"category": "Ll", "key": "1D7F", "mappings": {"default": {"default": "उपसिलोन स्ट्रोक के साथ"}}}, {"category": "Ll", "key": "1E9C", "mappings": {"default": {"default": "विकर्ण स्ट्रोक के साथ लैटिन छोटा पत्र लंबा एस"}}}, {"category": "Ll", "key": "1E9D", "mappings": {"default": {"default": "उच्च स्ट्रोक के साथ लैटिन छोटा पत्र लंबा एस"}}}, {"category": "Ll", "key": "018D", "mappings": {"default": {"default": "लैटिन स्माल लेटर टर्न डेल्टा"}}}, {"category": "Ll", "key": "1E9B", "mappings": {"default": {"default": "लैटिन स्मॉल लेटर लॉन्ग एस विद डॉट एबव"}}}, {"category": "Ll", "key": "1E9F", "mappings": {"default": {"default": "डेल्टा"}}}, {"category": "Ll", "key": "0138", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"category": "Ll", "key": "017F", "mappings": {"default": {"default": "लैटिन स्माल लेटर लॉन्ग एस"}}}, {"category": "Ll", "key": "0183", "mappings": {"default": {"default": "टोपबार के साथ लैटिन छोटा पत्र बी"}}}, {"category": "Ll", "key": "0185", "mappings": {"default": {"default": "लेटिन स्मॉल लेटर टोन सिक्स"}}}, {"category": "Ll", "key": "0188", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र सी"}}}, {"category": "Ll", "key": "018C", "mappings": {"default": {"default": "टोपबार के साथ लैटिन छोटा पत्र डी"}}}, {"category": "Ll", "key": "0192", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र एफ"}}}, {"category": "Ll", "key": "0195", "mappings": {"default": {"default": "Hv"}}}, {"category": "Ll", "key": "0199", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र के"}}}, {"category": "Ll", "key": "019A", "mappings": {"default": {"default": "बार के साथ लैटिन छोटा पत्र एल"}}}, {"category": "Ll", "key": "019E", "mappings": {"default": {"default": "लैटिन राइट लेटर एन लॉन्ग राइट लेग के साथ"}}}, {"category": "Ll", "key": "01A1", "mappings": {"default": {"default": "हॉर्न के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "01A3", "mappings": {"default": {"default": "ओई"}}}, {"category": "Ll", "key": "01A5", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र पी"}}}, {"category": "Ll", "key": "01A8", "mappings": {"default": {"default": "टोन दो"}}}, {"category": "Ll", "key": "01AA", "mappings": {"default": {"default": "लैटिन पत्र ईश लूप को उलट दिया"}}}, {"category": "Ll", "key": "01AB", "mappings": {"default": {"default": "पाताल हुक के साथ लैटिन छोटा पत्र टी"}}}, {"category": "Ll", "key": "01AD", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र टी"}}}, {"category": "Ll", "key": "01B0", "mappings": {"default": {"default": "हॉर्न के साथ लैटिन छोटा पत्र यू"}}}, {"category": "Ll", "key": "01B4", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र वाई"}}}, {"category": "Ll", "key": "01B9", "mappings": {"default": {"default": "Ezh उलट"}}}, {"category": "Ll", "key": "01BA", "mappings": {"default": {"default": "पूंछ के साथ लैटिन छोटा पत्र Ezh"}}}, {"category": "Ll", "key": "01BD", "mappings": {"default": {"default": "लेटिन स्मॉल लेटर टोन फाइव"}}}, {"category": "Ll", "key": "01BF", "mappings": {"default": {"default": "लैटिन पत्र Wynn"}}}, {"category": "Ll", "key": "01C6", "mappings": {"default": {"default": "कैरन के साथ लैटिन स्माल लेटर डीज़"}}}, {"category": "Ll", "key": "01C9", "mappings": {"default": {"default": "एल.जे."}}}, {"category": "Ll", "key": "01CC", "mappings": {"default": {"default": "Nj"}}}, {"category": "Ll", "key": "01E3", "mappings": {"default": {"default": "मैक्रोन के साथ लैटिन छोटा पत्र ए"}}}, {"category": "Ll", "key": "01EF", "mappings": {"default": {"default": "कैरन के साथ लैटिन छोटा पत्र Ezh"}}}, {"category": "Ll", "key": "01F3", "mappings": {"default": {"default": "Dz"}}}, {"category": "Ll", "key": "021D", "mappings": {"default": {"default": "योग"}}}, {"category": "Ll", "key": "026E", "mappings": {"default": {"default": "लैटिन छोटा पत्र लेझ"}}}, {"category": "Ll", "key": "0292", "mappings": {"default": {"default": "Ezh"}}}, {"category": "Ll", "key": "0293", "mappings": {"default": {"default": "कर्ल के साथ लैटिन छोटा पत्र Ezh"}}}, {"category": "Ll", "key": "02A4", "mappings": {"default": {"default": "देहज डिग्राफ"}}}, {"category": "Ll", "key": "01DD", "mappings": {"default": {"default": "ई बदल गया"}}}, {"category": "Ll", "key": "01FD", "mappings": {"default": {"default": "एक्यूट के साथ लैटिन स्माल लेटर Ae"}}}, {"category": "Ll", "key": "0221", "mappings": {"default": {"default": "कर्ल के साथ लैटिन छोटा पत्र डी"}}}, {"category": "Ll", "key": "0223", "mappings": {"default": {"default": "Ou"}}}, {"category": "Ll", "key": "0225", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र जेड"}}}, {"category": "Ll", "key": "0234", "mappings": {"default": {"default": "कर्ल के साथ लैटिन छोटा पत्र एल"}}}, {"category": "Ll", "key": "0235", "mappings": {"default": {"default": "कर्ल के साथ लैटिन छोटा पत्र एन"}}}, {"category": "Ll", "key": "0236", "mappings": {"default": {"default": "कर्ल के साथ लैटिन छोटा पत्र टी"}}}, {"category": "Ll", "key": "0238", "mappings": {"default": {"default": "डीबी डिग्राफ"}}}, {"category": "Ll", "key": "0239", "mappings": {"default": {"default": "Qp Digraph"}}}, {"category": "Ll", "key": "023F", "mappings": {"default": {"default": "स्वैश टेल के साथ लैटिन स्माल लेटर S"}}}, {"category": "Ll", "key": "0240", "mappings": {"default": {"default": "स्वैश टेल के साथ लैटिन स्माल लेटर जेड"}}}, {"category": "Ll", "key": "0242", "mappings": {"default": {"default": "लैटिन स्माल लेटर ग्लोटल स्टॉप"}}}, {"category": "Ll", "key": "024B", "mappings": {"default": {"default": "हुक टेल के साथ लैटिन छोटा पत्र क्यू"}}}, {"category": "Ll", "key": "0250", "mappings": {"default": {"default": "लैटिन छोटा पत्र एक बदल गया"}}}, {"category": "Ll", "key": "0251", "mappings": {"default": {"default": "अल्फा"}}}, {"category": "Ll", "key": "0252", "mappings": {"default": {"default": "बारी बारी से अल्फा"}}}, {"category": "Ll", "key": "0253", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र बी"}}}, {"category": "Ll", "key": "0254", "mappings": {"default": {"default": "ओपन ओ"}}}, {"category": "Ll", "key": "0255", "mappings": {"default": {"default": "कर्ल के साथ लैटिन छोटा पत्र सी"}}}, {"category": "Ll", "key": "0256", "mappings": {"default": {"default": "पूंछ के साथ लैटिन छोटा पत्र डी"}}}, {"category": "Ll", "key": "0257", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र डी"}}}, {"category": "Ll", "key": "0258", "mappings": {"default": {"default": "उलट ई"}}}, {"category": "Ll", "key": "0259", "mappings": {"default": {"default": "श्व"}}}, {"category": "Ll", "key": "025A", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र श्वा"}}}, {"category": "Ll", "key": "025B", "mappings": {"default": {"default": "ओपन ई"}}}, {"category": "Ll", "key": "025C", "mappings": {"default": {"default": "लैटिन छोटा पत्र उलटा खुला ई"}}}, {"category": "Ll", "key": "025D", "mappings": {"default": {"default": "लैटिन छोटा पत्र हुक के साथ खुला ई उलट"}}}, {"category": "Ll", "key": "025E", "mappings": {"default": {"default": "लैटिन छोटा पत्र बंद उलट खुला ई"}}}, {"category": "Ll", "key": "0260", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र जी"}}}, {"category": "Ll", "key": "0261", "mappings": {"default": {"default": "स्क्रिप्ट जी"}}}, {"category": "Ll", "key": "0263", "mappings": {"default": {"default": "गामा"}}}, {"category": "Ll", "key": "0264", "mappings": {"default": {"default": "लैटिन छोटा पत्र राम सींग"}}}, {"category": "Ll", "key": "0265", "mappings": {"default": {"default": "लैटिन छोटा पत्र एच बदल गया"}}}, {"category": "Ll", "key": "0266", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र एच"}}}, {"category": "Ll", "key": "0267", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र हेंग"}}}, {"category": "Ll", "key": "0269", "mappings": {"default": {"default": "Iota"}}}, {"category": "Ll", "key": "026B", "mappings": {"default": {"default": "मध्य टिल्डे के साथ एल"}}}, {"category": "Ll", "key": "026C", "mappings": {"default": {"default": "बेल्ट के साथ लैटिन छोटा पत्र एल"}}}, {"category": "Ll", "key": "026D", "mappings": {"default": {"default": "रेट्रोफ्लेक्स हुक के साथ लैटिन छोटा पत्र एल"}}}, {"category": "Ll", "key": "026F", "mappings": {"default": {"default": "लेटिन स्माल लेटर टर्न एम"}}}, {"category": "Ll", "key": "0270", "mappings": {"default": {"default": "लेटिन स्माल लेटर टर्न एम विथ लॉन्ग लेग"}}}, {"category": "Ll", "key": "0271", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र एम"}}}, {"category": "Ll", "key": "0272", "mappings": {"default": {"default": "लेफ्ट हुक के साथ लैटिन स्माल लेटर एन"}}}, {"category": "Ll", "key": "0273", "mappings": {"default": {"default": "रेट्रोफ्लेक्स हुक के साथ लैटिन छोटा पत्र एन"}}}, {"category": "Ll", "key": "0275", "mappings": {"default": {"default": "वर्जित हे"}}}, {"category": "Ll", "key": "0277", "mappings": {"default": {"default": "लैटिन छोटा पत्र बंद ओमेगा"}}}, {"category": "Ll", "key": "0278", "mappings": {"default": {"default": "फी"}}}, {"category": "Ll", "key": "0279", "mappings": {"default": {"default": "लैटिन छोटा पत्र आर बदल गया"}}}, {"category": "Ll", "key": "027A", "mappings": {"default": {"default": "लैटिन स्माल लेटर टर्न आर विथ लॉन्ग लेग"}}}, {"category": "Ll", "key": "027B", "mappings": {"default": {"default": "लैटिन छोटा पत्र हुक के साथ आर बदल गया"}}}, {"category": "Ll", "key": "027C", "mappings": {"default": {"default": "लंबे पैर के साथ लैटिन छोटा पत्र आर"}}}, {"category": "Ll", "key": "027D", "mappings": {"default": {"default": "पूंछ के साथ लैटिन छोटा पत्र आर"}}}, {"category": "Ll", "key": "027E", "mappings": {"default": {"default": "फिशहुक के साथ लैटिन स्माल लेटर आर"}}}, {"category": "Ll", "key": "027F", "mappings": {"default": {"default": "फिशहुक के साथ लैटिन स्माल लेटर उलट आर"}}}, {"category": "Ll", "key": "0282", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र एस"}}}, {"category": "Ll", "key": "0283", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"category": "Ll", "key": "0285", "mappings": {"default": {"default": "स्क्वाट ने ईश को उलट दिया"}}}, {"category": "Ll", "key": "0286", "mappings": {"default": {"default": "कर्ल के साथ लैटिन छोटा पत्र ईश"}}}, {"category": "Ll", "key": "0287", "mappings": {"default": {"default": "लेटिन स्माल लेटर टर्न टी"}}}, {"category": "Ll", "key": "0288", "mappings": {"default": {"default": "रेट्रोफ्लेक्स हुक के साथ लैटिन छोटा पत्र टी"}}}, {"category": "Ll", "key": "0289", "mappings": {"default": {"default": "यू बार"}}}, {"category": "Ll", "key": "028A", "mappings": {"default": {"default": "उपसिलन"}}}, {"category": "Ll", "key": "028B", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र वी"}}}, {"category": "Ll", "key": "028C", "mappings": {"default": {"default": "लेटिन स्मॉल लेटर टर्न वी"}}}, {"category": "Ll", "key": "028D", "mappings": {"default": {"default": "लैटिन छोटा पत्र डब्ल्यू बदल गया"}}}, {"category": "Ll", "key": "028E", "mappings": {"default": {"default": "लेटिन स्माल लेटर टर्न वाई"}}}, {"category": "Ll", "key": "0290", "mappings": {"default": {"default": "रेट्रोफ्लेक्स हुक के साथ लैटिन छोटा पत्र जेड"}}}, {"category": "Ll", "key": "0291", "mappings": {"default": {"default": "कर्ल के साथ लैटिन छोटा पत्र जेड"}}}, {"category": "Ll", "key": "0295", "mappings": {"default": {"default": "लैटिन पत्र Pharyngeal Voiced Fricative"}}}, {"category": "Ll", "key": "0296", "mappings": {"default": {"default": "लैटिन पत्र उल्टे ग्लोटल स्टॉप"}}}, {"category": "Ll", "key": "0297", "mappings": {"default": {"default": "लैटिन पत्र बढ़ा हुआ सी"}}}, {"category": "Ll", "key": "0298", "mappings": {"default": {"default": "लैटिन पत्र बिलाबियल क्लिक"}}}, {"category": "Ll", "key": "029A", "mappings": {"default": {"default": "लैटिन छोटा पत्र बंद ई"}}}, {"category": "Ll", "key": "029E", "mappings": {"default": {"default": "लेटिन स्मॉल लेटर टर्नड K"}}}, {"category": "Ll", "key": "02A0", "mappings": {"default": {"default": "हुक के साथ लैटिन छोटा पत्र क्यू"}}}, {"category": "Ll", "key": "02A3", "mappings": {"default": {"default": "डीजी डिग्राफ"}}}, {"category": "Ll", "key": "02A5", "mappings": {"default": {"default": "कर्ल के साथ लैटिन छोटा पत्र डीजी डिग्राफ"}}}, {"category": "Ll", "key": "02A6", "mappings": {"default": {"default": "<PERSON>s <PERSON>"}}}, {"category": "Ll", "key": "02A7", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"category": "Ll", "key": "02A8", "mappings": {"default": {"default": "कर्ल के साथ टीसी Digraph"}}}, {"category": "Ll", "key": "02A9", "mappings": {"default": {"default": "फेंग डिग्राफ"}}}, {"category": "Ll", "key": "02AA", "mappings": {"default": {"default": "एलएस डिग्राफ"}}}, {"category": "Ll", "key": "02AB", "mappings": {"default": {"default": "एल.जे. डिग्राफ"}}}, {"category": "Ll", "key": "02AC", "mappings": {"default": {"default": "लैटिन पत्र बिलाबियल पर्क्युसिव"}}}, {"category": "Ll", "key": "02AD", "mappings": {"default": {"default": "लैटिन पत्र द्विदिश टक्कर"}}}, {"category": "Ll", "key": "02AE", "mappings": {"default": {"default": "फिशहुक के साथ लैटिन स्माल लेटर टर्न एच"}}}, {"category": "Ll", "key": "02AF", "mappings": {"default": {"default": "फिशहुक और टेल के साथ लैटिन स्माल लेटर टर्न एच"}}}, {"category": "Ll", "key": "1D02", "mappings": {"default": {"default": "लेटिन स्माल लेटर टर्न ऐ"}}}, {"category": "Ll", "key": "1D08", "mappings": {"default": {"default": "लैटिन छोटा पत्र खुला ई"}}}, {"category": "Ll", "key": "1D09", "mappings": {"default": {"default": "लेटिन स्मॉल लेटर आई"}}}, {"category": "Ll", "key": "1D11", "mappings": {"default": {"default": "लेटिन स्मॉल लेटर साइडवेज ओ"}}}, {"category": "Ll", "key": "1D12", "mappings": {"default": {"default": "लैटिन छोटे पत्र बग़ल में ओपन ओ"}}}, {"category": "Ll", "key": "1D14", "mappings": {"default": {"default": "<PERSON>e बदल गया"}}}, {"category": "Ll", "key": "1D16", "mappings": {"default": {"default": "लैटिन छोटा पत्र शीर्ष आधा हे"}}}, {"category": "Ll", "key": "1D17", "mappings": {"default": {"default": "लेटिन स्मॉल लेटर बॉटम हाफ ओ"}}}, {"category": "Ll", "key": "1D1D", "mappings": {"default": {"default": "लैटिन छोटा पत्र बग़ल में यू"}}}, {"category": "Ll", "key": "1D1E", "mappings": {"default": {"default": "लैटिन छोटे पत्र बग़ल में यू का आकार बदल गया"}}}, {"category": "Ll", "key": "1D1F", "mappings": {"default": {"default": "लैटिन छोटे पत्र बग़ल में एम बदल गया"}}}, {"category": "Ll", "key": "1D24", "mappings": {"default": {"default": "लैटिन पत्र वायर्ड लेरिंजियल स्पिरंट"}}}, {"category": "Ll", "key": "1D25", "mappings": {"default": {"default": "लैटिन पत्र ऐन"}}}, {"category": "Ll", "key": "1D6B", "mappings": {"default": {"default": "यू"}}}, {"category": "Ll", "key": "1D6C", "mappings": {"default": {"default": "मध्य टिल्डे के साथ बी"}}}, {"category": "Ll", "key": "1D6D", "mappings": {"default": {"default": "मध्य टिल्डे के साथ डी"}}}, {"category": "Ll", "key": "1D6E", "mappings": {"default": {"default": "मध्य टिल्डे के साथ एफ"}}}, {"category": "Ll", "key": "1D6F", "mappings": {"default": {"default": "मध्य टिल्डे के साथ एम"}}}, {"category": "Ll", "key": "1D70", "mappings": {"default": {"default": "मध्य टिल्डे के साथ एन"}}}, {"category": "Ll", "key": "1D71", "mappings": {"default": {"default": "मध्य टिल्डे के साथ लैटिन छोटा पत्र पी"}}}, {"category": "Ll", "key": "1D72", "mappings": {"default": {"default": "मध्य टिल्डे के साथ आर"}}}, {"category": "Ll", "key": "1D73", "mappings": {"default": {"default": "फिशहुक और मिडिल टिल्ड के साथ लैटिन स्माल लेटर आर"}}}, {"category": "Ll", "key": "1D74", "mappings": {"default": {"default": "मध्य टिल्डे के साथ एस"}}}, {"category": "Ll", "key": "1D75", "mappings": {"default": {"default": "मध्य टिल्डे के साथ टी"}}}, {"category": "Ll", "key": "1D76", "mappings": {"default": {"default": "मध्य टिल्डे के साथ लैटिन छोटा पत्र जेड"}}}, {"category": "Ll", "key": "1D77", "mappings": {"default": {"default": "लेटिन स्मॉल लेटर टर्न जी"}}}, {"category": "Ll", "key": "1D79", "mappings": {"default": {"default": "लैटिन छोटा पत्र इंसुलर जी"}}}, {"category": "Ll", "key": "1D7A", "mappings": {"default": {"default": "स्ट्राइकथ्रू के साथ लैटिन छोटा पत्र गु"}}}, {"category": "Ll", "key": "1D80", "mappings": {"default": {"default": "पाताल हुक के साथ लैटिन छोटा पत्र बी"}}}, {"category": "Ll", "key": "1D81", "mappings": {"default": {"default": "पाताल हुक के साथ लैटिन छोटा पत्र डी"}}}, {"category": "Ll", "key": "1D82", "mappings": {"default": {"default": "पाताल हुक के साथ लैटिन छोटा पत्र एफ"}}}, {"category": "Ll", "key": "1D83", "mappings": {"default": {"default": "पाताल हुक के साथ लैटिन छोटा पत्र जी"}}}, {"category": "Ll", "key": "1D84", "mappings": {"default": {"default": "पाताल हुक के साथ लैटिन छोटा पत्र के"}}}, {"category": "Ll", "key": "1D85", "mappings": {"default": {"default": "पाताल हुक के साथ लैटिन छोटा पत्र एल"}}}, {"category": "Ll", "key": "1D86", "mappings": {"default": {"default": "पाताल हुक के साथ लैटिन छोटा पत्र एम"}}}, {"category": "Ll", "key": "1D87", "mappings": {"default": {"default": "पाताल हुक के साथ लैटिन छोटा पत्र एन"}}}, {"category": "Ll", "key": "1D88", "mappings": {"default": {"default": "पाताल हुक के साथ लैटिन छोटा पत्र पी"}}}, {"category": "Ll", "key": "1D89", "mappings": {"default": {"default": "पाताल हुक के साथ आर"}}}, {"category": "Ll", "key": "1D8A", "mappings": {"default": {"default": "पाताल हुक के साथ लैटिन छोटा पत्र एस"}}}, {"category": "Ll", "key": "1D8B", "mappings": {"default": {"default": "पाताल हुक के साथ लैटिन स्माल लेटर Esh"}}}, {"category": "Ll", "key": "1D8C", "mappings": {"default": {"default": "पाताल हुक के साथ लैटिन छोटा पत्र वी"}}}, {"category": "Ll", "key": "1D8D", "mappings": {"default": {"default": "पाताल हुक के साथ लैटिन छोटा पत्र एक्स"}}}, {"category": "Ll", "key": "1D8E", "mappings": {"default": {"default": "पाताल हुक के साथ लैटिन छोटा पत्र जेड"}}}, {"category": "Ll", "key": "1D8F", "mappings": {"default": {"default": "रेट्रोफ्लेक्स हुक के साथ लैटिन छोटा पत्र"}}}, {"category": "Ll", "key": "1D90", "mappings": {"default": {"default": "रेट्रोफ्लेक्स हुक के साथ लैटिन छोटा पत्र अल्फा"}}}, {"category": "Ll", "key": "1D91", "mappings": {"default": {"default": "हुक और पूंछ के साथ लैटिन छोटा पत्र डी"}}}, {"category": "Ll", "key": "1D92", "mappings": {"default": {"default": "रेट्रोफ्लेक्स हुक के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "1D93", "mappings": {"default": {"default": "रेट्रोफ्लेक्स हुक के साथ लैटिन छोटा पत्र ओपन ई"}}}, {"category": "Ll", "key": "1D94", "mappings": {"default": {"default": "लैटिन छोटा पत्र रेट्रोफ़्लेक्स हुक के साथ खुला ई उलट"}}}, {"category": "Ll", "key": "1D95", "mappings": {"default": {"default": "रेट्रोफ्लेक्स हुक के साथ लैटिन छोटा पत्र श्वा"}}}, {"category": "Ll", "key": "1D96", "mappings": {"default": {"default": "लैटिन छोटा पत्र I जिसमें रेट्रोफ्लेक्स हुक है"}}}, {"category": "Ll", "key": "1D97", "mappings": {"default": {"default": "रिट्रोफ्लेक्स हुक के साथ लैटिन छोटा पत्र ओपन ओ"}}}, {"category": "Ll", "key": "1D98", "mappings": {"default": {"default": "रेट्रोफ्लेक्स हुक के साथ लैटिन छोटा पत्र ईश"}}}, {"category": "Ll", "key": "1D99", "mappings": {"default": {"default": "रेट्रोफ्लेक्स हुक के साथ लैटिन छोटा पत्र यू"}}}, {"category": "Ll", "key": "1D9A", "mappings": {"default": {"default": "रेट्रोफ्लेक्स हुक के साथ लैटिन छोटा पत्र ईज़"}}}, {"category": "Ll", "key": "0149", "mappings": {"default": {"default": "apostrophe द्वारा लैटिन छोटे पत्र एन पूर्ववर्ती"}}}, {"category": "Ll", "key": "014B", "mappings": {"default": {"default": "लैटिन छोटा पत्र इंजी"}}}], "hi/symbols/latin-lower-single-accent.min": [{"locale": "hi"}, {"category": "Ll", "key": "00E0", "mappings": {"default": {"default": "ग्रेव के साथ लैटिन स्माल लेटर"}}}, {"category": "Ll", "key": "00E1", "mappings": {"default": {"default": "एक्यूट के साथ लैटिन स्माल लेटर"}}}, {"category": "Ll", "key": "00E2", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र"}}}, {"category": "Ll", "key": "00E3", "mappings": {"default": {"default": "टिल्डे के साथ लैटिन छोटा पत्र"}}}, {"category": "Ll", "key": "00E4", "mappings": {"default": {"default": "डायरिसिस के साथ लैटिन स्माल लेटर"}}}, {"category": "Ll", "key": "00E5", "mappings": {"default": {"default": "लैटिन स्मॉल लेटर विथ रिंग एबव"}}}, {"category": "Ll", "key": "00E7", "mappings": {"default": {"default": "सेडिला के साथ लैटिन छोटा पत्र सी"}}}, {"category": "Ll", "key": "00E8", "mappings": {"default": {"default": "ग्रेव के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "00E9", "mappings": {"default": {"default": "एक्यूट के साथ लैटिन स्माल लेटर ई"}}}, {"category": "Ll", "key": "00EA", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "00EB", "mappings": {"default": {"default": "डायसिस के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "00EC", "mappings": {"default": {"default": "लेटिन स्माल लेटर I विद ग्रेव"}}}, {"category": "Ll", "key": "00ED", "mappings": {"default": {"default": "एक्यूट के साथ लैटिन स्माल लेटर I"}}}, {"category": "Ll", "key": "00EE", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ I"}}}, {"category": "Ll", "key": "00EF", "mappings": {"default": {"default": "डायरिसिस के साथ लैटिन स्माल लेटर I"}}}, {"category": "Ll", "key": "00F1", "mappings": {"default": {"default": "टिल्डे के साथ एन"}}}, {"category": "Ll", "key": "00F2", "mappings": {"default": {"default": "ग्रेव के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "00F3", "mappings": {"default": {"default": "एक्यूट के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "00F4", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र ओ"}}}, {"category": "Ll", "key": "00F5", "mappings": {"default": {"default": "टिल्डे के साथ लैटिन छोटा पत्र ओ"}}}, {"category": "Ll", "key": "00F6", "mappings": {"default": {"default": "डायसिस के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "00F9", "mappings": {"default": {"default": "ग्रेव के साथ लैटिन छोटा पत्र यू"}}}, {"category": "Ll", "key": "00FA", "mappings": {"default": {"default": "एक्यूट के साथ लैटिन स्माल लेटर यू"}}}, {"category": "Ll", "key": "00FB", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र यू"}}}, {"category": "Ll", "key": "00FC", "mappings": {"default": {"default": "दो डॉट्स के साथ यू"}}}, {"category": "Ll", "key": "00FD", "mappings": {"default": {"default": "एक्यूट के साथ लैटिन स्माल लेटर वाई"}}}, {"category": "Ll", "key": "00FF", "mappings": {"default": {"default": "डायसिस के साथ वाई"}}}, {"category": "Ll", "key": "0101", "mappings": {"default": {"default": "मैक्रॉन के साथ लैटिन स्माल लेटर"}}}, {"category": "Ll", "key": "0103", "mappings": {"default": {"default": "ब्रेव के साथ लैटिन स्माल लेटर"}}}, {"category": "Ll", "key": "0105", "mappings": {"default": {"default": "ओगोनक के साथ लैटिन छोटा पत्र"}}}, {"category": "Ll", "key": "0107", "mappings": {"default": {"default": "एक्यूट के साथ लैटिन स्माल लेटर सी"}}}, {"category": "Ll", "key": "0109", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र सी"}}}, {"category": "Ll", "key": "010B", "mappings": {"default": {"default": "सी के साथ डॉट एबव"}}}, {"category": "Ll", "key": "010D", "mappings": {"default": {"default": "कैरन के साथ लैटिन छोटा पत्र सी"}}}, {"category": "Ll", "key": "010F", "mappings": {"default": {"default": "कैरन के साथ लैटिन छोटा पत्र डी"}}}, {"category": "Ll", "key": "0113", "mappings": {"default": {"default": "मैक्रॉन के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "0115", "mappings": {"default": {"default": "ब्रेव के साथ लैटिन स्माल लेटर ई"}}}, {"category": "Ll", "key": "0117", "mappings": {"default": {"default": "लैटिन छोटा पत्र ई डॉट के साथ ऊपर"}}}, {"category": "Ll", "key": "0119", "mappings": {"default": {"default": "ओगोनक के साथ ई"}}}, {"category": "Ll", "key": "011B", "mappings": {"default": {"default": "कैरन के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "011D", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र जी"}}}, {"category": "Ll", "key": "011F", "mappings": {"default": {"default": "ब्रेव के साथ लैटिन छोटा पत्र जी"}}}, {"category": "Ll", "key": "0121", "mappings": {"default": {"default": "लैटिन स्मॉल लेटर G विथ डॉट एबव"}}}, {"category": "Ll", "key": "0123", "mappings": {"default": {"default": "सेडिला के साथ लैटिन छोटा पत्र जी"}}}, {"category": "Ll", "key": "0125", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र एच"}}}, {"category": "Ll", "key": "0129", "mappings": {"default": {"default": "टिल्डे के साथ I"}}}, {"category": "Ll", "key": "012B", "mappings": {"default": {"default": "मैक्रॉन के साथ लैटिन स्माल लेटर I"}}}, {"category": "Ll", "key": "012D", "mappings": {"default": {"default": "ब्रेव के साथ लैटिन स्माल लेटर I"}}}, {"category": "Ll", "key": "012F", "mappings": {"default": {"default": "ओगोनक के साथ I"}}}, {"category": "Ll", "key": "0131", "mappings": {"default": {"default": "लैटिन स्माल लेटर डॉटलेस I"}}}, {"category": "Ll", "key": "0135", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र जे"}}}, {"category": "Ll", "key": "0137", "mappings": {"default": {"default": "सेडिला के साथ लैटिन छोटा पत्र के"}}}, {"category": "Ll", "key": "013A", "mappings": {"default": {"default": "तीव्र के साथ एल"}}}, {"category": "Ll", "key": "013C", "mappings": {"default": {"default": "सेडिला के साथ लैटिन छोटा पत्र एल"}}}, {"category": "Ll", "key": "013E", "mappings": {"default": {"default": "कैरन के साथ लैटिन छोटा पत्र एल"}}}, {"category": "Ll", "key": "0140", "mappings": {"default": {"default": "मध्य डॉट के साथ लैटिन छोटा पत्र एल"}}}, {"category": "Ll", "key": "0144", "mappings": {"default": {"default": "एक्यूट के साथ लैटिन स्माल लेटर एन"}}}, {"category": "Ll", "key": "0146", "mappings": {"default": {"default": "सेडिला के साथ लैटिन छोटा पत्र एन"}}}, {"category": "Ll", "key": "0148", "mappings": {"default": {"default": "कैरन के साथ लैटिन छोटा पत्र एन"}}}, {"category": "Ll", "key": "014D", "mappings": {"default": {"default": "मैक्रॉन के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "014F", "mappings": {"default": {"default": "ब्रेव के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "0151", "mappings": {"default": {"default": "डबल एक्यूट के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "0155", "mappings": {"default": {"default": "एक्यूट के साथ लैटिन स्माल लेटर आर"}}}, {"category": "Ll", "key": "0157", "mappings": {"default": {"default": "सेडिला के साथ लैटिन छोटा पत्र आर"}}}, {"category": "Ll", "key": "0159", "mappings": {"default": {"default": "कैरन के साथ लैटिन छोटा पत्र आर"}}}, {"category": "Ll", "key": "015B", "mappings": {"default": {"default": "तीव्र के साथ एस"}}}, {"category": "Ll", "key": "015D", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र एस"}}}, {"category": "Ll", "key": "015F", "mappings": {"default": {"default": "सेडिला के साथ एस"}}}, {"category": "Ll", "key": "0161", "mappings": {"default": {"default": "कैरन के साथ एस"}}}, {"category": "Ll", "key": "0163", "mappings": {"default": {"default": "सेडिला के साथ लैटिन छोटा पत्र टी"}}}, {"category": "Ll", "key": "0165", "mappings": {"default": {"default": "कैरन के साथ लैटिन स्माल लेटर टी"}}}, {"category": "Ll", "key": "0169", "mappings": {"default": {"default": "टिल्डे के साथ यू"}}}, {"category": "Ll", "key": "016B", "mappings": {"default": {"default": "मैक्रॉन के साथ लैटिन छोटा पत्र यू"}}}, {"category": "Ll", "key": "016D", "mappings": {"default": {"default": "ब्रेव के साथ लैटिन छोटा पत्र यू"}}}, {"category": "Ll", "key": "016F", "mappings": {"default": {"default": "लैटिन स्मॉल लेटर यू विद रिंग एबव"}}}, {"category": "Ll", "key": "0171", "mappings": {"default": {"default": "डबल एक्यूट के साथ लैटिन स्माल लेटर यू"}}}, {"category": "Ll", "key": "0173", "mappings": {"default": {"default": "ओगोनक के साथ यू"}}}, {"category": "Ll", "key": "0175", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र डब्ल्यू"}}}, {"category": "Ll", "key": "0177", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र वाई"}}}, {"category": "Ll", "key": "017A", "mappings": {"default": {"default": "एक्यूट के साथ लैटिन स्माल लेटर जेड"}}}, {"category": "Ll", "key": "017C", "mappings": {"default": {"default": "लैटिन स्मॉल लेटर जेड डॉट एबव के साथ"}}}, {"category": "Ll", "key": "017E", "mappings": {"default": {"default": "कैरन के साथ लैटिन छोटा पत्र जेड"}}}, {"category": "Ll", "key": "01CE", "mappings": {"default": {"default": "कैरन के साथ लैटिन छोटा पत्र"}}}, {"category": "Ll", "key": "01D0", "mappings": {"default": {"default": "कैरन के साथ लैटिन स्माल लेटर I"}}}, {"category": "Ll", "key": "01D2", "mappings": {"default": {"default": "कैरन के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "01D4", "mappings": {"default": {"default": "कैरन के साथ लैटिन छोटा पत्र यू"}}}, {"category": "Ll", "key": "01E7", "mappings": {"default": {"default": "कैरन के साथ लैटिन छोटा पत्र जी"}}}, {"category": "Ll", "key": "01E9", "mappings": {"default": {"default": "कैरन के साथ लैटिन छोटा पत्र के"}}}, {"category": "Ll", "key": "01EB", "mappings": {"default": {"default": "ओगोनक के साथ ओ"}}}, {"category": "Ll", "key": "01F0", "mappings": {"default": {"default": "कैरन के साथ लैटिन छोटा पत्र जे"}}}, {"category": "Ll", "key": "01F5", "mappings": {"default": {"default": "तीव्र के साथ जी"}}}, {"category": "Ll", "key": "01F9", "mappings": {"default": {"default": "ग्रेव के साथ लैटिन स्माल लेटर एन"}}}, {"category": "Ll", "key": "0201", "mappings": {"default": {"default": "डबल ग्रेव के साथ लैटिन स्माल लेटर"}}}, {"category": "Ll", "key": "0203", "mappings": {"default": {"default": "उल्टे ब्रेव के साथ लैटिन छोटा पत्र"}}}, {"category": "Ll", "key": "0205", "mappings": {"default": {"default": "डबल ग्रेव के साथ लैटिन स्माल लेटर ई"}}}, {"category": "Ll", "key": "0207", "mappings": {"default": {"default": "उल्टे ब्रेव के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "0209", "mappings": {"default": {"default": "लेटिन स्माल लेटर I विथ डबल ग्रेव"}}}, {"category": "Ll", "key": "020B", "mappings": {"default": {"default": "उल्टे ब्रेव के साथ लैटिन स्माल लेटर I"}}}, {"category": "Ll", "key": "020D", "mappings": {"default": {"default": "डबल ग्रेव के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "020F", "mappings": {"default": {"default": "उल्टे ब्रेव के साथ लैटिन स्माल लेटर ओ"}}}, {"category": "Ll", "key": "0211", "mappings": {"default": {"default": "डबल ग्रेव के साथ लैटिन स्माल लेटर आर"}}}, {"category": "Ll", "key": "0213", "mappings": {"default": {"default": "उल्टे ब्रेव के साथ लैटिन छोटा पत्र आर"}}}, {"category": "Ll", "key": "0215", "mappings": {"default": {"default": "डबल ग्रेव के साथ लैटिन छोटा पत्र यू"}}}, {"category": "Ll", "key": "0217", "mappings": {"default": {"default": "उल्टे ब्रेव के साथ लैटिन छोटा पत्र यू"}}}, {"category": "Ll", "key": "0219", "mappings": {"default": {"default": "नीचे कॉमा के साथ एस"}}}, {"category": "Ll", "key": "021B", "mappings": {"default": {"default": "नीचे कॉमा के साथ टी"}}}, {"category": "Ll", "key": "021F", "mappings": {"default": {"default": "कैरन के साथ लैटिन छोटा पत्र एच"}}}, {"category": "Ll", "key": "0227", "mappings": {"default": {"default": "लैटिन छोटा पत्र डॉट एबव के साथ"}}}, {"category": "Ll", "key": "0229", "mappings": {"default": {"default": "सेडिला के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "022F", "mappings": {"default": {"default": "ओ ऊपर डॉट के साथ"}}}, {"category": "Ll", "key": "0233", "mappings": {"default": {"default": "मैक्रॉन के साथ लैटिन छोटा पत्र वाई"}}}, {"category": "Ll", "key": "0237", "mappings": {"default": {"default": "डॉटलेस जे"}}}, {"category": "Ll", "key": "1E01", "mappings": {"default": {"default": "नीचे रिंग के साथ लैटिन छोटा पत्र"}}}, {"category": "Ll", "key": "1E03", "mappings": {"default": {"default": "लैटिन स्मॉल लेटर बी डॉट एबव के साथ"}}}, {"category": "Ll", "key": "1E05", "mappings": {"default": {"default": "नीचे के साथ लैटिन छोटा पत्र बी"}}}, {"category": "Ll", "key": "1E07", "mappings": {"default": {"default": "नीचे लाइन के साथ लैटिन छोटा पत्र बी"}}}, {"category": "Ll", "key": "1E0B", "mappings": {"default": {"default": "लैटिन स्मॉल लेटर डी विथ डॉट एबव"}}}, {"category": "Ll", "key": "1E0D", "mappings": {"default": {"default": "नीचे के साथ लैटिन छोटा पत्र डी"}}}, {"category": "Ll", "key": "1E0F", "mappings": {"default": {"default": "नीचे लाइन के साथ लैटिन छोटा पत्र डी"}}}, {"category": "Ll", "key": "1E11", "mappings": {"default": {"default": "सेडिला के साथ लैटिन छोटा पत्र डी"}}}, {"category": "Ll", "key": "1E13", "mappings": {"default": {"default": "नीचे सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र डी"}}}, {"category": "Ll", "key": "1E19", "mappings": {"default": {"default": "नीचे सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "1E1B", "mappings": {"default": {"default": "नीचे टिल्डे के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "1E1F", "mappings": {"default": {"default": "एफ ऊपर डॉट के साथ"}}}, {"category": "Ll", "key": "1E21", "mappings": {"default": {"default": "मैक्रॉन के साथ लैटिन छोटा पत्र जी"}}}, {"category": "Ll", "key": "1E23", "mappings": {"default": {"default": "लैटिन स्मॉल लेटर एच विद डॉट एबव"}}}, {"category": "Ll", "key": "1E25", "mappings": {"default": {"default": "नीचे के साथ लैटिन छोटा पत्र एच"}}}, {"category": "Ll", "key": "1E27", "mappings": {"default": {"default": "डायसिस के साथ एच"}}}, {"category": "Ll", "key": "1E29", "mappings": {"default": {"default": "सेडिला के साथ लैटिन छोटा पत्र एच"}}}, {"category": "Ll", "key": "1E2B", "mappings": {"default": {"default": "नीचे ब्रेव के साथ लैटिन छोटा पत्र एच"}}}, {"category": "Ll", "key": "1E2D", "mappings": {"default": {"default": "नीचे टिल्डे के साथ लैटिन छोटा पत्र I"}}}, {"category": "Ll", "key": "1E31", "mappings": {"default": {"default": "एक्यूट के साथ लैटिन स्माल लेटर K"}}}, {"category": "Ll", "key": "1E33", "mappings": {"default": {"default": "नीचे के साथ K"}}}, {"category": "Ll", "key": "1E35", "mappings": {"default": {"default": "नीचे लाइन के साथ लैटिन छोटा पत्र के"}}}, {"category": "Ll", "key": "1E37", "mappings": {"default": {"default": "नीचे के साथ लैटिन छोटा पत्र एल"}}}, {"category": "Ll", "key": "1E3B", "mappings": {"default": {"default": "नीचे लाइन के साथ लैटिन छोटा पत्र एल"}}}, {"category": "Ll", "key": "1E3D", "mappings": {"default": {"default": "नीचे दिए गए सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र एल"}}}, {"category": "Ll", "key": "1E3F", "mappings": {"default": {"default": "तीव्र के साथ एम"}}}, {"category": "Ll", "key": "1E41", "mappings": {"default": {"default": "लैटिन स्मॉल लेटर एम विद डॉट एबव"}}}, {"category": "Ll", "key": "1E43", "mappings": {"default": {"default": "नीचे के साथ एम"}}}, {"category": "Ll", "key": "1E45", "mappings": {"default": {"default": "लैटिन छोटे पत्र एन डॉट एबव के साथ"}}}, {"category": "Ll", "key": "1E47", "mappings": {"default": {"default": "नीचे के साथ लैटिन छोटा पत्र एन"}}}, {"category": "Ll", "key": "1E49", "mappings": {"default": {"default": "नीचे लाइन के साथ लैटिन छोटा पत्र एन"}}}, {"category": "Ll", "key": "1E4B", "mappings": {"default": {"default": "नीचे सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र एन"}}}, {"category": "Ll", "key": "1E55", "mappings": {"default": {"default": "एक्यूट के साथ लैटिन स्माल लेटर पी"}}}, {"category": "Ll", "key": "1E57", "mappings": {"default": {"default": "लैटिन स्मॉल लेटर P, डॉट एबव के साथ"}}}, {"category": "Ll", "key": "1E59", "mappings": {"default": {"default": "आर ऊपर डॉट के साथ"}}}, {"category": "Ll", "key": "1E5B", "mappings": {"default": {"default": "नीचे के साथ लैटिन छोटा पत्र आर"}}}, {"category": "Ll", "key": "1E5F", "mappings": {"default": {"default": "नीचे लाइन के साथ लैटिन छोटा पत्र आर"}}}, {"category": "Ll", "key": "1E61", "mappings": {"default": {"default": "एस ऊपर डॉट के साथ"}}}, {"category": "Ll", "key": "1E63", "mappings": {"default": {"default": "नीचे के साथ एस"}}}, {"category": "Ll", "key": "1E6B", "mappings": {"default": {"default": "लैटिन छोटा पत्र टी डॉट ऊपर के साथ"}}}, {"category": "Ll", "key": "1E6D", "mappings": {"default": {"default": "नीचे के साथ लैटिन छोटा पत्र टी"}}}, {"category": "Ll", "key": "1E6F", "mappings": {"default": {"default": "नीचे लाइन के साथ लैटिन छोटा पत्र टी"}}}, {"category": "Ll", "key": "1E71", "mappings": {"default": {"default": "नीचे सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र टी"}}}, {"category": "Ll", "key": "1E73", "mappings": {"default": {"default": "नीचे दीएसिस के साथ लैटिन छोटा पत्र यू"}}}, {"category": "Ll", "key": "1E75", "mappings": {"default": {"default": "नीचे टिल्डे के साथ लैटिन छोटा पत्र यू"}}}, {"category": "Ll", "key": "1E77", "mappings": {"default": {"default": "नीचे सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र यू"}}}, {"category": "Ll", "key": "1E7D", "mappings": {"default": {"default": "टिल्डे के साथ वी"}}}, {"category": "Ll", "key": "1E7F", "mappings": {"default": {"default": "नीचे के साथ वी"}}}, {"category": "Ll", "key": "1E81", "mappings": {"default": {"default": "ग्रेव के साथ लैटिन स्माल लेटर डब्ल्यू"}}}, {"category": "Ll", "key": "1E83", "mappings": {"default": {"default": "तीव्र के साथ डब्ल्यू"}}}, {"category": "Ll", "key": "1E85", "mappings": {"default": {"default": "डायसिस के साथ लैटिन स्मॉल लेटर डब्ल्यू"}}}, {"category": "Ll", "key": "1E87", "mappings": {"default": {"default": "लैटिन स्मॉल लेटर डब्ल्यू विद डॉट एबव"}}}, {"category": "Ll", "key": "1E89", "mappings": {"default": {"default": "नीचे के साथ डब्ल्यू"}}}, {"category": "Ll", "key": "1E8B", "mappings": {"default": {"default": "लैटिन स्मॉल लेटर X, डॉट एबव के साथ"}}}, {"category": "Ll", "key": "1E8D", "mappings": {"default": {"default": "डायरिसिस के साथ लैटिन स्माल लेटर एक्स"}}}, {"category": "Ll", "key": "1E8F", "mappings": {"default": {"default": "लैटिन छोटा पत्र वाई डॉट एबव के साथ"}}}, {"category": "Ll", "key": "1E91", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ लैटिन छोटा पत्र जेड"}}}, {"category": "Ll", "key": "1E93", "mappings": {"default": {"default": "नीचे के साथ लैटिन छोटा पत्र जेड"}}}, {"category": "Ll", "key": "1E95", "mappings": {"default": {"default": "नीचे लाइन के साथ लैटिन छोटा पत्र जेड"}}}, {"category": "Ll", "key": "1E96", "mappings": {"default": {"default": "नीचे लाइन के साथ लैटिन छोटा पत्र एच"}}}, {"category": "Ll", "key": "1E97", "mappings": {"default": {"default": "डायसिस के साथ टी"}}}, {"category": "Ll", "key": "1E98", "mappings": {"default": {"default": "रिंग के ऊपर लैटिन छोटा पत्र डब्ल्यू"}}}, {"category": "Ll", "key": "1E99", "mappings": {"default": {"default": "रिंग के ऊपर के साथ लैटिन छोटा पत्र वाई"}}}, {"category": "Ll", "key": "1E9A", "mappings": {"default": {"default": "राइट हाफ रिंग के साथ लैटिन स्माल लेटर"}}}, {"category": "Ll", "key": "1EA1", "mappings": {"default": {"default": "नीचे के साथ लैटिन लघु पत्र"}}}, {"category": "Ll", "key": "1EA3", "mappings": {"default": {"default": "हुक के ऊपर के साथ लैटिन छोटा पत्र"}}}, {"category": "Ll", "key": "1EB9", "mappings": {"default": {"default": "नीचे के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "1EBB", "mappings": {"default": {"default": "हुक के ऊपर लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "1EBD", "mappings": {"default": {"default": "टिल्डे के साथ लैटिन छोटा पत्र ई"}}}, {"category": "Ll", "key": "1EC9", "mappings": {"default": {"default": "लैटिन स्माल लेटर I विद हुक एबव"}}}, {"category": "Ll", "key": "1ECB", "mappings": {"default": {"default": "लैटिन छोटा पत्र I नीचे डॉट के साथ"}}}, {"category": "Ll", "key": "1ECD", "mappings": {"default": {"default": "नीचे के साथ लैटिन छोटा पत्र ओ"}}}, {"category": "Ll", "key": "1ECF", "mappings": {"default": {"default": "हुक के ऊपर लैटिन छोटा पत्र ओ"}}}, {"category": "Ll", "key": "1EE5", "mappings": {"default": {"default": "नीचे के साथ यू"}}}, {"category": "Ll", "key": "1EE7", "mappings": {"default": {"default": "हुक के ऊपर लैटिन छोटा पत्र यू"}}}, {"category": "Ll", "key": "1EF3", "mappings": {"default": {"default": "ग्रेव के साथ लैटिन स्माल लेटर वाई"}}}, {"category": "Ll", "key": "1EF5", "mappings": {"default": {"default": "नीचे के साथ वाई"}}}, {"category": "Ll", "key": "1EF7", "mappings": {"default": {"default": "हुक के ऊपर लैटिन छोटा पत्र वाई"}}}, {"category": "Ll", "key": "1EF9", "mappings": {"default": {"default": "टिल्डे के साथ वाई"}}}], "hi/symbols/latin-rest.min": [{"locale": "hi"}, {"category": "Ll", "key": "210E", "mappings": {"default": {"default": "प्लांक कॉन्स्टेंट"}}}, {"category": "Mn", "key": "0363", "mappings": {"default": {"default": "ए का संयोजन"}}}, {"category": "Mn", "key": "0364", "mappings": {"default": {"default": "ई का संयोजन"}}}, {"category": "Mn", "key": "0365", "mappings": {"default": {"default": "लैटिन लैटिन लेटर I का संयोजन"}}}, {"category": "Mn", "key": "0366", "mappings": {"default": {"default": "O का संयोजन"}}}, {"category": "Mn", "key": "0367", "mappings": {"default": {"default": "यू का संयोजन"}}}, {"category": "Mn", "key": "0368", "mappings": {"default": {"default": "सी का संयोजन"}}}, {"category": "Mn", "key": "0369", "mappings": {"default": {"default": "डी का संयोजन"}}}, {"category": "Mn", "key": "036A", "mappings": {"default": {"default": "एच का संयोजन"}}}, {"category": "Mn", "key": "036B", "mappings": {"default": {"default": "एम का संयोजन"}}}, {"category": "Mn", "key": "036C", "mappings": {"default": {"default": "आर का संयोजन"}}}, {"category": "Mn", "key": "036D", "mappings": {"default": {"default": "टी का संयोजन"}}}, {"category": "Mn", "key": "036E", "mappings": {"default": {"default": "वी का संयोजन"}}}, {"category": "Mn", "key": "036F", "mappings": {"default": {"default": "एक्स का संयोजन"}}}, {"category": "Lm", "key": "1D62", "mappings": {"default": {"default": "लैटिन सदस्यता छोटी पत्र I"}}}, {"category": "Lm", "key": "1D63", "mappings": {"default": {"default": "लैटिन सदस्यता छोटी पत्र आर"}}}, {"category": "Lm", "key": "1D64", "mappings": {"default": {"default": "लैटिन सदस्यता छोटी पत्र यू"}}}, {"category": "Lm", "key": "1D65", "mappings": {"default": {"default": "लैटिन सदस्यता छोटी पत्र वी"}}}, {"category": "Mn", "key": "1DCA", "mappings": {"default": {"default": "नीचे लैटिन छोटे पत्र आर का संयोजन"}}}, {"category": "Mn", "key": "1DD3", "mappings": {"default": {"default": "लैटिन छोटे पत्र को मिलाकर चपटा खुला एक ऊपर"}}}, {"category": "Mn", "key": "1DD4", "mappings": {"default": {"default": "एई का संयोजन"}}}, {"category": "Mn", "key": "1DD5", "mappings": {"default": {"default": "एओ का संयोजन"}}}, {"category": "Mn", "key": "1DD6", "mappings": {"default": {"default": "एव का संयोजन"}}}, {"category": "Mn", "key": "1DD7", "mappings": {"default": {"default": "लैटिन लैटिन लेटर सी सेडिला का संयोजन"}}}, {"category": "Mn", "key": "1DD8", "mappings": {"default": {"default": "इंसुलर डी का संयोजन"}}}, {"category": "Mn", "key": "1DD9", "mappings": {"default": {"default": "लैटिन लैटिन लेटर एथ का संयोजन"}}}, {"category": "Mn", "key": "1DDA", "mappings": {"default": {"default": "लैटिन छोटे पत्र जी का संयोजन"}}}, {"category": "Mn", "key": "1DDB", "mappings": {"default": {"default": "लेटिन लेटर स्मॉल कैपिटल जी को मिलाकर"}}}, {"category": "Mn", "key": "1DDC", "mappings": {"default": {"default": "<PERSON> का संयोजन"}}}, {"category": "Mn", "key": "1DDD", "mappings": {"default": {"default": "एल का संयोजन"}}}, {"category": "Mn", "key": "1DDE", "mappings": {"default": {"default": "लैटिन लेटर का संयोजन स्मॉल कैपिटल एल"}}}, {"category": "Mn", "key": "1DDF", "mappings": {"default": {"default": "लेटिन लेटर स्माल कैपिटल एम"}}}, {"category": "Mn", "key": "1DE0", "mappings": {"default": {"default": "एन का संयोजन"}}}, {"category": "Mn", "key": "1DE1", "mappings": {"default": {"default": "लैटिन लेटर का मेल स्मॉल कैपिटल एन"}}}, {"category": "Mn", "key": "1DE2", "mappings": {"default": {"default": "लैटिन लेटर का मेल स्मॉल कैपिटल आर"}}}, {"category": "Mn", "key": "1DE3", "mappings": {"default": {"default": "लैटिन छोटे पत्र आर रोटुंडा का संयोजन"}}}, {"category": "Mn", "key": "1DE4", "mappings": {"default": {"default": "एस का संयोजन"}}}, {"category": "Mn", "key": "1DE5", "mappings": {"default": {"default": "लैटिन छोटे पत्र लंबे एस का संयोजन"}}}, {"category": "Mn", "key": "1DE6", "mappings": {"default": {"default": "जेड का संयोजन"}}}, {"category": "Lm", "key": "2071", "mappings": {"default": {"default": "सुपरस्क्रिप्ट लैटिन लैटिन पत्र I"}}}, {"category": "Lm", "key": "207F", "mappings": {"default": {"default": "सुपरस्क्रिप्ट एन"}}}, {"category": "Lm", "key": "2090", "mappings": {"default": {"default": "लैटिन सदस्यता छोटे पत्र ए"}}}, {"category": "Lm", "key": "2091", "mappings": {"default": {"default": "लैटिन सदस्यता छोटे पत्र ई"}}}, {"category": "Lm", "key": "2092", "mappings": {"default": {"default": "लैटिन सदस्यता छोटे पत्र हे"}}}, {"category": "Lm", "key": "2093", "mappings": {"default": {"default": "लैटिन सदस्यता छोटे पत्र एक्स"}}}, {"category": "Lm", "key": "2094", "mappings": {"default": {"default": "लैटिन सदस्यता छोटे पत्र Schwa"}}}, {"category": "Lm", "key": "2095", "mappings": {"default": {"default": "लैटिन सदस्यता छोटे पत्र एच"}}}, {"category": "Lm", "key": "2096", "mappings": {"default": {"default": "लैटिन सदस्यता छोटे पत्र K"}}}, {"category": "Lm", "key": "2097", "mappings": {"default": {"default": "लैटिन सदस्यता छोटे पत्र एल"}}}, {"category": "Lm", "key": "2098", "mappings": {"default": {"default": "लैटिन सदस्यता छोटे पत्र एम"}}}, {"category": "Lm", "key": "2099", "mappings": {"default": {"default": "लैटिन सदस्यता छोटी पत्र एन"}}}, {"category": "Lm", "key": "209A", "mappings": {"default": {"default": "लैटिन सदस्यता छोटे पत्र पी"}}}, {"category": "Lm", "key": "209B", "mappings": {"default": {"default": "लैटिन सदस्यता छोटे पत्र एस"}}}, {"category": "Lm", "key": "209C", "mappings": {"default": {"default": "लैटिन सदस्यता छोटे पत्र टी"}}}, {"category": "Lm", "key": "2C7C", "mappings": {"default": {"default": "लैटिन सदस्यता छोटे पत्र जे"}}}, {"category": "So", "key": "1F12A", "mappings": {"default": {"default": "कछुआ शैल ब्रैकेटेड एस"}}}, {"category": "So", "key": "1F12B", "mappings": {"default": {"default": "सर्किल इटैलिक सी"}}}, {"category": "So", "key": "1F12C", "mappings": {"default": {"default": "सर्किल इटैलिक आर"}}}, {"category": "So", "key": "1F18A", "mappings": {"default": {"default": "पार किए गए नकारात्मक वर्ग लैटिन पूंजी पत्र P"}}}], "hi/symbols/latin-upper-double-accent.min": [{"locale": "hi"}, {"category": "<PERSON>", "key": "01D5", "mappings": {"default": {"default": "डायरसिस और मैक्रोन के साथ यू"}}}, {"category": "<PERSON>", "key": "01D7", "mappings": {"default": {"default": "डायरिसिस और एक्यूट के साथ यू"}}}, {"category": "<PERSON>", "key": "01D9", "mappings": {"default": {"default": "डायरिसिस और कैरन के साथ यू"}}}, {"category": "<PERSON>", "key": "01DB", "mappings": {"default": {"default": "डायरिसिस और ग्रेव के साथ यू"}}}, {"category": "<PERSON>", "key": "01DE", "mappings": {"default": {"default": "डायरिसिस और मैक्रोन के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "01E0", "mappings": {"default": {"default": "के साथ डॉट एबव और मैक्रोन"}}}, {"category": "<PERSON>", "key": "01EC", "mappings": {"default": {"default": "ओगोनक और मैक्रोन के साथ ओ"}}}, {"category": "<PERSON>", "key": "01FA", "mappings": {"default": {"default": "विथ रिंग एबव और एक्यूट"}}}, {"category": "<PERSON>", "key": "022A", "mappings": {"default": {"default": "डायरिसिस और मैक्रोन के साथ ओ"}}}, {"category": "<PERSON>", "key": "022C", "mappings": {"default": {"default": "टिल्डे और मैक्रॉन के साथ ओ"}}}, {"category": "<PERSON>", "key": "0230", "mappings": {"default": {"default": "ओ विथ डॉट एबव और मैक्रोन"}}}, {"category": "<PERSON>", "key": "1E08", "mappings": {"default": {"default": "सेडिला और एक्यूट के साथ सी"}}}, {"category": "<PERSON>", "key": "1E14", "mappings": {"default": {"default": "मैक्रॉन और ग्रेव के साथ ई"}}}, {"category": "<PERSON>", "key": "1E16", "mappings": {"default": {"default": "मैक्रॉन और एक्यूट के साथ ई"}}}, {"category": "<PERSON>", "key": "1E1C", "mappings": {"default": {"default": "सेडिला और ब्रेव के साथ ई"}}}, {"category": "<PERSON>", "key": "1E2E", "mappings": {"default": {"default": "I विद डायरेसिस एंड एक्यूट"}}}, {"category": "<PERSON>", "key": "1E38", "mappings": {"default": {"default": "लैटिन राजधानी पत्र एल नीचे डॉट और मैक्रॉन के साथ"}}}, {"category": "<PERSON>", "key": "1E4C", "mappings": {"default": {"default": "टिल्डे और एक्यूट के साथ ओ"}}}, {"category": "<PERSON>", "key": "1E4E", "mappings": {"default": {"default": "टिल्डे और डायरेसिस के साथ ओ"}}}, {"category": "<PERSON>", "key": "1E50", "mappings": {"default": {"default": "मैक्रॉन और ग्रेव के साथ ओ"}}}, {"category": "<PERSON>", "key": "1E52", "mappings": {"default": {"default": "मैक्रॉन और एक्यूट के साथ ओ"}}}, {"category": "<PERSON>", "key": "1E5C", "mappings": {"default": {"default": "लैटिन राजधानी पत्र आर नीचे डॉट और मैक्रॉन के साथ"}}}, {"category": "<PERSON>", "key": "1E64", "mappings": {"default": {"default": "एक्यूट और डॉट एबव के साथ एस"}}}, {"category": "<PERSON>", "key": "1E66", "mappings": {"default": {"default": "कैरन और डॉट एबव के साथ एस"}}}, {"category": "<PERSON>", "key": "1E68", "mappings": {"default": {"default": "एस के साथ डॉट नीचे और डॉट के ऊपर"}}}, {"category": "<PERSON>", "key": "1E78", "mappings": {"default": {"default": "टिल्डे और एक्यूट के साथ यू"}}}, {"category": "<PERSON>", "key": "1E7A", "mappings": {"default": {"default": "मैक्रोन और डायसिस के साथ यू"}}}, {"category": "<PERSON>", "key": "1EA4", "mappings": {"default": {"default": "सर्कमफ्लेक्स और एक्यूट के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "1EA6", "mappings": {"default": {"default": "सर्कमफ्लेक्स और ग्रेव के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "1EA8", "mappings": {"default": {"default": "सर्कमफ्लेक्स और हुक एबव के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "1EAA", "mappings": {"default": {"default": "सर्कमफ्लेक्स और टिल्डे के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "1EAC", "mappings": {"default": {"default": "सर्कमफ्लेक्स और डॉट नीचे के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "1EAE", "mappings": {"default": {"default": "ब्रेव और एक्यूट के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "1EB0", "mappings": {"default": {"default": "ब्रेव और ग्रेव के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "1EB2", "mappings": {"default": {"default": "ब्रेव और हुक एबव के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "1EB4", "mappings": {"default": {"default": "ब्रेव और टिल्डे के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "1EB6", "mappings": {"default": {"default": "ब्रेव और डॉट नीचे के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "1EBE", "mappings": {"default": {"default": "सर्कमफ्लेक्स और एक्यूट के साथ ई"}}}, {"category": "<PERSON>", "key": "1EC0", "mappings": {"default": {"default": "सर्कमफ्लेक्स और ग्रेव के साथ ई"}}}, {"category": "<PERSON>", "key": "1EC2", "mappings": {"default": {"default": "सर्कमफ्लेक्स और हुक एबव के साथ ई"}}}, {"category": "<PERSON>", "key": "1EC4", "mappings": {"default": {"default": "सर्कमफ्लेक्स और टिल्डे के साथ ई"}}}, {"category": "<PERSON>", "key": "1EC6", "mappings": {"default": {"default": "सर्कमफ्लेक्स और डॉट नीचे के साथ ई"}}}, {"category": "<PERSON>", "key": "1ED0", "mappings": {"default": {"default": "सर्कमफ्लेक्स और एक्यूट के साथ ओ"}}}, {"category": "<PERSON>", "key": "1ED2", "mappings": {"default": {"default": "सर्कमफ्लेक्स और ग्रेव के साथ ओ"}}}, {"category": "<PERSON>", "key": "1ED4", "mappings": {"default": {"default": "सर्कमफ्लेक्स और हुक एबव के साथ ओ"}}}, {"category": "<PERSON>", "key": "1ED6", "mappings": {"default": {"default": "सर्कमफ्लेक्स और टिल्डे के साथ ओ"}}}, {"category": "<PERSON>", "key": "1ED8", "mappings": {"default": {"default": "सर्कमफ्लेक्स और डॉट नीचे के साथ ओ"}}}, {"category": "<PERSON>", "key": "1EDA", "mappings": {"default": {"default": "हॉर्न और एक्यूट के साथ ओ"}}}, {"category": "<PERSON>", "key": "1EDC", "mappings": {"default": {"default": "हॉर्न और ग्रेव के साथ ओ"}}}, {"category": "<PERSON>", "key": "1EDE", "mappings": {"default": {"default": "हॉर्न और हुक के साथ ओ"}}}, {"category": "<PERSON>", "key": "1EE0", "mappings": {"default": {"default": "हॉर्न और टिल्ड के साथ ओ"}}}, {"category": "<PERSON>", "key": "1EE2", "mappings": {"default": {"default": "हॉर्न और डॉट नीचे के साथ ओ"}}}, {"category": "<PERSON>", "key": "1EE8", "mappings": {"default": {"default": "हॉर्न और एक्यूट के साथ यू"}}}, {"category": "<PERSON>", "key": "1EEA", "mappings": {"default": {"default": "हॉर्न और ग्रेव के साथ यू"}}}, {"category": "<PERSON>", "key": "1EEC", "mappings": {"default": {"default": "हॉर्न और हुक के साथ यू"}}}, {"category": "<PERSON>", "key": "1EEE", "mappings": {"default": {"default": "हॉर्न और टिल्ड के साथ यू"}}}, {"category": "<PERSON>", "key": "1EF0", "mappings": {"default": {"default": "हॉर्न और डॉट नीचे के साथ यू"}}}], "hi/symbols/latin-upper-single-accent.min": [{"locale": "hi"}, {"category": "<PERSON>", "key": "00C0", "mappings": {"default": {"default": "ग्रेव के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "00C1", "mappings": {"default": {"default": "एक्यूट के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "00C2", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "00C3", "mappings": {"default": {"default": "टिल्डे के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "00C4", "mappings": {"default": {"default": "diaeresis के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "00C5", "mappings": {"default": {"default": "विथ रिंग एबव"}}}, {"category": "<PERSON>", "key": "00C7", "mappings": {"default": {"default": "सेडिला के साथ सी"}}}, {"category": "<PERSON>", "key": "00C8", "mappings": {"default": {"default": "ग्रेव के साथ ई"}}}, {"category": "<PERSON>", "key": "00C9", "mappings": {"default": {"default": "एक्यूट के साथ ई"}}}, {"category": "<PERSON>", "key": "00CA", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ ई"}}}, {"category": "<PERSON>", "key": "00CB", "mappings": {"default": {"default": "डायरिसिस के साथ ई"}}}, {"category": "<PERSON>", "key": "00CC", "mappings": {"default": {"default": "I विद ग्रेव"}}}, {"category": "<PERSON>", "key": "00CD", "mappings": {"default": {"default": "एक्यूट के साथ I"}}}, {"category": "<PERSON>", "key": "00CE", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ I"}}}, {"category": "<PERSON>", "key": "00CF", "mappings": {"default": {"default": "डायरिसिस के साथ I"}}}, {"category": "<PERSON>", "key": "00D1", "mappings": {"default": {"default": "टिल्डे के साथ एन"}}}, {"category": "<PERSON>", "key": "00D2", "mappings": {"default": {"default": "ग्रेव के साथ ओ"}}}, {"category": "<PERSON>", "key": "00D3", "mappings": {"default": {"default": "एक्यूट के साथ ओ"}}}, {"category": "<PERSON>", "key": "00D4", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ ओ"}}}, {"category": "<PERSON>", "key": "00D5", "mappings": {"default": {"default": "टिल्डे के साथ ओ"}}}, {"category": "<PERSON>", "key": "00D6", "mappings": {"default": {"default": "डायरिसिस के साथ ओ"}}}, {"category": "<PERSON>", "key": "00D9", "mappings": {"default": {"default": "ग्रेव के साथ यू"}}}, {"category": "<PERSON>", "key": "00DA", "mappings": {"default": {"default": "एक्यूट के साथ यू"}}}, {"category": "<PERSON>", "key": "00DB", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ यू"}}}, {"category": "<PERSON>", "key": "00DC", "mappings": {"default": {"default": "डायरिसिस के साथ यू"}}}, {"category": "<PERSON>", "key": "00DD", "mappings": {"default": {"default": "एक्यूट के साथ वाई"}}}, {"category": "<PERSON>", "key": "0100", "mappings": {"default": {"default": "मैक्रॉन के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "0102", "mappings": {"default": {"default": "ब्रेव के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "0104", "mappings": {"default": {"default": "ओगोनक के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "0106", "mappings": {"default": {"default": "एक्यूट के साथ सी"}}}, {"category": "<PERSON>", "key": "0108", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ सी"}}}, {"category": "<PERSON>", "key": "010A", "mappings": {"default": {"default": "सी के साथ डॉट एबव"}}}, {"category": "<PERSON>", "key": "010C", "mappings": {"default": {"default": "कैरन के साथ सी"}}}, {"category": "<PERSON>", "key": "010E", "mappings": {"default": {"default": "कैरन के साथ डी"}}}, {"category": "<PERSON>", "key": "0112", "mappings": {"default": {"default": "मैक्रोन के साथ ई"}}}, {"category": "<PERSON>", "key": "0114", "mappings": {"default": {"default": "ब्रेव के साथ ई"}}}, {"category": "<PERSON>", "key": "0116", "mappings": {"default": {"default": "E के साथ डॉट एबव"}}}, {"category": "<PERSON>", "key": "0118", "mappings": {"default": {"default": "ओगोनक के साथ ई"}}}, {"category": "<PERSON>", "key": "011A", "mappings": {"default": {"default": "कैरन के साथ ई"}}}, {"category": "<PERSON>", "key": "011C", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ जी"}}}, {"category": "<PERSON>", "key": "011E", "mappings": {"default": {"default": "ब्रेव के साथ जी"}}}, {"category": "<PERSON>", "key": "0120", "mappings": {"default": {"default": "G के साथ डॉट एबव"}}}, {"category": "<PERSON>", "key": "0122", "mappings": {"default": {"default": "सेडिला के साथ जी"}}}, {"category": "<PERSON>", "key": "0124", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ एच"}}}, {"category": "<PERSON>", "key": "0128", "mappings": {"default": {"default": "टिल्डे के साथ I"}}}, {"category": "<PERSON>", "key": "012A", "mappings": {"default": {"default": "मैक्रॉन के साथ I"}}}, {"category": "<PERSON>", "key": "012C", "mappings": {"default": {"default": "ब्रेव के साथ I"}}}, {"category": "<PERSON>", "key": "012E", "mappings": {"default": {"default": "ओगोनक के साथ I"}}}, {"category": "<PERSON>", "key": "0130", "mappings": {"default": {"default": "I विद डॉट एबव"}}}, {"category": "<PERSON>", "key": "0134", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ जे"}}}, {"category": "<PERSON>", "key": "0136", "mappings": {"default": {"default": "सेडिला के साथ के"}}}, {"category": "<PERSON>", "key": "0139", "mappings": {"default": {"default": "एक्यूट के साथ एल"}}}, {"category": "<PERSON>", "key": "013B", "mappings": {"default": {"default": "सेडिला के साथ एल"}}}, {"category": "<PERSON>", "key": "013D", "mappings": {"default": {"default": "कैरन के साथ एल"}}}, {"category": "<PERSON>", "key": "013F", "mappings": {"default": {"default": "मध्य डॉट के साथ एल"}}}, {"category": "<PERSON>", "key": "0143", "mappings": {"default": {"default": "एक्यूट के साथ एन"}}}, {"category": "<PERSON>", "key": "0145", "mappings": {"default": {"default": "सेडिला के साथ एन"}}}, {"category": "<PERSON>", "key": "0147", "mappings": {"default": {"default": "कैरन के साथ एन"}}}, {"category": "<PERSON>", "key": "014C", "mappings": {"default": {"default": "मैक्रोन के साथ ओ"}}}, {"category": "<PERSON>", "key": "014E", "mappings": {"default": {"default": "ब्रेव के साथ ओ"}}}, {"category": "<PERSON>", "key": "0150", "mappings": {"default": {"default": "डबल एक्यूट के साथ ओ"}}}, {"category": "<PERSON>", "key": "0154", "mappings": {"default": {"default": "एक्यूट के साथ आर"}}}, {"category": "<PERSON>", "key": "0156", "mappings": {"default": {"default": "सेडिला के साथ आर"}}}, {"category": "<PERSON>", "key": "0158", "mappings": {"default": {"default": "कैरन के साथ आर"}}}, {"category": "<PERSON>", "key": "015A", "mappings": {"default": {"default": "एक्यूट के साथ एस"}}}, {"category": "<PERSON>", "key": "015C", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ एस"}}}, {"category": "<PERSON>", "key": "015E", "mappings": {"default": {"default": "सेडिला के साथ एस"}}}, {"category": "<PERSON>", "key": "0160", "mappings": {"default": {"default": "कैरन के साथ एस"}}}, {"category": "<PERSON>", "key": "0162", "mappings": {"default": {"default": "सेडिला के साथ टी"}}}, {"category": "<PERSON>", "key": "0164", "mappings": {"default": {"default": "कैरन के साथ टी"}}}, {"category": "<PERSON>", "key": "0168", "mappings": {"default": {"default": "टिल्डे के साथ यू"}}}, {"category": "<PERSON>", "key": "016A", "mappings": {"default": {"default": "मैक्रोन के साथ यू"}}}, {"category": "<PERSON>", "key": "016C", "mappings": {"default": {"default": "ब्रेव के साथ यू"}}}, {"category": "<PERSON>", "key": "016E", "mappings": {"default": {"default": "रिंग कैप के साथ यू"}}}, {"category": "<PERSON>", "key": "0170", "mappings": {"default": {"default": "डबल एक्यूट के साथ यू"}}}, {"category": "<PERSON>", "key": "0172", "mappings": {"default": {"default": "ओगोनक के साथ यू"}}}, {"category": "<PERSON>", "key": "0174", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ डब्ल्यू"}}}, {"category": "<PERSON>", "key": "0176", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ वाई"}}}, {"category": "<PERSON>", "key": "0178", "mappings": {"default": {"default": "डायिरसिस के साथ वाई"}}}, {"category": "<PERSON>", "key": "0179", "mappings": {"default": {"default": "एक्यूट के साथ जेड"}}}, {"category": "<PERSON>", "key": "017B", "mappings": {"default": {"default": "जेड, डॉट एबव के साथ"}}}, {"category": "<PERSON>", "key": "017D", "mappings": {"default": {"default": "कैरन के साथ जेड"}}}, {"category": "<PERSON>", "key": "01CD", "mappings": {"default": {"default": "कैरन के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "01CF", "mappings": {"default": {"default": "कैरन के साथ I"}}}, {"category": "<PERSON>", "key": "01D1", "mappings": {"default": {"default": "कैरन के साथ ओ"}}}, {"category": "<PERSON>", "key": "01D3", "mappings": {"default": {"default": "कैरन के साथ यू"}}}, {"category": "<PERSON>", "key": "01E6", "mappings": {"default": {"default": "कैरन के साथ जी"}}}, {"category": "<PERSON>", "key": "01E8", "mappings": {"default": {"default": "कैरन के साथ के"}}}, {"category": "<PERSON>", "key": "01EA", "mappings": {"default": {"default": "ओगोनक के साथ ओ"}}}, {"category": "<PERSON>", "key": "01F4", "mappings": {"default": {"default": "एक्यूट के साथ जी"}}}, {"category": "<PERSON>", "key": "01F8", "mappings": {"default": {"default": "ग्रेव के साथ एन"}}}, {"category": "<PERSON>", "key": "0200", "mappings": {"default": {"default": "डबल ग्रेव के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "0202", "mappings": {"default": {"default": "उल्टे ब्रेव के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "0204", "mappings": {"default": {"default": "डबल ग्रेव के साथ ई"}}}, {"category": "<PERSON>", "key": "0206", "mappings": {"default": {"default": "उल्टे ब्रेव के साथ ई"}}}, {"category": "<PERSON>", "key": "0208", "mappings": {"default": {"default": "I विथ डबल ग्रेव"}}}, {"category": "<PERSON>", "key": "020A", "mappings": {"default": {"default": "उल्टे ब्रेव के साथ I"}}}, {"category": "<PERSON>", "key": "020C", "mappings": {"default": {"default": "डबल ग्रेव के साथ ओ"}}}, {"category": "<PERSON>", "key": "020E", "mappings": {"default": {"default": "उल्टे ब्रेव के साथ ओ"}}}, {"category": "<PERSON>", "key": "0210", "mappings": {"default": {"default": "डबल ग्रेव के साथ आर"}}}, {"category": "<PERSON>", "key": "0212", "mappings": {"default": {"default": "उल्टे ब्रेव के साथ आर"}}}, {"category": "<PERSON>", "key": "0214", "mappings": {"default": {"default": "डबल ग्रेव के साथ यू"}}}, {"category": "<PERSON>", "key": "0216", "mappings": {"default": {"default": "उल्टे ब्रेव के साथ यू"}}}, {"category": "<PERSON>", "key": "0218", "mappings": {"default": {"default": "नीचे कॉमा के साथ एस"}}}, {"category": "<PERSON>", "key": "021A", "mappings": {"default": {"default": "नीचे कॉमा के साथ टी"}}}, {"category": "<PERSON>", "key": "021E", "mappings": {"default": {"default": "कैरन के साथ एच"}}}, {"category": "<PERSON>", "key": "0226", "mappings": {"default": {"default": "के साथ डॉट एबव"}}}, {"category": "<PERSON>", "key": "0228", "mappings": {"default": {"default": "सेडिला के साथ ई"}}}, {"category": "<PERSON>", "key": "022E", "mappings": {"default": {"default": "ओ विथ डॉट एबव"}}}, {"category": "<PERSON>", "key": "0232", "mappings": {"default": {"default": "मैक्रोन के साथ वाई"}}}, {"category": "<PERSON>", "key": "1E00", "mappings": {"default": {"default": "नीचे रिंग के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "1E02", "mappings": {"default": {"default": "बी, डॉट एबव के साथ"}}}, {"category": "<PERSON>", "key": "1E04", "mappings": {"default": {"default": "नीचे के साथ बी"}}}, {"category": "<PERSON>", "key": "1E06", "mappings": {"default": {"default": "नीचे लाइन के साथ बी"}}}, {"category": "<PERSON>", "key": "1E0A", "mappings": {"default": {"default": "डी के साथ डॉट एबव"}}}, {"category": "<PERSON>", "key": "1E0C", "mappings": {"default": {"default": "नीचे के साथ डी"}}}, {"category": "<PERSON>", "key": "1E0E", "mappings": {"default": {"default": "नीचे लाइन के साथ डी"}}}, {"category": "<PERSON>", "key": "1E10", "mappings": {"default": {"default": "सेडिला के साथ डी"}}}, {"category": "<PERSON>", "key": "1E12", "mappings": {"default": {"default": "नीचे Circumflex के साथ डी"}}}, {"category": "<PERSON>", "key": "1E18", "mappings": {"default": {"default": "नीचे सर्कुफलेक्स के साथ ई"}}}, {"category": "<PERSON>", "key": "1E1A", "mappings": {"default": {"default": "नीचे Tilde के साथ E"}}}, {"category": "<PERSON>", "key": "1E1E", "mappings": {"default": {"default": "F के साथ डॉट एबव"}}}, {"category": "<PERSON>", "key": "1E20", "mappings": {"default": {"default": "मैक्रोन के साथ जी"}}}, {"category": "<PERSON>", "key": "1E22", "mappings": {"default": {"default": "एच विद डॉट एबव"}}}, {"category": "<PERSON>", "key": "1E24", "mappings": {"default": {"default": "नीचे के साथ एच"}}}, {"category": "<PERSON>", "key": "1E26", "mappings": {"default": {"default": "डायरिसिस के साथ एच"}}}, {"category": "<PERSON>", "key": "1E28", "mappings": {"default": {"default": "सेडिला के साथ एच"}}}, {"category": "<PERSON>", "key": "1E2A", "mappings": {"default": {"default": "ब्रेव के साथ एच"}}}, {"category": "<PERSON>", "key": "1E2C", "mappings": {"default": {"default": "नीचे दिए गए टिल्डे के साथ I"}}}, {"category": "<PERSON>", "key": "1E30", "mappings": {"default": {"default": "एक्यूट के साथ K"}}}, {"category": "<PERSON>", "key": "1E32", "mappings": {"default": {"default": "नीचे के साथ K"}}}, {"category": "<PERSON>", "key": "1E34", "mappings": {"default": {"default": "नीचे लाइन के साथ K"}}}, {"category": "<PERSON>", "key": "1E36", "mappings": {"default": {"default": "नीचे के साथ एल"}}}, {"category": "<PERSON>", "key": "1E3A", "mappings": {"default": {"default": "नीचे लाइन के साथ एल"}}}, {"category": "<PERSON>", "key": "1E3C", "mappings": {"default": {"default": "नीचे सर्कुफलेक्स के साथ एल"}}}, {"category": "<PERSON>", "key": "1E3E", "mappings": {"default": {"default": "एक्यूट के साथ एम"}}}, {"category": "<PERSON>", "key": "1E40", "mappings": {"default": {"default": "M के साथ डॉट एबव"}}}, {"category": "<PERSON>", "key": "1E42", "mappings": {"default": {"default": "नीचे के साथ एम"}}}, {"category": "<PERSON>", "key": "1E44", "mappings": {"default": {"default": "एन के साथ डॉट एबव"}}}, {"category": "<PERSON>", "key": "1E46", "mappings": {"default": {"default": "नीचे के साथ एन"}}}, {"category": "<PERSON>", "key": "1E48", "mappings": {"default": {"default": "नीचे लाइन के साथ एन"}}}, {"category": "<PERSON>", "key": "1E4A", "mappings": {"default": {"default": "नीचे सर्कुफलेक्स के साथ एन"}}}, {"category": "<PERSON>", "key": "1E54", "mappings": {"default": {"default": "एक्यूट के साथ P"}}}, {"category": "<PERSON>", "key": "1E56", "mappings": {"default": {"default": "पी के साथ डॉट एबव"}}}, {"category": "<PERSON>", "key": "1E58", "mappings": {"default": {"default": "R के साथ डॉट एबव"}}}, {"category": "<PERSON>", "key": "1E5A", "mappings": {"default": {"default": "नीचे के साथ आर"}}}, {"category": "<PERSON>", "key": "1E5E", "mappings": {"default": {"default": "नीचे लाइन के साथ आर"}}}, {"category": "<PERSON>", "key": "1E60", "mappings": {"default": {"default": "एस के साथ डॉट एबव"}}}, {"category": "<PERSON>", "key": "1E62", "mappings": {"default": {"default": "नीचे के साथ एस"}}}, {"category": "<PERSON>", "key": "1E6A", "mappings": {"default": {"default": "टी के साथ डॉट एबव"}}}, {"category": "<PERSON>", "key": "1E6C", "mappings": {"default": {"default": "नीचे के साथ टी"}}}, {"category": "<PERSON>", "key": "1E6E", "mappings": {"default": {"default": "नीचे लाइन के साथ टी"}}}, {"category": "<PERSON>", "key": "1E70", "mappings": {"default": {"default": "नीचे सर्कुफ्लेक्स के साथ टी"}}}, {"category": "<PERSON>", "key": "1E72", "mappings": {"default": {"default": "नीचे डीएरेसिस के साथ यू"}}}, {"category": "<PERSON>", "key": "1E74", "mappings": {"default": {"default": "नीचे <PERSON>ilde के साथ यू"}}}, {"category": "<PERSON>", "key": "1E76", "mappings": {"default": {"default": "नीचे Circumflex के साथ यू"}}}, {"category": "<PERSON>", "key": "1E7C", "mappings": {"default": {"default": "टिल्डे के साथ वी"}}}, {"category": "<PERSON>", "key": "1E7E", "mappings": {"default": {"default": "नीचे के साथ वी"}}}, {"category": "<PERSON>", "key": "1E80", "mappings": {"default": {"default": "ग्रेव के साथ डब्ल्यू"}}}, {"category": "<PERSON>", "key": "1E82", "mappings": {"default": {"default": "एक्यूट के साथ डब्ल्यू"}}}, {"category": "<PERSON>", "key": "1E84", "mappings": {"default": {"default": "डायिरसिस के साथ डब्ल्यू"}}}, {"category": "<PERSON>", "key": "1E86", "mappings": {"default": {"default": "डब्ल्यू विद डॉट एबव"}}}, {"category": "<PERSON>", "key": "1E88", "mappings": {"default": {"default": "नीचे के साथ डब्ल्यू"}}}, {"category": "<PERSON>", "key": "1E8A", "mappings": {"default": {"default": "X, डॉट एबव के साथ"}}}, {"category": "<PERSON>", "key": "1E8C", "mappings": {"default": {"default": "डायरिसिस के साथ एक्स"}}}, {"category": "<PERSON>", "key": "1E8E", "mappings": {"default": {"default": "वाई के साथ डॉट एबव"}}}, {"category": "<PERSON>", "key": "1E90", "mappings": {"default": {"default": "सर्कमफ्लेक्स के साथ जेड"}}}, {"category": "<PERSON>", "key": "1E92", "mappings": {"default": {"default": "नीचे के साथ जेड"}}}, {"category": "<PERSON>", "key": "1E94", "mappings": {"default": {"default": "नीचे लाइन के साथ जेड"}}}, {"category": "<PERSON>", "key": "1EA0", "mappings": {"default": {"default": "नीचे के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "1EA2", "mappings": {"default": {"default": "हुक कैप के साथ लैटिन कैपिटल लेटर"}}}, {"category": "<PERSON>", "key": "1EB8", "mappings": {"default": {"default": "नीचे के साथ ई"}}}, {"category": "<PERSON>", "key": "1EBA", "mappings": {"default": {"default": "हुक कैप के साथ ई"}}}, {"category": "<PERSON>", "key": "1EBC", "mappings": {"default": {"default": "टिल्डे के साथ ई"}}}, {"category": "<PERSON>", "key": "1EC8", "mappings": {"default": {"default": "I विद हुक एबव"}}}, {"category": "<PERSON>", "key": "1ECA", "mappings": {"default": {"default": "लैटिन राजधानी पत्र I नीचे डॉट के साथ"}}}, {"category": "<PERSON>", "key": "1ECC", "mappings": {"default": {"default": "नीचे के साथ ओ"}}}, {"category": "<PERSON>", "key": "1ECE", "mappings": {"default": {"default": "हुक कैप के साथ ओ"}}}, {"category": "<PERSON>", "key": "1EE4", "mappings": {"default": {"default": "नीचे के साथ यू"}}}, {"category": "<PERSON>", "key": "1EE6", "mappings": {"default": {"default": "हुक कैप के साथ यू"}}}, {"category": "<PERSON>", "key": "1EF2", "mappings": {"default": {"default": "ग्रेव के साथ वाई"}}}, {"category": "<PERSON>", "key": "1EF4", "mappings": {"default": {"default": "नीचे के साथ वाई"}}}, {"category": "<PERSON>", "key": "1EF6", "mappings": {"default": {"default": "हुक कैप के साथ वाई"}}}, {"category": "<PERSON>", "key": "1EF8", "mappings": {"default": {"default": "टिल्डे के साथ वाई"}}}], "hi/symbols/math_angles.min": [{"locale": "hi"}, {"category": "Sm", "key": "22BE", "mappings": {"default": {"default": "आर्क के साथ राइट एंगल"}}}, {"category": "Sm", "key": "237C", "mappings": {"default": {"default": "राइट एंगल विथ डाउनवर्ड जिगजैग एरो"}}}, {"category": "Sm", "key": "27C0", "mappings": {"default": {"default": "तीन आयामी कोण"}}}, {"category": "Sm", "key": "299B", "mappings": {"default": {"default": "मापा कोण खुलने वाला बायाँ"}}}, {"category": "Sm", "key": "299C", "mappings": {"default": {"default": "स्क्वायर के साथ राइट एंगल वेरिएंट"}}}, {"category": "Sm", "key": "299D", "mappings": {"default": {"default": "डॉट के साथ सही कोण मापा जाता है"}}}, {"category": "Sm", "key": "299E", "mappings": {"default": {"default": "एस अंदर के साथ कोण"}}}, {"category": "Sm", "key": "299F", "mappings": {"default": {"default": "न्यून कोण"}}}, {"category": "Sm", "key": "29A0", "mappings": {"default": {"default": "गोलाकार कोण खोलना वाम"}}}, {"category": "Sm", "key": "29A1", "mappings": {"default": {"default": "गोलाकार कोण खोलना"}}}, {"category": "Sm", "key": "29A2", "mappings": {"default": {"default": "बदल गया कोण"}}}, {"category": "Sm", "key": "29A3", "mappings": {"default": {"default": "उलटा कोण"}}}, {"category": "Sm", "key": "29A4", "mappings": {"default": {"default": "अंडरबार के साथ कोण"}}}, {"category": "Sm", "key": "29A5", "mappings": {"default": {"default": "अंबर के साथ उलटा कोण"}}}, {"category": "Sm", "key": "29A6", "mappings": {"default": {"default": "ओब्लिक एंगल ओपनिंग"}}}, {"category": "Sm", "key": "29A7", "mappings": {"default": {"default": "ओब्लिक एंगल ओपनिंग डाउन"}}}, {"category": "Sm", "key": "29A8", "mappings": {"default": {"default": "एरो पोज़िंग अप और राइट में ओपन आर्म एंडिंग के साथ मापा गया एंगल"}}}, {"category": "Sm", "key": "29A9", "mappings": {"default": {"default": "एरो पोज़िंग अप और लेफ्ट में ओपन आर्म एंडिंग के साथ मापा गया एंगल"}}}, {"category": "Sm", "key": "29AA", "mappings": {"default": {"default": "एरो पाइंटिंग डाउन एंड राइट में ओपन आर्म एंडिंग के साथ एंगल मापा गया"}}}, {"category": "Sm", "key": "29AB", "mappings": {"default": {"default": "तीर और नीचे और बाएं ओर इशारा करते हुए ओपन आर्म एंडिंग के साथ मापा गया कोण"}}}, {"category": "Sm", "key": "29AC", "mappings": {"default": {"default": "एरो पाइंटिंग राइट एंड अप में ओपन आर्म एंडिंग के साथ एंगल मापा गया"}}}, {"category": "Sm", "key": "29AD", "mappings": {"default": {"default": "बायाँ और ऊपर की ओर इशारा करते हुए खुले हाथ की छोर के साथ मापा गया कोण"}}}, {"category": "Sm", "key": "29AE", "mappings": {"default": {"default": "एरो पाइंटिंग राइट एंड डाउन में ओपन आर्म एंडिंग के साथ एंगल मापा गया"}}}, {"category": "Sm", "key": "29AF", "mappings": {"default": {"default": "बायां और नीचे की ओर इशारा करते हुए तीर में ओपन आर्म एंडिंग के साथ मापा गया कोण"}}}], "hi/symbols/math_arrows.min": [{"locale": "hi"}, {"category": "Sm", "key": "2190", "mappings": {"default": {"default": "बांयी और तीर"}}}, {"category": "Sm", "key": "2191", "mappings": {"default": {"default": "ऊपर की ओर तीर"}}}, {"category": "Sm", "key": "2192", "mappings": {"default": {"default": "दाईं ओर तीर"}}}, {"category": "Sm", "key": "2193", "mappings": {"default": {"default": "नीचे की ओर तीर"}}}, {"category": "Sm", "key": "2194", "mappings": {"default": {"default": "दांयी बांयी दोनों और तीर"}}}, {"category": "So", "key": "2195", "mappings": {"default": {"default": "ऊपर नीचे दोनों और तीर"}}}, {"category": "So", "key": "2196", "mappings": {"default": {"default": "उत्तर पश्चिम तीर"}}}, {"category": "So", "key": "2197", "mappings": {"default": {"default": "उत्तर पूर्व तीर"}}}, {"category": "So", "key": "2198", "mappings": {"default": {"default": "दक्षिण पूर्व तीर"}}}, {"category": "So", "key": "2199", "mappings": {"default": {"default": "दक्षिण पश्चिम तीर"}}}, {"category": "Sm", "key": "219A", "mappings": {"default": {"default": "स्ट्रोक के साथ बायाँ तीर"}}}, {"category": "Sm", "key": "219B", "mappings": {"default": {"default": "स्ट्रोक के साथ दाईं ओर तीर"}}}, {"category": "So", "key": "219C", "mappings": {"default": {"default": "लेफ्टवर्ड वेव एरो"}}}, {"category": "So", "key": "219D", "mappings": {"default": {"default": "राइट वेव एरो"}}}, {"category": "So", "key": "219E", "mappings": {"default": {"default": "बाईं ओर दो सिर वाला तीर"}}}, {"category": "So", "key": "219F", "mappings": {"default": {"default": "ऊपर की ओर दो सिर वाला तीर"}}}, {"category": "Sm", "key": "21A0", "mappings": {"default": {"default": "दायीं ओर दो सिर वाला तीर"}}}, {"category": "So", "key": "21A1", "mappings": {"default": {"default": "नीचे की ओर दो सिर वाला तीर"}}}, {"category": "So", "key": "21A2", "mappings": {"default": {"default": "टेल के साथ बायाँ तीर"}}}, {"category": "Sm", "key": "21A3", "mappings": {"default": {"default": "टेल के साथ दाईं ओर तीर"}}}, {"category": "So", "key": "21A4", "mappings": {"default": {"default": "बार से बायाँ तीर"}}}, {"category": "So", "key": "21A5", "mappings": {"default": {"default": "बार से ऊपर की ओर तीर"}}}, {"category": "Sm", "key": "21A6", "mappings": {"default": {"default": "बार से दाहिना तीर"}}}, {"category": "So", "key": "21A7", "mappings": {"default": {"default": "बार से नीचे की ओर तीर"}}}, {"category": "So", "key": "21A8", "mappings": {"default": {"default": "बेस के साथ डाउन एरो"}}}, {"category": "So", "key": "21A9", "mappings": {"default": {"default": "हुक के साथ बायां तीर"}}}, {"category": "So", "key": "21AA", "mappings": {"default": {"default": "हुक के साथ दाईं ओर तीर"}}}, {"category": "So", "key": "21AB", "mappings": {"default": {"default": "लूप के साथ बायां तीर"}}}, {"category": "So", "key": "21AC", "mappings": {"default": {"default": "लूप के साथ दाईं ओर तीर"}}}, {"category": "So", "key": "21AD", "mappings": {"default": {"default": "लेफ्ट राइट वेव एरो"}}}, {"category": "Sm", "key": "21AE", "mappings": {"default": {"default": "स्ट्रोके साथ लेफ्ट राइट एरो"}}}, {"category": "So", "key": "21AF", "mappings": {"default": {"default": "नीचे की ओर ज़िगज़ैग एरो"}}}, {"category": "So", "key": "21B0", "mappings": {"default": {"default": "ऊपर की ओर तीर बाएं ओर की ओर"}}}, {"category": "So", "key": "21B1", "mappings": {"default": {"default": "ऊपर की ओर तीर ठीक से टिप के साथ"}}}, {"category": "So", "key": "21B2", "mappings": {"default": {"default": "नीचे की ओर तीर के साथ टिप बाईं ओर"}}}, {"category": "So", "key": "21B3", "mappings": {"default": {"default": "नीचे की ओर एरो टिप के साथ"}}}, {"category": "So", "key": "21B4", "mappings": {"default": {"default": "कॉर्नर डाउन के साथ एरोवर्ड"}}}, {"category": "So", "key": "21B5", "mappings": {"default": {"default": "कॉर्नर के साथ नीचे की तरफ एरो"}}}, {"category": "So", "key": "21B6", "mappings": {"default": {"default": "एंटीक्लॉकवाइज शीर्ष अर्धवृत्त तीर"}}}, {"category": "So", "key": "21B7", "mappings": {"default": {"default": "दक्षिणावर्त शीर्ष अर्धवृत्त तीर"}}}, {"category": "So", "key": "21B8", "mappings": {"default": {"default": "नॉर्थ वेस्ट एरो टू लॉन्ग बार"}}}, {"category": "So", "key": "21B9", "mappings": {"default": {"default": "बाईं ओर की ओर तीर की ओर बार की ओर दाईं ओर की ओर तीर"}}}, {"category": "So", "key": "21BA", "mappings": {"default": {"default": "एंटीलॉकवाइज ओपन सर्कल एरो"}}}, {"category": "So", "key": "21BB", "mappings": {"default": {"default": "क्लॉकवाइज ओपन सर्कल एरो"}}}, {"category": "So", "key": "21C4", "mappings": {"default": {"default": "दायीं ओर का तीर, बाएं ओर का तीर"}}}, {"category": "So", "key": "21C5", "mappings": {"default": {"default": "ऊपर की ओर तीर नीचे की ओर तीर की ओर"}}}, {"category": "So", "key": "21C6", "mappings": {"default": {"default": "बायाँ तीर आगे की ओर तीर"}}}, {"category": "So", "key": "21C7", "mappings": {"default": {"default": "बाईं ओर जोड़ीदार तीर"}}}, {"category": "So", "key": "21C8", "mappings": {"default": {"default": "ऊपर की ओर तीर चलाया"}}}, {"category": "So", "key": "21C9", "mappings": {"default": {"default": "दाईं ओर बाँध दिया तीर"}}}, {"category": "So", "key": "21CA", "mappings": {"default": {"default": "नीचे की ओर पेयरेड एरो"}}}, {"category": "So", "key": "21CD", "mappings": {"default": {"default": "स्ट्रॉक के साथ लेफ्टवर्ड डबल एरो"}}}, {"category": "Sm", "key": "21CE", "mappings": {"default": {"default": "स्ट्रोके साथ लेफ्ट राइट डबल एरो"}}}, {"category": "Sm", "key": "21CF", "mappings": {"default": {"default": "स्ट्रॉक के साथ राइट डबल एरो"}}}, {"category": "So", "key": "21D0", "mappings": {"default": {"default": "बायाँ डबल तीर"}}}, {"category": "So", "key": "21D1", "mappings": {"default": {"default": "ऊपर की ओर डबल ऐरो"}}}, {"category": "Sm", "key": "21D2", "mappings": {"default": {"default": "दायीं ओर डबल ऐरो"}}}, {"category": "So", "key": "21D3", "mappings": {"default": {"default": "नीचे की ओर डबल ऐरो"}}}, {"category": "Sm", "key": "21D4", "mappings": {"default": {"default": "लेफ्ट राइट डबल एरो"}}}, {"category": "So", "key": "21D5", "mappings": {"default": {"default": "ऊपर डबल तीर"}}}, {"category": "So", "key": "21D6", "mappings": {"default": {"default": "नॉर्थ वेस्ट डबल एरो"}}}, {"category": "So", "key": "21D7", "mappings": {"default": {"default": "नॉर्थ ईस्ट डबल एरो"}}}, {"category": "So", "key": "21D8", "mappings": {"default": {"default": "साउथ ईस्ट डबल एरो"}}}, {"category": "So", "key": "21D9", "mappings": {"default": {"default": "साउथ वेस्ट डबल एरो"}}}, {"category": "So", "key": "21DA", "mappings": {"default": {"default": "बायाँ ट्रिपल तीर"}}}, {"category": "So", "key": "21DB", "mappings": {"default": {"default": "दायीं ओर ट्रिपल ऐरो"}}}, {"category": "So", "key": "21DC", "mappings": {"default": {"default": "बाईं ओर स्क्विगल एरो"}}}, {"category": "So", "key": "21DD", "mappings": {"default": {"default": "दाहिना ओर स्क्विगल एरो"}}}, {"category": "So", "key": "21DE", "mappings": {"default": {"default": "डबल स्ट्रोक के साथ ऊपर की ओर तीर"}}}, {"category": "So", "key": "21DF", "mappings": {"default": {"default": "डबल स्ट्रोक के साथ नीचे की ओर तीर"}}}, {"category": "So", "key": "21E0", "mappings": {"default": {"default": "बायाँ धराशायी तीर"}}}, {"category": "So", "key": "21E1", "mappings": {"default": {"default": "ऊपर की ओर धंसा हुआ तीर"}}}, {"category": "So", "key": "21E2", "mappings": {"default": {"default": "दायीं ओर का तीर"}}}, {"category": "So", "key": "21E3", "mappings": {"default": {"default": "नीचे की ओर धंसा हुआ तीर"}}}, {"category": "So", "key": "21E4", "mappings": {"default": {"default": "बायाँ बाण बार"}}}, {"category": "So", "key": "21E5", "mappings": {"default": {"default": "दाईं ओर का तीर"}}}, {"category": "So", "key": "21E6", "mappings": {"default": {"default": "बाईं ओर सफेद तीर"}}}, {"category": "So", "key": "21E7", "mappings": {"default": {"default": "ऊपर की ओर सफेद तीर"}}}, {"category": "So", "key": "21E8", "mappings": {"default": {"default": "दाईं ओर सफेद तीर"}}}, {"category": "So", "key": "21E9", "mappings": {"default": {"default": "नीचे की ओर सफेद तीर"}}}, {"category": "So", "key": "21EA", "mappings": {"default": {"default": "ऊपर से सफेद तीर"}}}, {"category": "So", "key": "21EB", "mappings": {"default": {"default": "ऊपर की ओर सफेद तीर पेडस्टल पर"}}}, {"category": "So", "key": "21EC", "mappings": {"default": {"default": "क्षैतिज पट्टी के साथ पैदल की ओर सफेद तीर"}}}, {"category": "So", "key": "21ED", "mappings": {"default": {"default": "ऊपर की ओर सफेद तीर ऊर्ध्वाधर पट्टी के साथ पेडस्टल पर"}}}, {"category": "So", "key": "21EE", "mappings": {"default": {"default": "ऊपर की ओर सफेद डबल ऐरो"}}}, {"category": "So", "key": "21EF", "mappings": {"default": {"default": "पेडस्टल पर ऊपर की ओर सफेद डबल ऐरो"}}}, {"category": "So", "key": "21F0", "mappings": {"default": {"default": "दीवार से दाईं ओर सफेद तीर"}}}, {"category": "So", "key": "21F1", "mappings": {"default": {"default": "कॉर्नर पर नॉर्थ वेस्ट एरो"}}}, {"category": "So", "key": "21F2", "mappings": {"default": {"default": "कॉर्नर को साउथ ईस्ट एरो"}}}, {"category": "So", "key": "21F3", "mappings": {"default": {"default": "ऊपर नीचे सफेद तीर"}}}, {"category": "Sm", "key": "21F4", "mappings": {"default": {"default": "छोटे सर्कल के साथ राइट एरो"}}}, {"category": "Sm", "key": "21F5", "mappings": {"default": {"default": "नीचे की ओर तीर ऊपर की ओर तीर की ओर"}}}, {"category": "Sm", "key": "21F6", "mappings": {"default": {"default": "थ्री राइटवर्ड एरो"}}}, {"category": "Sm", "key": "21F7", "mappings": {"default": {"default": "ऊर्ध्वाधर स्ट्रोक के साथ बायाँ तीर"}}}, {"category": "Sm", "key": "21F8", "mappings": {"default": {"default": "वर्टिकल स्ट्रोक के साथ राइट एरो"}}}, {"category": "Sm", "key": "21F9", "mappings": {"default": {"default": "वर्टिकल स्ट्रोक के साथ लेफ्ट राइट एरो"}}}, {"category": "Sm", "key": "21FA", "mappings": {"default": {"default": "लेफ्टवर्ड एरो डबल वर्टिकल स्ट्रोक के साथ"}}}, {"category": "Sm", "key": "21FB", "mappings": {"default": {"default": "डबल वर्टिकल स्ट्रोक के साथ राइट एरो"}}}, {"category": "Sm", "key": "21FC", "mappings": {"default": {"default": "डबल वर्टिकल स्ट्रोक के साथ लेफ्ट राइट एरो"}}}, {"category": "Sm", "key": "21FD", "mappings": {"default": {"default": "बायाँ ओपन-हेडेड एरो"}}}, {"category": "Sm", "key": "21FE", "mappings": {"default": {"default": "राइट-ओपन-हेडेड एरो"}}}, {"category": "Sm", "key": "21FF", "mappings": {"default": {"default": "लेफ्ट राइट ओपन हेडेड ऐरो"}}}, {"category": "So", "key": "2301", "mappings": {"default": {"default": "विद्युत चिह्न"}}}, {"category": "So", "key": "2303", "mappings": {"default": {"default": "ऊपर तीर"}}}, {"category": "So", "key": "2304", "mappings": {"default": {"default": "नीचे तीर"}}}, {"category": "So", "key": "2324", "mappings": {"default": {"default": "दो क्षैतिज सलाखों के बीच ऊपर तीर"}}}, {"category": "So", "key": "238B", "mappings": {"default": {"default": "नॉर्थवेस्ट एरो के साथ टूटा हुआ सर्कल"}}}, {"category": "So", "key": "2794", "mappings": {"default": {"default": "हैवी वाइड-हेडेड राइट एरो"}}}, {"category": "So", "key": "2798", "mappings": {"default": {"default": "भारी दक्षिण पूर्व तीर"}}}, {"category": "So", "key": "2799", "mappings": {"default": {"default": "भारी दायाँ तीर"}}}, {"category": "So", "key": "279A", "mappings": {"default": {"default": "भारी नार्थ ईस्ट एरो"}}}, {"category": "So", "key": "279B", "mappings": {"default": {"default": "ड्राफ्टिंग प्वाइंट राइट एरो"}}}, {"category": "So", "key": "279C", "mappings": {"default": {"default": "हैवी राउंड-टिप्ड राइट एरो"}}}, {"category": "So", "key": "279D", "mappings": {"default": {"default": "त्रिभुज-हेडेड राइट एरो"}}}, {"category": "So", "key": "279E", "mappings": {"default": {"default": "हैवी ट्राइएंगल-हेडेड राइट एरो"}}}, {"category": "So", "key": "279F", "mappings": {"default": {"default": "धराशायी त्रिभुज-सिर दाहिना तीर"}}}, {"category": "So", "key": "27A0", "mappings": {"default": {"default": "हैवी डैश वाला ट्राइएंगल हेडेड राइट एरो"}}}, {"category": "So", "key": "27A1", "mappings": {"default": {"default": "ब्लैक राइट एरो"}}}, {"category": "So", "key": "27A2", "mappings": {"default": {"default": "थ्री-डी टॉप-लाइटेड राइट एरोहेड"}}}, {"category": "So", "key": "27A3", "mappings": {"default": {"default": "थ्री-डी बॉटम-लाइटेड राईटवर्ड्स एरोहेड"}}}, {"category": "So", "key": "27A4", "mappings": {"default": {"default": "ब्लैक राइटवर्ड एरोहेड"}}}, {"category": "So", "key": "27A5", "mappings": {"default": {"default": "हैवी ब्लैक कर्व्ड डाउन और राइट एरो"}}}, {"category": "So", "key": "27A6", "mappings": {"default": {"default": "भारी काले घुमावदार ऊपर की ओर और दाहिनी ओर तीर"}}}, {"category": "So", "key": "27A7", "mappings": {"default": {"default": "स्क्वाट ब्लैक राइट एरो"}}}, {"category": "So", "key": "27A8", "mappings": {"default": {"default": "हैवी कॉनक्लेव-पॉइंटेड ब्लैक राइट एरो"}}}, {"category": "So", "key": "27A9", "mappings": {"default": {"default": "राइट-शेडेड व्हाइट राइट एरो"}}}, {"category": "So", "key": "27AA", "mappings": {"default": {"default": "लेफ्ट-शेडेड व्हाइट राइट एरो"}}}, {"category": "So", "key": "27AB", "mappings": {"default": {"default": "बैक-टिल्टेड शैडो व्हाइट राइट एरो"}}}, {"category": "So", "key": "27AC", "mappings": {"default": {"default": "फ्रंट-टिल्डेड शैडो वाइट राइट एरो"}}}, {"category": "So", "key": "27AD", "mappings": {"default": {"default": "हैवी लोअर राइट-शैडेड व्हाइट राइट एरो"}}}, {"category": "So", "key": "27AE", "mappings": {"default": {"default": "हैवी अपर राइट-शैड वाइट राइट एरो"}}}, {"category": "So", "key": "27AF", "mappings": {"default": {"default": "नोकदार निचला दायाँ-छायादार सफेद दायाँ तीर"}}}, {"category": "So", "key": "27B1", "mappings": {"default": {"default": "नोकदार ऊपरी दाएं-छाया वाले सफेद दाएं तीर"}}}, {"category": "So", "key": "27B2", "mappings": {"default": {"default": "हैवी व्हाइट राइट एरो परिक्रमा की"}}}, {"category": "So", "key": "27B3", "mappings": {"default": {"default": "श्वेत-पंखयुक्त दाहिना तीर"}}}, {"category": "So", "key": "27B4", "mappings": {"default": {"default": "ब्लैक-फेदर साउथ ईस्ट एरो"}}}, {"category": "So", "key": "27B5", "mappings": {"default": {"default": "ब्लैक-फ़ेदरेड राइट एरो"}}}, {"category": "So", "key": "27B6", "mappings": {"default": {"default": "ब्लैक-फेदर नॉर्थ ईस्ट एरो"}}}, {"category": "So", "key": "27B7", "mappings": {"default": {"default": "भारी काले पंख वाले दक्षिण पूर्व तीर"}}}, {"category": "So", "key": "27B8", "mappings": {"default": {"default": "हैवी ब्लैक-फेदरेड राइट एरो"}}}, {"category": "So", "key": "27B9", "mappings": {"default": {"default": "हैवी ब्लैक-फेदर नॉर्थ ईस्ट एरो"}}}, {"category": "So", "key": "27BA", "mappings": {"default": {"default": "टियरड्रॉप-कांटेदार दाएं तीर"}}}, {"category": "So", "key": "27BB", "mappings": {"default": {"default": "भारी अश्रु-शंख दाहिनी ओर तीर"}}}, {"category": "So", "key": "27BC", "mappings": {"default": {"default": "वेज-टेल्ड राइट एरो"}}}, {"category": "So", "key": "27BD", "mappings": {"default": {"default": "हेवी वेज-टेल्ड राइट एरो"}}}, {"category": "So", "key": "27BE", "mappings": {"default": {"default": "ओपन-आउटवर्ड राइट एरो"}}}, {"category": "Sm", "key": "27F0", "mappings": {"default": {"default": "ऊपर की ओर चौगुनी तीर"}}}, {"category": "Sm", "key": "27F1", "mappings": {"default": {"default": "नीचे की ओर चौगुनी तीर"}}}, {"category": "Sm", "key": "27F2", "mappings": {"default": {"default": "एंटीलॉकवाइज गैप्ड सर्कल एरो"}}}, {"category": "Sm", "key": "27F3", "mappings": {"default": {"default": "क्लॉकवाइज गैप सर्कल एरो"}}}, {"category": "Sm", "key": "27F4", "mappings": {"default": {"default": "सर्किल प्लस के साथ राइट एरो"}}}, {"category": "Sm", "key": "27F5", "mappings": {"default": {"default": "लंबी बाईं ओर तीर"}}}, {"category": "Sm", "key": "27F6", "mappings": {"default": {"default": "लॉन्ग राइट एरो"}}}, {"category": "Sm", "key": "27F7", "mappings": {"default": {"default": "लंबी बाईं दाईं ओर तीर"}}}, {"category": "Sm", "key": "27F8", "mappings": {"default": {"default": "लंबी बाईं ओर डबल तीर"}}}, {"category": "Sm", "key": "27F9", "mappings": {"default": {"default": "लॉन्ग राइटवर्ड डबल एरो"}}}, {"category": "Sm", "key": "27FA", "mappings": {"default": {"default": "लॉन्ग लेफ्ट राइट डबल एरो"}}}, {"category": "Sm", "key": "27FB", "mappings": {"default": {"default": "बार से लंबी बाईं ओर तीर"}}}, {"category": "Sm", "key": "27FC", "mappings": {"default": {"default": "बार से लॉन्ग राइट एरो"}}}, {"category": "Sm", "key": "27FD", "mappings": {"default": {"default": "बार से लंबी बाईं ओर डबल एरो"}}}, {"category": "Sm", "key": "27FE", "mappings": {"default": {"default": "बार से लॉन्ग राइट डबल तीर"}}}, {"category": "Sm", "key": "27FF", "mappings": {"default": {"default": "लॉन्ग राइट साइड स्क्विगल एरो"}}}, {"category": "Sm", "key": "2900", "mappings": {"default": {"default": "वर्टिकल स्ट्रोक के साथ राइट-टू-हेडेड एरो"}}}, {"category": "Sm", "key": "2901", "mappings": {"default": {"default": "डबल वर्टिकल स्ट्रोक के साथ राइट टू हैड एरो"}}}, {"category": "Sm", "key": "2902", "mappings": {"default": {"default": "वर्टिकल स्ट्रोक के साथ लेफ्टवर्ड डबल एरो"}}}, {"category": "Sm", "key": "2903", "mappings": {"default": {"default": "वर्टिकल स्ट्रोक के साथ राइट डबल एरो"}}}, {"category": "Sm", "key": "2904", "mappings": {"default": {"default": "वर्टिकल स्ट्रोक के साथ लेफ्ट राइट डबल एरो"}}}, {"category": "Sm", "key": "2905", "mappings": {"default": {"default": "बार की ओर से दो-सिर वाला तीर"}}}, {"category": "Sm", "key": "2906", "mappings": {"default": {"default": "बार से लेफ्ट डबल एरो"}}}, {"category": "Sm", "key": "2907", "mappings": {"default": {"default": "बार से दाहिना डबल तीर"}}}, {"category": "Sm", "key": "2908", "mappings": {"default": {"default": "क्षैतिज स्ट्रोक के साथ नीचे की ओर तीर"}}}, {"category": "Sm", "key": "2909", "mappings": {"default": {"default": "क्षैतिज स्ट्रोक के साथ ऊपर की ओर तीर"}}}, {"category": "Sm", "key": "290A", "mappings": {"default": {"default": "ऊपर की ओर ट्रिपल ऐरो"}}}, {"category": "Sm", "key": "290B", "mappings": {"default": {"default": "नीचे की ओर ट्रिपल एरो"}}}, {"category": "Sm", "key": "290C", "mappings": {"default": {"default": "लेफ्टवर्ड डबल डैश एरो"}}}, {"category": "Sm", "key": "290D", "mappings": {"default": {"default": "दायाँ डबल डैश तीर"}}}, {"category": "Sm", "key": "290E", "mappings": {"default": {"default": "लेफ्टवर्ड ट्रिपल डैश एरो"}}}, {"category": "Sm", "key": "290F", "mappings": {"default": {"default": "दायीं ओर ट्रिपल डैश एरो"}}}, {"category": "Sm", "key": "2910", "mappings": {"default": {"default": "दायीं ओर दो-सिर वाला ट्रिपल डैश एरो"}}}, {"category": "Sm", "key": "2911", "mappings": {"default": {"default": "डॉटेड स्टेम के साथ राइट एरो"}}}, {"category": "Sm", "key": "2912", "mappings": {"default": {"default": "ऊपर की ओर तीर बार"}}}, {"category": "Sm", "key": "2913", "mappings": {"default": {"default": "नीचे की ओर एरो टू बार"}}}, {"category": "Sm", "key": "2914", "mappings": {"default": {"default": "वर्टिकल स्ट्रोक के साथ टेल के साथ राइट एरो"}}}, {"category": "Sm", "key": "2915", "mappings": {"default": {"default": "डबल वर्टिकल स्ट्रोक के साथ टेल के साथ राइट एरो"}}}, {"category": "Sm", "key": "2916", "mappings": {"default": {"default": "टेल के साथ राइट टू हेड-हेडेड एरो"}}}, {"category": "Sm", "key": "2917", "mappings": {"default": {"default": "वर्टिकल स्ट्रोक के साथ टेल के साथ राइट-हेडेड एरो"}}}, {"category": "Sm", "key": "2918", "mappings": {"default": {"default": "डबल वर्टिकल स्ट्रोक के साथ टेल के साथ राइट-हेडेड एरो"}}}, {"category": "Sm", "key": "2919", "mappings": {"default": {"default": "बायाँ तीर-पूँछ"}}}, {"category": "Sm", "key": "291A", "mappings": {"default": {"default": "एरोवर्ड-एरो-टेल"}}}, {"category": "Sm", "key": "291B", "mappings": {"default": {"default": "लेफ्टवर्ड डबल एरो-टेल"}}}, {"category": "Sm", "key": "291C", "mappings": {"default": {"default": "दाईं ओर डबल एरो-टेल"}}}, {"category": "Sm", "key": "291D", "mappings": {"default": {"default": "काले हीरे को बायां तीर"}}}, {"category": "Sm", "key": "291E", "mappings": {"default": {"default": "ब्लैक डायमंड को सही तीर"}}}, {"category": "Sm", "key": "291F", "mappings": {"default": {"default": "बार टू ब्लैक डायमंड से लेफ्ट एरो"}}}, {"category": "Sm", "key": "2920", "mappings": {"default": {"default": "बार से ब्लैक डायमंड तक एरोवर्ड"}}}, {"category": "Sm", "key": "2921", "mappings": {"default": {"default": "उत्तर पश्चिम और दक्षिण पूर्व तीर"}}}, {"category": "Sm", "key": "2922", "mappings": {"default": {"default": "नॉर्थ ईस्ट और साउथ वेस्ट एरो"}}}, {"category": "Sm", "key": "2923", "mappings": {"default": {"default": "हुक के साथ उत्तर पश्चिम तीर"}}}, {"category": "Sm", "key": "2924", "mappings": {"default": {"default": "हुक के साथ नॉर्थ ईस्ट एरो"}}}, {"category": "Sm", "key": "2925", "mappings": {"default": {"default": "हुक के साथ दक्षिण पूर्व तीर"}}}, {"category": "Sm", "key": "2926", "mappings": {"default": {"default": "हुक के साथ दक्षिण पश्चिम तीर"}}}, {"category": "Sm", "key": "2927", "mappings": {"default": {"default": "नॉर्थ वेस्ट एरो और नॉर्थ ईस्ट एरो"}}}, {"category": "Sm", "key": "2928", "mappings": {"default": {"default": "नॉर्थ ईस्ट एरो और साउथ ईस्ट एरो"}}}, {"category": "Sm", "key": "2929", "mappings": {"default": {"default": "दक्षिण पूर्व तीर और दक्षिण पश्चिम तीर"}}}, {"category": "Sm", "key": "292A", "mappings": {"default": {"default": "दक्षिण पश्चिम तीर और उत्तर पश्चिम तीर"}}}, {"category": "Sm", "key": "292D", "mappings": {"default": {"default": "साउथ ईस्ट एरो क्रॉसिंग नॉर्थ ईस्ट एरो"}}}, {"category": "Sm", "key": "292E", "mappings": {"default": {"default": "नॉर्थ ईस्ट एरो क्रॉसिंग साउथ ईस्ट एरो"}}}, {"category": "Sm", "key": "292F", "mappings": {"default": {"default": "गिरते हुए विकर्ण उत्तर पूर्व तीर को पार करते हुए"}}}, {"category": "Sm", "key": "2930", "mappings": {"default": {"default": "राइजिंग डायगोनल क्रॉसिंग साउथ ईस्ट एरो"}}}, {"category": "Sm", "key": "2931", "mappings": {"default": {"default": "नॉर्थ ईस्ट एरो क्रॉसिंग नॉर्थ वेस्ट एरो"}}}, {"category": "Sm", "key": "2932", "mappings": {"default": {"default": "नॉर्थ वेस्ट एरो क्रॉसिंग नॉर्थ ईस्ट एरो"}}}, {"category": "Sm", "key": "2933", "mappings": {"default": {"default": "वेव एरो पाइंटिंग डायरेक्टली राइट"}}}, {"category": "Sm", "key": "2934", "mappings": {"default": {"default": "तीर को ठीक से ऊपर की ओर इंगित करना और फिर ऊपर की ओर झुकना"}}}, {"category": "Sm", "key": "2935", "mappings": {"default": {"default": "एरो पाइंटिंग राइट साइड फिर कर्विंग डाउनवर्ड"}}}, {"category": "Sm", "key": "2936", "mappings": {"default": {"default": "तीर नीचे की ओर इशारा करते हुए फिर बाईं ओर घुमावदार"}}}, {"category": "Sm", "key": "2937", "mappings": {"default": {"default": "नीचे की ओर इशारा करते हुए एरो पॉइंटिंग करें"}}}, {"category": "Sm", "key": "2938", "mappings": {"default": {"default": "राइट-साइड आर्क क्लॉकवाइज एरो"}}}, {"category": "Sm", "key": "2939", "mappings": {"default": {"default": "लेफ्ट-साइड आर्क एंटीक्लॉकवाइज एरो"}}}, {"category": "Sm", "key": "293A", "mappings": {"default": {"default": "शीर्ष आर्क एंटिक्लवाइज एरो"}}}, {"category": "Sm", "key": "293B", "mappings": {"default": {"default": "निचला आर्क एंटिक्लवाइज एरो"}}}, {"category": "Sm", "key": "293C", "mappings": {"default": {"default": "माइनस के साथ शीर्ष आर्क दक्षिणावर्त तीर"}}}, {"category": "Sm", "key": "293D", "mappings": {"default": {"default": "प्लस के साथ शीर्ष आर्क एंटिक्लवाइज एरो"}}}, {"category": "Sm", "key": "293E", "mappings": {"default": {"default": "निचला दाया अर्धवृत्ताकार दक्षिणावर्त तीर"}}}, {"category": "Sm", "key": "293F", "mappings": {"default": {"default": "निचला बायाँ अर्धवृत्ताकार एंटीलॉकवाइज एरो"}}}, {"category": "Sm", "key": "2940", "mappings": {"default": {"default": "एंटीक्लॉकवाइज क्लोज्ड सर्कल एरो"}}}, {"category": "Sm", "key": "2941", "mappings": {"default": {"default": "क्लॉकवाइज क्लोज्ड सर्कल एरो"}}}, {"category": "Sm", "key": "2942", "mappings": {"default": {"default": "दायीं ओर एरो एबव शार्ट लेफ्टवर्ड एरो"}}}, {"category": "Sm", "key": "2943", "mappings": {"default": {"default": "लेफ्टवर्ड एरो एबव शॉर्ट शॉर्टवर्ड एरो"}}}, {"category": "Sm", "key": "2944", "mappings": {"default": {"default": "शॉर्ट राइट एरो एरोव एबव लेफ्टवर्ड एरो"}}}, {"category": "Sm", "key": "2945", "mappings": {"default": {"default": "इसके ठीक नीचे प्लस के साथ एरो है"}}}, {"category": "Sm", "key": "2946", "mappings": {"default": {"default": "प्लस नीचे के साथ बायाँ तीर"}}}, {"category": "Sm", "key": "2947", "mappings": {"default": {"default": "एक्स के माध्यम से राइट एरो"}}}, {"category": "Sm", "key": "2948", "mappings": {"default": {"default": "लेफ्ट राइट एरो स्मॉल सर्कल के जरिए"}}}, {"category": "Sm", "key": "2949", "mappings": {"default": {"default": "स्मॉल सर्कल से ऊपर की ओर दो सिर वाला तीर"}}}, {"category": "Sm", "key": "2970", "mappings": {"default": {"default": "राउंडेड हेड के साथ राइट डबल एरो"}}}, {"category": "Sm", "key": "2971", "mappings": {"default": {"default": "समतुल्य तीर के ऊपर साइन इन करें"}}}, {"category": "Sm", "key": "2972", "mappings": {"default": {"default": "राइट साइड एरो के ऊपर टिल्ड ऑपरेटर"}}}, {"category": "Sm", "key": "2973", "mappings": {"default": {"default": "टिल्डे ऑपरेटर से ऊपर की ओर तीर"}}}, {"category": "Sm", "key": "2974", "mappings": {"default": {"default": "टिल्डे ऑपरेटर के ठीक ऊपर तीर"}}}, {"category": "Sm", "key": "2975", "mappings": {"default": {"default": "राइट एरो एबव से ऊपर लगभग बराबर"}}}, {"category": "Sm", "key": "2976", "mappings": {"default": {"default": "लेफ्ट-एरो के ऊपर से कम"}}}, {"category": "Sm", "key": "2977", "mappings": {"default": {"default": "कम-से-कम के माध्यम से वामावर्त तीर"}}}, {"category": "Sm", "key": "2978", "mappings": {"default": {"default": "ग्रेटर-थान के ठीक ऊपर तीर"}}}, {"category": "Sm", "key": "2979", "mappings": {"default": {"default": "राइट साइड एरो के ऊपर सबसेट"}}}, {"category": "Sm", "key": "297A", "mappings": {"default": {"default": "बाएं ओर तीर सबसेट के माध्यम से"}}}, {"category": "Sm", "key": "297B", "mappings": {"default": {"default": "सुपरसेट एबव लेफ्टवर्ड एरो"}}}, {"category": "Sm", "key": "29B3", "mappings": {"default": {"default": "राइट एरो एबव के साथ खाली सेट"}}}, {"category": "Sm", "key": "29B4", "mappings": {"default": {"default": "लेफ्ट एरो एबव के साथ खाली सेट"}}}, {"category": "Sm", "key": "29BD", "mappings": {"default": {"default": "सर्कल के माध्यम से ऊपर तीर"}}}, {"category": "Sm", "key": "29EA", "mappings": {"default": {"default": "ब्लैक डायमंड विथ डाउन एरो"}}}, {"category": "Sm", "key": "29EC", "mappings": {"default": {"default": "डाउन एरो के साथ व्हाइट सर्कल"}}}, {"category": "Sm", "key": "29ED", "mappings": {"default": {"default": "डाउन एरो के साथ ब्लैक सर्कल"}}}, {"category": "Sm", "key": "2A17", "mappings": {"default": {"default": "हुक के साथ लेफ्टवर्ड एरो के साथ इंटीग्रल"}}}, {"category": "So", "key": "2B00", "mappings": {"default": {"default": "उत्तर पूर्व सफेद तीर"}}}, {"category": "So", "key": "2B01", "mappings": {"default": {"default": "उत्तर पश्चिम सफेद तीर"}}}, {"category": "So", "key": "2B02", "mappings": {"default": {"default": "दक्षिण पूर्व सफेद तीर"}}}, {"category": "So", "key": "2B03", "mappings": {"default": {"default": "दक्षिण पश्चिम सफेद तीर"}}}, {"category": "So", "key": "2B04", "mappings": {"default": {"default": "लेफ्ट राइट वाइट एरो"}}}, {"category": "So", "key": "2B05", "mappings": {"default": {"default": "काले तीर छोड़ दिया"}}}, {"category": "So", "key": "2B06", "mappings": {"default": {"default": "ऊपर की ओर काला तीर"}}}, {"category": "So", "key": "2B07", "mappings": {"default": {"default": "नीचे की ओर काले तीर"}}}, {"category": "So", "key": "2B08", "mappings": {"default": {"default": "नॉर्थ ईस्ट ब्लैक ऐरो"}}}, {"category": "So", "key": "2B09", "mappings": {"default": {"default": "नॉर्थ वेस्ट ब्लैक ऐरो"}}}, {"category": "So", "key": "2B0A", "mappings": {"default": {"default": "दक्षिण पूर्व काली तीर"}}}, {"category": "So", "key": "2B0B", "mappings": {"default": {"default": "साउथ वेस्ट ब्लैक ऐरो"}}}, {"category": "So", "key": "2B0C", "mappings": {"default": {"default": "लेफ्ट राइट ब्लैक ऐरो"}}}, {"category": "So", "key": "2B0D", "mappings": {"default": {"default": "अप डाउन ब्लैक ऐरो"}}}, {"category": "So", "key": "2B0E", "mappings": {"default": {"default": "नीचे की ओर टिप के साथ सही एरो"}}}, {"category": "So", "key": "2B0F", "mappings": {"default": {"default": "टिप अपवर्ड के साथ राइट एरो"}}}, {"category": "So", "key": "2B10", "mappings": {"default": {"default": "नीचे की ओर टिप के साथ बायाँ तीर"}}}, {"category": "So", "key": "2B11", "mappings": {"default": {"default": "टिप अपवर्ड के साथ लेफ्ट एरो"}}}, {"category": "Sm", "key": "2B30", "mappings": {"default": {"default": "छोटे वृत्त के साथ बायां तीर"}}}, {"category": "Sm", "key": "2B31", "mappings": {"default": {"default": "तीन वामपंथी तीर"}}}, {"category": "Sm", "key": "2B32", "mappings": {"default": {"default": "सर्किल प्लस के साथ लेफ्ट एरो"}}}, {"category": "Sm", "key": "2B33", "mappings": {"default": {"default": "लंबे समय तक बाईं ओर स्क्विगल एरो"}}}, {"category": "Sm", "key": "2B34", "mappings": {"default": {"default": "लेफ्टवर्ड टू-हेडेड एरो विथ वर्टिकल स्ट्रोक"}}}, {"category": "Sm", "key": "2B35", "mappings": {"default": {"default": "लेफ्टवर्ड टू-हेडेड एरो विथ डबल वर्टिकल स्ट्रोक"}}}, {"category": "Sm", "key": "2B36", "mappings": {"default": {"default": "बार से बाईं ओर दो-सिर वाला तीर"}}}, {"category": "Sm", "key": "2B37", "mappings": {"default": {"default": "लेफ्टवर्ड टू-हेडेड ट्रिपल डैश एरो"}}}, {"category": "Sm", "key": "2B38", "mappings": {"default": {"default": "डॉटेड स्टेम के साथ लेफ्टवर्ड एरो"}}}, {"category": "Sm", "key": "2B39", "mappings": {"default": {"default": "लेफ्टवर्ड एरो विथ टेल विथ वर्टिकल स्ट्रोक"}}}, {"category": "Sm", "key": "2B3A", "mappings": {"default": {"default": "डबल वर्टिकल स्ट्रोक के साथ टेल के साथ लेफ्टवर्ड एरो"}}}, {"category": "Sm", "key": "2B3B", "mappings": {"default": {"default": "टेल के साथ लेफ्टवर्ड टू-हेडेड एरो"}}}, {"category": "Sm", "key": "2B3C", "mappings": {"default": {"default": "लेफ्टवर्ड टू-हेडेड एरो विथ टेल विथ वर्टिकल स्ट्रोक"}}}, {"category": "Sm", "key": "2B3D", "mappings": {"default": {"default": "डबल वर्टिकल स्ट्रोक के साथ टेल के साथ लेफ्टवर्ड टू-हेडेड एरो"}}}, {"category": "Sm", "key": "2B3E", "mappings": {"default": {"default": "एक्स के माध्यम से बाईं ओर तीर"}}}, {"category": "Sm", "key": "2B3F", "mappings": {"default": {"default": "वेव एरो पॉइंटिंग सीधे लेफ्ट"}}}, {"category": "Sm", "key": "2B40", "mappings": {"default": {"default": "बायीं ओर तीर के बराबर चिह्न"}}}, {"category": "Sm", "key": "2B41", "mappings": {"default": {"default": "रिवर्स टिल्डे ऑपरेटर ऊपर की ओर बाईं ओर तीर"}}}, {"category": "Sm", "key": "2B42", "mappings": {"default": {"default": "लेफ्टवर्ड एरो ऊपर उल्टा लगभग बराबर"}}}, {"category": "Sm", "key": "2B43", "mappings": {"default": {"default": "ग्रेटर-थान के माध्यम से राइट एरो"}}}, {"category": "Sm", "key": "2B44", "mappings": {"default": {"default": "सुपरसेट के माध्यम से सही एरो"}}}, {"category": "So", "key": "2B45", "mappings": {"default": {"default": "बायाँ चौगुना तीर"}}}, {"category": "So", "key": "2B46", "mappings": {"default": {"default": "दाहिनी ओर चौगुना तीर"}}}, {"category": "Sm", "key": "2B47", "mappings": {"default": {"default": "राइट टिलर के ऊपर उल्टा टिल्ड ऑपरेटर"}}}, {"category": "Sm", "key": "2B48", "mappings": {"default": {"default": "राइट एरो एबव उलटा लगभग बराबर"}}}, {"category": "Sm", "key": "2B49", "mappings": {"default": {"default": "टिल्डे ऑपरेटर ऊपर की ओर लेफ्ट एरो"}}}, {"category": "Sm", "key": "2B4A", "mappings": {"default": {"default": "लेफ्टवर्ड एरो एबव एबव लगभग बराबर"}}}, {"category": "Sm", "key": "2B4B", "mappings": {"default": {"default": "लेफ्टवर्ड एरो से ऊपर रिवर्स टिल्ड ऑपरेटर"}}}, {"category": "Sm", "key": "2B4C", "mappings": {"default": {"default": "उल्टा तीर उल्टा टिल्ड ऑपरेटर"}}}, {"category": "Sm", "key": "FFE9", "mappings": {"default": {"default": "आधी-अधूरी बाण"}}}, {"category": "Sm", "key": "FFEA", "mappings": {"default": {"default": "अर्धव्यास ऊपर की ओर तीर"}}}, {"category": "Sm", "key": "FFEB", "mappings": {"default": {"default": "आधी अधूरी तीर"}}}, {"category": "Sm", "key": "FFEC", "mappings": {"default": {"default": "आधा नीचे तीर"}}}], "hi/symbols/math_characters.min": [{"locale": "hi"}, {"category": "Ll", "key": "2113", "mappings": {"default": {"default": "स्क्रिप्ट छोटा एल"}}}, {"category": "Sm", "key": "2118", "mappings": {"default": {"default": "स्क्रिप्ट कैपिटल पी"}}}, {"category": "Ll", "key": "213C", "mappings": {"default": {"default": "डबल-स्ट्रक स्मॉल पाई"}}}, {"category": "Ll", "key": "213D", "mappings": {"default": {"default": "डबल-स्ट्रक स्माल गामा"}}}, {"category": "<PERSON>", "key": "213E", "mappings": {"default": {"default": "डबल-स्ट्रक कैपिटल गामा"}}}, {"category": "<PERSON>", "key": "213F", "mappings": {"default": {"default": "डबल-स्ट्रक कैपिटल पाई"}}}, {"category": "Sm", "key": "2140", "mappings": {"default": {"default": "डबल-स्ट्रक एन-आर्य सारांश"}}}, {"category": "<PERSON>", "key": "2145", "mappings": {"default": {"default": "डबल-स्ट्रक इटैलिक कैपिटल डी"}}}, {"category": "Ll", "key": "2146", "mappings": {"default": {"default": "डबल-स्ट्रक इटैलिक स्मॉल डी"}}}, {"category": "Ll", "key": "2147", "mappings": {"default": {"default": "डबल-स्ट्रक इटैलिक स्मॉल ई"}}}, {"category": "Ll", "key": "2148", "mappings": {"default": {"default": "डबल-स्ट्रक इटैलिक स्मॉल आई"}}}, {"category": "Ll", "key": "2149", "mappings": {"default": {"default": "डबल-स्ट्रक इटैलिक स्मॉल जे"}}}, {"category": "Ll", "key": "1D6A4", "mappings": {"default": {"default": "गणितीय इटैलिक स्मॉल डॉटलेस I"}}}, {"category": "Ll", "key": "1D6A5", "mappings": {"default": {"default": "गणितीय इटैलिक स्मॉल डॉटलेस जे"}}}], "hi/symbols/math_delimiters.min": [{"locale": "hi"}, {"category": "Ps", "key": "0028", "mappings": {"default": {"default": "कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "0029", "mappings": {"default": {"default": "कोष्ठ अंत"}}}, {"category": "Ps", "key": "005B", "mappings": {"default": {"default": "चौकोर कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "005D", "mappings": {"default": {"default": "चौकोर कोष्ठ अंत"}}}, {"category": "Ps", "key": "007B", "mappings": {"default": {"default": "धनुः कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "007D", "mappings": {"default": {"default": "धनुः कोष्ठ अंत"}}}, {"category": "Ps", "key": "2045", "mappings": {"default": {"default": "कंटक कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "2046", "mappings": {"default": {"default": "कंटक कोष्ठ अंत"}}}, {"category": "Sm", "key": "2308", "mappings": {"default": {"default": "छत कोष्ठ आरंभ"}}}, {"category": "Sm", "key": "2309", "mappings": {"default": {"default": "छत कोष्ठ अंत"}}}, {"category": "Sm", "key": "230A", "mappings": {"default": {"default": "भूतल कोष्ठ आरंभ"}}}, {"category": "Sm", "key": "230B", "mappings": {"default": {"default": "भूतल कोष्ठ अंत"}}}, {"category": "So", "key": "230C", "mappings": {"default": {"default": "नीचे दांयी और स्थित काट"}}}, {"category": "So", "key": "230D", "mappings": {"default": {"default": "नीचे बांयी और स्थित काट"}}}, {"category": "So", "key": "230E", "mappings": {"default": {"default": "ऊपर दांयी और स्थित काट"}}}, {"category": "So", "key": "230F", "mappings": {"default": {"default": "ऊपर बांयी और स्थित काट"}}}, {"category": "So", "key": "231C", "mappings": {"default": {"default": "ऊपरी बायां कोना"}}}, {"category": "So", "key": "231D", "mappings": {"default": {"default": "ऊपरी दायां कोना"}}}, {"category": "So", "key": "231E", "mappings": {"default": {"default": "निचला बायां कोना"}}}, {"category": "So", "key": "231F", "mappings": {"default": {"default": "निचला दांया कोना"}}}, {"category": "Sm", "key": "2320", "mappings": {"default": {"default": "शीर्ष आधा अभिन्न"}}}, {"category": "Sm", "key": "2321", "mappings": {"default": {"default": "निचला आधा अभिन्न"}}}, {"category": "Ps", "key": "2329", "mappings": {"default": {"default": "वाम-संकेत कोण ब्रैकेट"}}}, {"category": "Pe", "key": "232A", "mappings": {"default": {"default": "राइट-पॉइंटिंग एंगल ब्रैकेट"}}}, {"category": "Sm", "key": "239B", "mappings": {"default": {"default": "बाएं कोष्ठक ऊपरी हुक"}}}, {"category": "Sm", "key": "239C", "mappings": {"default": {"default": "बाएं कोष्ठक का विस्तार"}}}, {"category": "Sm", "key": "239D", "mappings": {"default": {"default": "बाएं कोष्ठक निचला हुक"}}}, {"category": "Sm", "key": "239E", "mappings": {"default": {"default": "दायाँ कोष्ठक ऊपरी हुक"}}}, {"category": "Sm", "key": "239F", "mappings": {"default": {"default": "सही कोष्ठक विस्तार"}}}, {"category": "Sm", "key": "23A0", "mappings": {"default": {"default": "दायाँ कोष्ठक निचला हुक"}}}, {"category": "Sm", "key": "23A1", "mappings": {"default": {"default": "लेफ्ट स्क्वायर ब्रैकेट अपर कॉर्नर"}}}, {"category": "Sm", "key": "23A2", "mappings": {"default": {"default": "बायां वर्ग ब्रैकेट एक्सटेंशन"}}}, {"category": "Sm", "key": "23A3", "mappings": {"default": {"default": "लेफ्ट स्क्वायर ब्रैकेट लोअर कॉर्नर"}}}, {"category": "Sm", "key": "23A4", "mappings": {"default": {"default": "राइट स्क्वायर ब्रैकेट अपर कॉर्नर"}}}, {"category": "Sm", "key": "23A5", "mappings": {"default": {"default": "राइट स्क्वायर ब्रैकेट एक्सटेंशन"}}}, {"category": "Sm", "key": "23A6", "mappings": {"default": {"default": "राइट स्क्वायर ब्रैकेट लोअर कॉर्नर"}}}, {"category": "Sm", "key": "23A7", "mappings": {"default": {"default": "लेफ्ट कर्ली ब्रैकेट अपर हुक"}}}, {"category": "Sm", "key": "23A8", "mappings": {"default": {"default": "लेफ्ट कर्ली ब्रैकेट मध्य टुकड़ा"}}}, {"category": "Sm", "key": "23A9", "mappings": {"default": {"default": "लेफ्ट कर्ली ब्रैकेट लोअर हुक"}}}, {"category": "Sm", "key": "23AA", "mappings": {"default": {"default": "घुंघराले ब्रैकेट एक्सटेंशन"}}}, {"category": "Sm", "key": "23AB", "mappings": {"default": {"default": "राइट कर्ली ब्रैकेट अपर हुक"}}}, {"category": "Sm", "key": "23AC", "mappings": {"default": {"default": "राइट कर्ली ब्रैकेट मध्य टुकड़ा"}}}, {"category": "Sm", "key": "23AD", "mappings": {"default": {"default": "राइट कर्ली ब्रैकेट लोअर हुक"}}}, {"category": "Sm", "key": "23AE", "mappings": {"default": {"default": "एकात्म विस्तार"}}}, {"category": "Sm", "key": "23AF", "mappings": {"default": {"default": "क्षैतिज रेखा विस्तार"}}}, {"category": "Sm", "key": "23B0", "mappings": {"default": {"default": "अपर लेफ्ट या लोअर राइट कर्ली ब्रैकेट सेक्शन"}}}, {"category": "Sm", "key": "23B1", "mappings": {"default": {"default": "अपर राइट या लोअर लेफ्ट कर्ली ब्रैकेट सेक्शन"}}}, {"category": "Sm", "key": "23B2", "mappings": {"default": {"default": "संक्षेप शीर्ष"}}}, {"category": "Sm", "key": "23B3", "mappings": {"default": {"default": "संक्षेप नीचे"}}}, {"category": "So", "key": "23B4", "mappings": {"default": {"default": "शीर्ष चौकोर कोष्ठ"}}}, {"category": "So", "key": "23B5", "mappings": {"default": {"default": "निचला चौकोर कोष्ठ"}}}, {"category": "So", "key": "23B6", "mappings": {"default": {"default": "शीर्ष चौकोर कोष्ठ के ऊपर निचला चौकोर कोष्ठ"}}}, {"category": "So", "key": "23B7", "mappings": {"default": {"default": "मूलांक चिह्न"}}}, {"category": "So", "key": "23B8", "mappings": {"default": {"default": "बांयी ऊर्ध्वाधर रेखा"}}}, {"category": "So", "key": "23B9", "mappings": {"default": {"default": "दांयी ऊर्ध्वाधर रेखा"}}}, {"category": "Sm", "key": "23DC", "mappings": {"default": {"default": "शीर्ष कोष्ठ"}}}, {"category": "Sm", "key": "23DD", "mappings": {"default": {"default": "निचला कोष्ठ"}}}, {"category": "Sm", "key": "23DE", "mappings": {"default": {"default": "शीर्ष धनुः कोष्ठ"}}}, {"category": "Sm", "key": "23DF", "mappings": {"default": {"default": "निचला धनुः कोष्ठ"}}}, {"category": "Sm", "key": "23E0", "mappings": {"default": {"default": "शीर्ष कछुआ कोष्ठ"}}}, {"category": "Sm", "key": "23E1", "mappings": {"default": {"default": "निचला कछुआ कोष्ठ"}}}, {"category": "Ps", "key": "2768", "mappings": {"default": {"default": "कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "2769", "mappings": {"default": {"default": "कोष्ठ अंत"}}}, {"category": "Ps", "key": "276A", "mappings": {"default": {"default": "चपटा कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "276B", "mappings": {"default": {"default": "चपटा कोष्ठ अंत"}}}, {"category": "Ps", "key": "276C", "mappings": {"default": {"default": "कोण कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "276D", "mappings": {"default": {"default": "कोण कोष्ठ अंत"}}}, {"category": "Ps", "key": "276E", "mappings": {"default": {"default": "स्थूल कोण कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "276F", "mappings": {"default": {"default": "स्थूल कोण कोष्ठ अंत"}}}, {"category": "Ps", "key": "2770", "mappings": {"default": {"default": "स्थूल कोण कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "2771", "mappings": {"default": {"default": "स्थूल कोण कोष्ठ अंत"}}}, {"category": "Ps", "key": "2772", "mappings": {"default": {"default": "चपटा कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "2773", "mappings": {"default": {"default": "चपटा कोष्ठ अंत"}}}, {"category": "Ps", "key": "2774", "mappings": {"default": {"default": "धनुः कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "2775", "mappings": {"default": {"default": "धनुः कोष्ठ अंत"}}}, {"category": "Ps", "key": "27C5", "mappings": {"default": {"default": "लेफ्ट एस-शेप्ड बैग डिलिमिटर"}}}, {"category": "Pe", "key": "27C6", "mappings": {"default": {"default": "राइट एस-शेप्ड बैग डेलिमिटर"}}}, {"category": "Ps", "key": "27E6", "mappings": {"default": {"default": "चौकोर द्विकोष्ठ आरंभ"}}}, {"category": "Pe", "key": "27E7", "mappings": {"default": {"default": "चौकोर द्विकोष्ठ अंत"}}}, {"category": "Ps", "key": "27E8", "mappings": {"default": {"default": "कोण कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "27E9", "mappings": {"default": {"default": "कोण कोष्ठ अंत"}}}, {"category": "Ps", "key": "27EA", "mappings": {"default": {"default": "कोण द्विकोष्ठ आरंभ"}}}, {"category": "Pe", "key": "27EB", "mappings": {"default": {"default": "कोण द्विकोष्ठ अंत"}}}, {"category": "Ps", "key": "27EC", "mappings": {"default": {"default": "कछुआ द्विकोष्ठ आरंभ"}}}, {"category": "Pe", "key": "27ED", "mappings": {"default": {"default": "कछुआ द्विकोष्ठ अंत"}}}, {"category": "Ps", "key": "27EE", "mappings": {"default": {"default": "चपटा कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "27EF", "mappings": {"default": {"default": "चपटा कोष्ठ अंत"}}}, {"category": "Ps", "key": "2983", "mappings": {"default": {"default": "पोला धनुः कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "2984", "mappings": {"default": {"default": "पोला धनुः कोष्ठ अंत"}}}, {"category": "Ps", "key": "2985", "mappings": {"default": {"default": "पोला कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "2986", "mappings": {"default": {"default": "पोला कोष्ठ अंत"}}}, {"category": "Ps", "key": "2987", "mappings": {"default": {"default": "वृत्तखंडाकर कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "2988", "mappings": {"default": {"default": "जेड नोटेशन राइट इमेज ब्रैकेट"}}}, {"category": "Ps", "key": "2989", "mappings": {"default": {"default": "जेड नोटेशन लेफ्ट बाइंडिंग ब्रैकेट"}}}, {"category": "Pe", "key": "298A", "mappings": {"default": {"default": "जेड नोटेशन राइट बाइंडिंग ब्रैकेट"}}}, {"category": "Ps", "key": "298B", "mappings": {"default": {"default": "अंडरबार के साथ लेफ्ट स्क्वायर ब्रैकेट"}}}, {"category": "Pe", "key": "298C", "mappings": {"default": {"default": "अंडरबार के साथ राइट स्क्वायर ब्रैकेट"}}}, {"category": "Ps", "key": "298D", "mappings": {"default": {"default": "टॉप कॉर्नर में टिक के साथ लेफ्ट स्क्वायर ब्रैकेट"}}}, {"category": "Pe", "key": "298E", "mappings": {"default": {"default": "नीचे कोने में टिक के साथ राइट स्क्वायर ब्रैकेट"}}}, {"category": "Ps", "key": "298F", "mappings": {"default": {"default": "निचले कोने में टिक के साथ लेफ्ट स्क्वायर ब्रैकेट"}}}, {"category": "Pe", "key": "2990", "mappings": {"default": {"default": "टॉप कॉर्नर में टिक के साथ राइट स्क्वायर ब्रैकेट"}}}, {"category": "Ps", "key": "2991", "mappings": {"default": {"default": "डॉट के साथ लेफ्ट एंगल ब्रैकेट"}}}, {"category": "Pe", "key": "2992", "mappings": {"default": {"default": "डॉट के साथ राइट एंगल ब्रैकेट"}}}, {"category": "Ps", "key": "2993", "mappings": {"default": {"default": "बाएं चाप कम-से-कम ब्रैकेट"}}}, {"category": "Pe", "key": "2994", "mappings": {"default": {"default": "सही आर्क ग्रेटर-थ्रैड ब्रैकेट"}}}, {"category": "Ps", "key": "2995", "mappings": {"default": {"default": "डबल लेफ्ट आर्क ग्रेटर-थान ब्रैकेट"}}}, {"category": "Pe", "key": "2996", "mappings": {"default": {"default": "डबल सही आर्क कम-से-कम ब्रैकेट"}}}, {"category": "Ps", "key": "2997", "mappings": {"default": {"default": "वाम काले कछुए खोल ब्रैकेट"}}}, {"category": "Pe", "key": "2998", "mappings": {"default": {"default": "राइट ब्लैक कछुआ खोल ब्रैकेट"}}}, {"category": "Ps", "key": "29D8", "mappings": {"default": {"default": "वाम विगली बाड़"}}}, {"category": "Pe", "key": "29D9", "mappings": {"default": {"default": "राइट विगली फेंस"}}}, {"category": "Ps", "key": "29DA", "mappings": {"default": {"default": "वाम डबल विगेंस बाड़"}}}, {"category": "Pe", "key": "29DB", "mappings": {"default": {"default": "राइट डबल विगली फेंस"}}}, {"category": "Ps", "key": "29FC", "mappings": {"default": {"default": "बाईं ओर की ओर घुमावदार एंगल ब्रैकेट"}}}, {"category": "Pe", "key": "29FD", "mappings": {"default": {"default": "राइट-पॉइंटिंग कर्व्ड एंगल ब्रैकेट"}}}, {"category": "Ps", "key": "2E22", "mappings": {"default": {"default": "टॉप लेफ्ट हाफ ब्रैकेट"}}}, {"category": "Pe", "key": "2E23", "mappings": {"default": {"default": "टॉप राइट हाफ ब्रैकेट"}}}, {"category": "Ps", "key": "2E24", "mappings": {"default": {"default": "बॉटम लेफ्ट हाफ ब्रैकेट"}}}, {"category": "Pe", "key": "2E25", "mappings": {"default": {"default": "बॉटम राइट हाफ ब्रैकेट"}}}, {"category": "Ps", "key": "2E26", "mappings": {"default": {"default": "लेफ्ट सिड्यूज यू ब्रैकेट"}}}, {"category": "Pe", "key": "2E27", "mappings": {"default": {"default": "राइट सिड्यूज यू ब्रैकेट"}}}, {"category": "Ps", "key": "2E28", "mappings": {"default": {"default": "द्विकोष्ठ आरंभ"}}}, {"category": "Pe", "key": "2E29", "mappings": {"default": {"default": "द्विकोष्ठ अंत"}}}, {"category": "Ps", "key": "3008", "mappings": {"default": {"default": "कोण कोष्ठ आरंभ"}}}, {"category": "Pe", "key": "3009", "mappings": {"default": {"default": "कोण कोष्ठ अंत"}}}, {"category": "Ps", "key": "300A", "mappings": {"default": {"default": "कोण द्विकोष्ठ आरंभ"}}}, {"category": "Pe", "key": "300B", "mappings": {"default": {"default": "कोण द्विकोष्ठ अंत"}}}, {"category": "Ps", "key": "300C", "mappings": {"default": {"default": "लेफ्ट कार्नर ब्रैकेट"}}}, {"category": "Pe", "key": "300D", "mappings": {"default": {"default": "राइट कॉर्नर ब्रैकेट"}}}, {"category": "Ps", "key": "300E", "mappings": {"default": {"default": "लेफ्ट व्हाइट कॉर्नर ब्रैकेट"}}}, {"category": "Pe", "key": "300F", "mappings": {"default": {"default": "राइट व्हाइट कॉर्नर ब्रैकेट"}}}, {"category": "Ps", "key": "3010", "mappings": {"default": {"default": "लेफ्ट ब्लैक लेंटिकुलर ब्रैकेट"}}}, {"category": "Pe", "key": "3011", "mappings": {"default": {"default": "राइट ब्लैक लेंटिकुलर ब्रैकेट"}}}, {"category": "Ps", "key": "3014", "mappings": {"default": {"default": "वाम कछुआ खोल ब्रैकेट"}}}, {"category": "Pe", "key": "3015", "mappings": {"default": {"default": "सही कछुआ खोल ब्रैकेट"}}}, {"category": "Ps", "key": "3016", "mappings": {"default": {"default": "लेफ्ट व्हाइट लेंटिकुलर ब्रैकेट"}}}, {"category": "Pe", "key": "3017", "mappings": {"default": {"default": "राइट व्हाइट लेंटिकुलर ब्रैकेट"}}}, {"category": "Ps", "key": "3018", "mappings": {"default": {"default": "वाम व्हाइट कछुआ खोल ब्रैकेट"}}}, {"category": "Pe", "key": "3019", "mappings": {"default": {"default": "सही सफेद कछुआ खोल ब्रैकेट"}}}, {"category": "Ps", "key": "301A", "mappings": {"default": {"default": "लेफ्ट व्हाइट स्क्वायर ब्रैकेट"}}}, {"category": "Pe", "key": "301B", "mappings": {"default": {"default": "सही सफेद वर्ग ब्रैकेट"}}}, {"category": "Ps", "key": "301D", "mappings": {"default": {"default": "उलटा डबल प्राइम कोटेशन मार्क"}}}, {"category": "Pe", "key": "301E", "mappings": {"default": {"default": "डबल प्राइम कोटेशन मार्क"}}}, {"category": "Pe", "key": "301F", "mappings": {"default": {"default": "लो डबल प्राइम कोटेशन मार्क"}}}, {"category": "Ps", "key": "FD3E", "mappings": {"default": {"default": "अलंकृत वाम कोष्ठक"}}}, {"category": "Pe", "key": "FD3F", "mappings": {"default": {"default": "अलंकृत सही कोष्ठक"}}}, {"category": "Ps", "key": "FE17", "mappings": {"default": {"default": "वर्टिकल लेफ्ट व्हाइट लेंटिकुलर ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Pe", "key": "FE18", "mappings": {"default": {"default": "वर्टिकल राइट व्हाइट लेंटिकुलर ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Ps", "key": "FE35", "mappings": {"default": {"default": "वर्टिकल लेफ्ट पेरेंटेसिस के लिए प्रस्तुति फॉर्म"}}}, {"category": "Pe", "key": "FE36", "mappings": {"default": {"default": "वर्टिकल राइट कोष्ठक के लिए प्रस्तुति प्रपत्र"}}}, {"category": "Ps", "key": "FE37", "mappings": {"default": {"default": "वर्टिकल लेफ्ट कर्ली ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Pe", "key": "FE38", "mappings": {"default": {"default": "वर्टिकल राइट कर्ली ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Ps", "key": "FE39", "mappings": {"default": {"default": "वर्टिकल लेफ्ट कछुआ शेल ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Pe", "key": "FE3A", "mappings": {"default": {"default": "वर्टिकल राइट कछुआ शेल ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Ps", "key": "FE3B", "mappings": {"default": {"default": "वर्टिकल लेफ्ट ब्लैक लेंटिकुलर ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Pe", "key": "FE3C", "mappings": {"default": {"default": "वर्टिकल राइट ब्लैक लेंटिकुलर ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Ps", "key": "FE3D", "mappings": {"default": {"default": "वर्टिकल लेफ्ट डबल एंगल ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Pe", "key": "FE3E", "mappings": {"default": {"default": "वर्टिकल राइट डबल एंगल ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Ps", "key": "FE3F", "mappings": {"default": {"default": "वर्टिकल लेफ्ट एंगल ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Pe", "key": "FE40", "mappings": {"default": {"default": "वर्टिकल राइट एंगल ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Ps", "key": "FE41", "mappings": {"default": {"default": "वर्टिकल लेफ्ट कॉर्नर ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Pe", "key": "FE42", "mappings": {"default": {"default": "वर्टिकल राइट कॉर्नर ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Ps", "key": "FE43", "mappings": {"default": {"default": "वर्टिकल लेफ्ट व्हाइट कॉर्नर ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Pe", "key": "FE44", "mappings": {"default": {"default": "वर्टिकल राइट व्हाइट कॉर्नर ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Ps", "key": "FE47", "mappings": {"default": {"default": "वर्टिकल लेफ्ट स्क्वायर ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Pe", "key": "FE48", "mappings": {"default": {"default": "वर्टिकल राइट स्क्वायर ब्रैकेट के लिए प्रस्तुति फॉर्म"}}}, {"category": "Ps", "key": "FE59", "mappings": {"default": {"default": "लघु वाम कोष्ठक"}}}, {"category": "Pe", "key": "FE5A", "mappings": {"default": {"default": "लघु दाहिनी कोष्ठक"}}}, {"category": "Ps", "key": "FE5B", "mappings": {"default": {"default": "छोटा लेफ्ट कर्ली ब्रैकेट"}}}, {"category": "Pe", "key": "FE5C", "mappings": {"default": {"default": "छोटी सी सही घुंघराले ब्रैकेट"}}}, {"category": "Ps", "key": "FE5D", "mappings": {"default": {"default": "लघु वाम कछुआ खोल ब्रैकेट"}}}, {"category": "Pe", "key": "FE5E", "mappings": {"default": {"default": "छोटे सही कछुआ खोल ब्रैकेट"}}}, {"category": "Ps", "key": "FF08", "mappings": {"default": {"default": "पूर्ण विराम वाम कोष्ठक"}}}, {"category": "Pe", "key": "FF09", "mappings": {"default": {"default": "पूर्ण विचलन सही कोष्ठक"}}}, {"category": "Ps", "key": "FF3B", "mappings": {"default": {"default": "फुल एक्सपोर्मेंट लेफ्ट स्क्वायर ब्रैकेट"}}}, {"category": "Pe", "key": "FF3D", "mappings": {"default": {"default": "पूर्णविराम राइट स्क्वायर ब्रैकेट"}}}, {"category": "Ps", "key": "FF5B", "mappings": {"default": {"default": "फुल एक्सपोर्मेंट लेफ्ट कर्ली ब्रैकेट"}}}, {"category": "Pe", "key": "FF5D", "mappings": {"default": {"default": "फ़ुल विड्थ राइट कर्ली ब्रैकेट"}}}, {"category": "Ps", "key": "FF5F", "mappings": {"default": {"default": "फुल विदाउट लेफ्ट व्हाइट पेरेंटेसिस"}}}, {"category": "Pe", "key": "FF60", "mappings": {"default": {"default": "फ़ुल विसरोफ़ राइट व्हाइट कोष्ठक"}}}, {"category": "Ps", "key": "FF62", "mappings": {"default": {"default": "हाफ साइड लेफ्ट कॉर्नर ब्रैकेट"}}}, {"category": "Pe", "key": "FF63", "mappings": {"default": {"default": "हाफ एक्सपोजर राइट कॉर्नर ब्रैकेट"}}}], "hi/symbols/math_geometry.min": [{"locale": "hi"}, {"category": "So", "key": "2500", "mappings": {"default": {"default": "बॉक्स चित्र प्रकाश क्षैतिज"}}}, {"category": "So", "key": "2501", "mappings": {"default": {"default": "बॉक्स चित्र भारी क्षैतिज"}}}, {"category": "So", "key": "2502", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट वर्टिकल"}}}, {"category": "So", "key": "2503", "mappings": {"default": {"default": "बॉक्स चित्र भारी कार्यक्षेत्र"}}}, {"category": "So", "key": "2504", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट ट्रिपल डैश क्षैतिज"}}}, {"category": "So", "key": "2505", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हैवी ट्रिपल डैश क्षैतिज"}}}, {"category": "So", "key": "2506", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट ट्रिपल डैश वर्टिकल"}}}, {"category": "So", "key": "2507", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हेवी ट्रिपल डैश वर्टिकल"}}}, {"category": "So", "key": "2508", "mappings": {"default": {"default": "बॉक्स चित्र प्रकाश चौगुनी डैश क्षैतिज"}}}, {"category": "So", "key": "2509", "mappings": {"default": {"default": "बॉक्स चित्र भारी चौगुनी डैश क्षैतिज"}}}, {"category": "So", "key": "250A", "mappings": {"default": {"default": "बॉक्स चित्र प्रकाश चौगुनी डैश कार्यक्षेत्र"}}}, {"category": "So", "key": "250B", "mappings": {"default": {"default": "बॉक्स चित्र भारी चौगुनी डैश कार्यक्षेत्र"}}}, {"category": "So", "key": "250C", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट डाउन और राइट"}}}, {"category": "So", "key": "250D", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट एंड राइट हैवी"}}}, {"category": "So", "key": "250E", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डाउन हैवी और राइट लाइट"}}}, {"category": "So", "key": "250F", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हैवी डाउन और राइट"}}}, {"category": "So", "key": "2510", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट डाउन और लेफ्ट"}}}, {"category": "So", "key": "2511", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट एंड लेफ्ट हैवी"}}}, {"category": "So", "key": "2512", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डाउन हैवी और लेफ्ट लाइट"}}}, {"category": "So", "key": "2513", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हैवी डाउन और लेफ्ट"}}}, {"category": "So", "key": "2514", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट अप और राइट"}}}, {"category": "So", "key": "2515", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग अप लाइट एंड राइट हैवी"}}}, {"category": "So", "key": "2516", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग अप हैवी और राइट लाइट"}}}, {"category": "So", "key": "2517", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हैवी अप और राइट"}}}, {"category": "So", "key": "2518", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट अप और लेफ्ट"}}}, {"category": "So", "key": "2519", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग अप लाइट एंड लेफ्ट हैवी"}}}, {"category": "So", "key": "251A", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हैवी और लेफ्ट लाइट"}}}, {"category": "So", "key": "251B", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हैवी अप और लेफ्ट"}}}, {"category": "So", "key": "251C", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट वर्टिकल और राइट"}}}, {"category": "So", "key": "251D", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग वर्टिकल लाइट और राइट हैवी"}}}, {"category": "So", "key": "251E", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग अप हैवी और राइट डाउन लाइट"}}}, {"category": "So", "key": "251F", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डाउन हैवी और राइट अप लाइट"}}}, {"category": "So", "key": "2520", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग वर्टिकल हैवी और राइट लाइट"}}}, {"category": "So", "key": "2521", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट और राइट अप हैवी"}}}, {"category": "So", "key": "2522", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग अप लाइट एंड राइट डाउन हैवी"}}}, {"category": "So", "key": "2523", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हैवी वर्टिकल और राइट"}}}, {"category": "So", "key": "2524", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट वर्टिकल और लेफ्ट"}}}, {"category": "So", "key": "2525", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग वर्टिकल लाइट और लेफ्ट हैवी"}}}, {"category": "So", "key": "2526", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग अप हैवी और लेफ्ट डाउन लाइट"}}}, {"category": "So", "key": "2527", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डाउन हैवी और लेफ्ट अप लाइट"}}}, {"category": "So", "key": "2528", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग वर्टिकल हैवी और लेफ्ट लाइट"}}}, {"category": "So", "key": "2529", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डाउन लाइट और लेफ्ट अप हैवी"}}}, {"category": "So", "key": "252A", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट एंड लेफ्ट डाउन हैवी"}}}, {"category": "So", "key": "252B", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हैवी वर्टिकल और लेफ्ट"}}}, {"category": "So", "key": "252C", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट डाउन और हॉरिजॉन्टल"}}}, {"category": "So", "key": "252D", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लेफ्ट हैवी और राइट डाउन लाइट"}}}, {"category": "So", "key": "252E", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग राइट हैवी और लेफ्ट डाउन लाइट"}}}, {"category": "So", "key": "252F", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट एंड हॉरिज़ॉन्टल हैवी"}}}, {"category": "So", "key": "2530", "mappings": {"default": {"default": "भारी और क्षैतिज प्रकाश नीचे बॉक्स चित्र"}}}, {"category": "So", "key": "2531", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग राइट लाइट और लेफ्ट डाउन हैवी"}}}, {"category": "So", "key": "2532", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लेफ्ट लाइट और राइट डाउन हैवी"}}}, {"category": "So", "key": "2533", "mappings": {"default": {"default": "बॉक्स चित्र भारी नीचे और क्षैतिज"}}}, {"category": "So", "key": "2534", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट अप और क्षैतिज"}}}, {"category": "So", "key": "2535", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लेफ्ट हैवी और राइट अप लाइट"}}}, {"category": "So", "key": "2536", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग राइट हैवी और लेफ्ट अप लाइट"}}}, {"category": "So", "key": "2537", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग अप लाइट एंड हॉरिज़ॉन्टल हैवी"}}}, {"category": "So", "key": "2538", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग भारी और क्षैतिज प्रकाश"}}}, {"category": "So", "key": "2539", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग राइट लाइट और लेफ्ट अप हैवी"}}}, {"category": "So", "key": "253A", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लेफ्ट लाइट और राइट अप हैवी"}}}, {"category": "So", "key": "253B", "mappings": {"default": {"default": "बॉक्स चित्र भारी और क्षैतिज"}}}, {"category": "So", "key": "253C", "mappings": {"default": {"default": "बॉक्स चित्र हल्की ऊर्ध्वाधर और क्षैतिज"}}}, {"category": "So", "key": "253D", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लेफ्ट हैवी और राइट वर्टिकल लाइट"}}}, {"category": "So", "key": "253E", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग राइट हैवी और लेफ्ट वर्टिकल लाइट"}}}, {"category": "So", "key": "253F", "mappings": {"default": {"default": "बॉक्स चित्र ऊर्ध्वाधर प्रकाश और क्षैतिज भारी"}}}, {"category": "So", "key": "2540", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग अप हैवी और डाउन हॉरिज़ॉन्टल लाइट"}}}, {"category": "So", "key": "2541", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग नीचे भारी और ऊपर क्षैतिज प्रकाश"}}}, {"category": "So", "key": "2542", "mappings": {"default": {"default": "बॉक्स चित्र ऊर्ध्वाधर भारी और क्षैतिज प्रकाश"}}}, {"category": "So", "key": "2543", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लेफ्ट अप हैवी और राइट डाउन लाइट"}}}, {"category": "So", "key": "2544", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग राइट हैवी और लेफ्ट डाउन लाइट"}}}, {"category": "So", "key": "2545", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लेफ्ट डाउन हैवी और राइट अप लाइट"}}}, {"category": "So", "key": "2546", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग राइट हैवी और लेफ्ट अप लाइट"}}}, {"category": "So", "key": "2547", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट एंड अप हॉरिज़ॉन्टल हैवी"}}}, {"category": "So", "key": "2548", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग अप लाइट एंड डाउन हॉरिजॉन्टल हैवी"}}}, {"category": "So", "key": "2549", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग राइट लाइट और लेफ्ट वर्टिकल हैवी"}}}, {"category": "So", "key": "254A", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लेफ्ट लाइट और राइट वर्टिकल हैवी"}}}, {"category": "So", "key": "254B", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हैवी वर्टिकल और हॉरिजॉन्टल"}}}, {"category": "So", "key": "254C", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट डबल डैश क्षैतिज"}}}, {"category": "So", "key": "254D", "mappings": {"default": {"default": "बॉक्स चित्र भारी डबल डैश क्षैतिज"}}}, {"category": "So", "key": "254E", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट डबल डैश वर्टिकल"}}}, {"category": "So", "key": "254F", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हैवी डबल डैश वर्टिकल"}}}, {"category": "So", "key": "2550", "mappings": {"default": {"default": "बॉक्स आरेख डबल क्षैतिज"}}}, {"category": "So", "key": "2551", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डबल वर्टिकल"}}}, {"category": "So", "key": "2552", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग सिंगल और राइट डबल"}}}, {"category": "So", "key": "2553", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डाउन डबल और राइट सिंगल"}}}, {"category": "So", "key": "2554", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डबल डाउन और राइट"}}}, {"category": "So", "key": "2555", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग सिंगल और लेफ्ट डबल"}}}, {"category": "So", "key": "2556", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डाउन डबल और लेफ्ट सिंगल"}}}, {"category": "So", "key": "2557", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डबल डाउन और लेफ्ट"}}}, {"category": "So", "key": "2558", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग अप सिंगल और राइट डबल"}}}, {"category": "So", "key": "2559", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग अप डबल और राइट सिंगल"}}}, {"category": "So", "key": "255A", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डबल अप और राइट"}}}, {"category": "So", "key": "255B", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग अप सिंगल और लेफ्ट डबल"}}}, {"category": "So", "key": "255C", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग अप डबल और लेफ्ट सिंगल"}}}, {"category": "So", "key": "255D", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डबल अप और लेफ्ट"}}}, {"category": "So", "key": "255E", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग वर्टिकल सिंगल और राइट डबल"}}}, {"category": "So", "key": "255F", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग वर्टिकल डबल और राइट सिंगल"}}}, {"category": "So", "key": "2560", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डबल वर्टिकल और राइट"}}}, {"category": "So", "key": "2561", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग वर्टिकल सिंगल और लेफ्ट डबल"}}}, {"category": "So", "key": "2562", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग वर्टिकल डबल और लेफ्ट सिंगल"}}}, {"category": "So", "key": "2563", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डबल वर्टिकल और लेफ्ट"}}}, {"category": "So", "key": "2564", "mappings": {"default": {"default": "एकल और क्षैतिज डबल नीचे बॉक्स चित्र"}}}, {"category": "So", "key": "2565", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डाउन डबल और हॉरिजॉन्टल सिंगल"}}}, {"category": "So", "key": "2566", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डबल डाउन और हॉरिजॉन्टल"}}}, {"category": "So", "key": "2567", "mappings": {"default": {"default": "एकल और क्षैतिज डबल ऊपर बॉक्स चित्र"}}}, {"category": "So", "key": "2568", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग अप डबल और हॉरिज़ॉन्टल सिंगल"}}}, {"category": "So", "key": "2569", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डबल अप और हॉरिजॉन्टल"}}}, {"category": "So", "key": "256A", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग वर्टिकल सिंगल और हॉरिजॉन्टल डबल"}}}, {"category": "So", "key": "256B", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग वर्टिकल डबल और हॉरिजॉन्टल सिंगल"}}}, {"category": "So", "key": "256C", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग डबल वर्टिकल और हॉरिजॉन्टल"}}}, {"category": "So", "key": "256D", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट आर्क डाउन और राइट"}}}, {"category": "So", "key": "256E", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट आर्क डाउन और लेफ्ट"}}}, {"category": "So", "key": "256F", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट आर्क अप एंड लेफ्ट"}}}, {"category": "So", "key": "2570", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट आर्क अप और राइट"}}}, {"category": "So", "key": "2571", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट विकर्ण ऊपरी दाएं से निचले बाएँ"}}}, {"category": "So", "key": "2572", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट विकर्ण ऊपरी बाएं निचले हिस्से में"}}}, {"category": "So", "key": "2573", "mappings": {"default": {"default": "बॉक्स चित्र प्रकाश विकर्ण क्रॉस"}}}, {"category": "So", "key": "2574", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट लेफ्ट"}}}, {"category": "So", "key": "2575", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट अप"}}}, {"category": "So", "key": "2576", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट राइट"}}}, {"category": "So", "key": "2577", "mappings": {"default": {"default": "बॉक्स चित्र नीचे प्रकाश"}}}, {"category": "So", "key": "2578", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हेवी लेफ्ट"}}}, {"category": "So", "key": "2579", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हैवी अप"}}}, {"category": "So", "key": "257A", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हैवी राइट"}}}, {"category": "So", "key": "257B", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग भारी नीचे"}}}, {"category": "So", "key": "257C", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट लेफ्ट और हेवी राइट"}}}, {"category": "So", "key": "257D", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग लाइट अप और हैवी डाउन"}}}, {"category": "So", "key": "257E", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हैवी लेफ्ट और लाइट राइट"}}}, {"category": "So", "key": "257F", "mappings": {"default": {"default": "बॉक्स ड्रॉइंग हैवी अप और लाइट डाउन"}}}, {"category": "So", "key": "2580", "mappings": {"default": {"default": "अपर हाफ ब्लॉक"}}}, {"category": "So", "key": "2581", "mappings": {"default": {"default": "लोअर वन आठवां ब्लॉक"}}}, {"category": "So", "key": "2582", "mappings": {"default": {"default": "लोअर वन क्वार्टर ब्लॉक"}}}, {"category": "So", "key": "2583", "mappings": {"default": {"default": "लोअर थ्री ईगेट्स ब्लॉक"}}}, {"category": "So", "key": "2584", "mappings": {"default": {"default": "लोअर हाफ ब्लॉक"}}}, {"category": "So", "key": "2585", "mappings": {"default": {"default": "लोअर फाइव इग्थ्स ब्लॉक"}}}, {"category": "So", "key": "2586", "mappings": {"default": {"default": "लोअर थ्री क्वार्टर ब्लॉक"}}}, {"category": "So", "key": "2587", "mappings": {"default": {"default": "लोअर सेवन एग्थ्स ब्लॉक"}}}, {"category": "So", "key": "2588", "mappings": {"default": {"default": "पूर्ण ब्लॉक"}}}, {"category": "So", "key": "2589", "mappings": {"default": {"default": "लेफ्ट सेवन इग्थ्स ब्लॉक"}}}, {"category": "So", "key": "258A", "mappings": {"default": {"default": "लेफ्ट थ्री क्वार्टर ब्लॉक"}}}, {"category": "So", "key": "258B", "mappings": {"default": {"default": "लेफ्ट फाइव इग्थ्स ब्लॉक"}}}, {"category": "So", "key": "258C", "mappings": {"default": {"default": "लेफ्ट हाफ ब्लॉक"}}}, {"category": "So", "key": "258D", "mappings": {"default": {"default": "लेफ्ट थ्री ईगेट्स ब्लॉक"}}}, {"category": "So", "key": "258E", "mappings": {"default": {"default": "लेफ्ट वन क्वार्टर ब्लॉक"}}}, {"category": "So", "key": "258F", "mappings": {"default": {"default": "लेफ्ट वन आठवां ब्लॉक"}}}, {"category": "So", "key": "2590", "mappings": {"default": {"default": "राइट हाफ ब्लॉक"}}}, {"category": "So", "key": "2591", "mappings": {"default": {"default": "हल्का धुंधला"}}}, {"category": "So", "key": "2592", "mappings": {"default": {"default": "मध्यम छाया"}}}, {"category": "So", "key": "2593", "mappings": {"default": {"default": "डार्क शेड"}}}, {"category": "So", "key": "2594", "mappings": {"default": {"default": "ऊपरी एक आठवां ब्लॉक"}}}, {"category": "So", "key": "2595", "mappings": {"default": {"default": "राइट वन आठवां ब्लॉक"}}}, {"category": "So", "key": "2596", "mappings": {"default": {"default": "चतुर्थांश निचला बाएँ"}}}, {"category": "So", "key": "2597", "mappings": {"default": {"default": "चतुर्थांश निचला अधिकार"}}}, {"category": "So", "key": "2598", "mappings": {"default": {"default": "चतुर्थांश अपर वाम"}}}, {"category": "So", "key": "2599", "mappings": {"default": {"default": "चतुर्थांश ऊपरी बाएँ और निचले बाएँ और निचले दाएँ"}}}, {"category": "So", "key": "259A", "mappings": {"default": {"default": "चतुर्थांश ऊपरी बाएँ और निचले दाएँ"}}}, {"category": "So", "key": "259B", "mappings": {"default": {"default": "चतुर्थांश ऊपरी बाएँ और ऊपरी दाएँ और निचले बाएँ"}}}, {"category": "So", "key": "259C", "mappings": {"default": {"default": "चतुर्थांश ऊपरी बाएँ और ऊपरी दाएँ और निचले दाएँ"}}}, {"category": "So", "key": "259D", "mappings": {"default": {"default": "चतुष्कोण ऊपरी दायां"}}}, {"category": "So", "key": "259E", "mappings": {"default": {"default": "चतुर्थांश ऊपरी दाएँ और निचले बाएँ"}}}, {"category": "So", "key": "259F", "mappings": {"default": {"default": "चतुर्थांश ऊपरी दाएँ और निचले बाएँ और निचले दाएँ"}}}, {"category": "So", "key": "25A0", "mappings": {"default": {"default": "काला वर्ग"}}}, {"category": "So", "key": "25A1", "mappings": {"default": {"default": "सफेद वर्ग"}}}, {"category": "So", "key": "25A2", "mappings": {"default": {"default": "गोल कोनों के साथ सफेद वर्ग"}}}, {"category": "So", "key": "25A3", "mappings": {"default": {"default": "व्हाइट स्क्वायर युक्त ब्लैक स्मॉल स्क्वायर"}}}, {"category": "So", "key": "25A4", "mappings": {"default": {"default": "क्षैतिज भरण के साथ वर्ग"}}}, {"category": "So", "key": "25A5", "mappings": {"default": {"default": "वर्टिकल फिल के साथ स्क्वायर"}}}, {"category": "So", "key": "25A6", "mappings": {"default": {"default": "ऑर्थोगोनल क्रॉसचैट फिल के साथ स्क्वायर"}}}, {"category": "So", "key": "25A7", "mappings": {"default": {"default": "अपर लेफ्ट टू लोअर राइट फिल के साथ स्क्वायर"}}}, {"category": "So", "key": "25A8", "mappings": {"default": {"default": "ऊपरी दाएं से निचले बाएँ भरण के लिए वर्ग"}}}, {"category": "So", "key": "25A9", "mappings": {"default": {"default": "विकर्ण क्रॉसचैट भराव के साथ वर्ग"}}}, {"category": "So", "key": "25AA", "mappings": {"default": {"default": "काले छोटे वर्ग"}}}, {"category": "So", "key": "25AB", "mappings": {"default": {"default": "सफेद छोटा वर्ग"}}}, {"category": "So", "key": "25AC", "mappings": {"default": {"default": "काला आयत"}}}, {"category": "So", "key": "25AD", "mappings": {"default": {"default": "सफेद आयत"}}}, {"category": "So", "key": "25AE", "mappings": {"default": {"default": "ब्लैक वर्टिकल आयत"}}}, {"category": "So", "key": "25AF", "mappings": {"default": {"default": "सफेद लंबवत आयत"}}}, {"category": "So", "key": "25B0", "mappings": {"default": {"default": "काले समानांतर चतुर्भुज"}}}, {"category": "So", "key": "25B1", "mappings": {"default": {"default": "श्वेत वर्णमाला"}}}, {"category": "So", "key": "25B2", "mappings": {"default": {"default": "ब्लैक अप-पॉइंटिंग ट्राइएंगल"}}}, {"category": "So", "key": "25B3", "mappings": {"default": {"default": "व्हाइट अप-पॉइंटिंग ट्राइएंगल"}}}, {"category": "So", "key": "25B4", "mappings": {"default": {"default": "ब्लैक अप-पॉइंटिंग स्माल ट्राएंगल"}}}, {"category": "So", "key": "25B5", "mappings": {"default": {"default": "व्हाइट अप-पॉइंटिंग स्माल ट्राएंगल"}}}, {"category": "So", "key": "25B6", "mappings": {"default": {"default": "ब्लैक राइट-पॉइंटिंग ट्राइएंगल"}}}, {"category": "Sm", "key": "25B7", "mappings": {"default": {"default": "व्हाइट राइट-पॉइंटिंग ट्राइएंगल"}}}, {"category": "So", "key": "25B8", "mappings": {"default": {"default": "ब्लैक राइट-पॉइंटिंग स्माल ट्राएंगल"}}}, {"category": "So", "key": "25B9", "mappings": {"default": {"default": "व्हाइट राइट-पॉइंटिंग स्माल ट्राएंगल"}}}, {"category": "So", "key": "25BA", "mappings": {"default": {"default": "ब्लैक राइट-पॉइंटिंग पॉइंटर"}}}, {"category": "So", "key": "25BB", "mappings": {"default": {"default": "व्हाइट राइट-पॉइंटिंग पॉइंटर"}}}, {"category": "So", "key": "25BC", "mappings": {"default": {"default": "ब्लैक डाउन-पॉइंटिंग ट्राइएंगल"}}}, {"category": "So", "key": "25BD", "mappings": {"default": {"default": "व्हाइट डाउन-पॉइंटिंग ट्राइएंगल"}}}, {"category": "So", "key": "25BE", "mappings": {"default": {"default": "ब्लैक डाउन-पॉइंटिंग स्माल ट्राएंगल"}}}, {"category": "So", "key": "25BF", "mappings": {"default": {"default": "व्हाइट डाउन-पॉइंटिंग स्माल ट्राएंगल"}}}, {"category": "So", "key": "25C0", "mappings": {"default": {"default": "ब्लैक लेफ्ट-पॉइंटिंग ट्राइएंगल"}}}, {"category": "Sm", "key": "25C1", "mappings": {"default": {"default": "व्हाइट लेफ्ट-पॉइंटिंग ट्राइएंगल"}}}, {"category": "So", "key": "25C2", "mappings": {"default": {"default": "ब्लैक लेफ्ट-पॉइंटिंग स्मॉल ट्राएंगल"}}}, {"category": "So", "key": "25C3", "mappings": {"default": {"default": "सफेद वाम-इंगित छोटे त्रिभुज"}}}, {"category": "So", "key": "25C4", "mappings": {"default": {"default": "ब्लैक लेफ्ट-पॉइंटिंग पॉइंटर"}}}, {"category": "So", "key": "25C5", "mappings": {"default": {"default": "व्हाइट लेफ्ट-पॉइंटिंग पॉइंटर"}}}, {"category": "So", "key": "25C6", "mappings": {"default": {"default": "काला हीरा"}}}, {"category": "So", "key": "25C7", "mappings": {"default": {"default": "सफेद हीरा"}}}, {"category": "So", "key": "25C8", "mappings": {"default": {"default": "व्हाइट डायमंड ब्लैक ब्लैक डायमंड से युक्त"}}}, {"category": "So", "key": "25C9", "mappings": {"default": {"default": "fisheye"}}}, {"category": "So", "key": "25CA", "mappings": {"default": {"default": "विषमकोण"}}}, {"category": "So", "key": "25CB", "mappings": {"default": {"default": "सफेद घेरा"}}}, {"category": "So", "key": "25CC", "mappings": {"default": {"default": "बिंदीदार घेरा"}}}, {"category": "So", "key": "25CD", "mappings": {"default": {"default": "सर्कल को वर्टिकल फिल के साथ"}}}, {"category": "So", "key": "25CE", "mappings": {"default": {"default": "बुल्सआई"}}}, {"category": "So", "key": "25CF", "mappings": {"default": {"default": "ब्लैक सर्कल"}}}, {"category": "So", "key": "25D0", "mappings": {"default": {"default": "सर्किल लेफ्ट हाफ ब्लैक के साथ"}}}, {"category": "So", "key": "25D1", "mappings": {"default": {"default": "सर्कल के साथ सही आधा काला"}}}, {"category": "So", "key": "25D2", "mappings": {"default": {"default": "सर्किल लोअर हाफ ब्लैक के साथ"}}}, {"category": "So", "key": "25D3", "mappings": {"default": {"default": "अपर हाफ ब्लैक के साथ सर्कल"}}}, {"category": "So", "key": "25D4", "mappings": {"default": {"default": "अपर राइट क्वाड्रेंट ब्लैक वाला सर्कल"}}}, {"category": "So", "key": "25D5", "mappings": {"default": {"default": "सर्किल ऑल बट अपर लेफ्ट क्वाड्रेंट ब्लैक"}}}, {"category": "So", "key": "25D6", "mappings": {"default": {"default": "लेफ्ट हाफ ब्लैक सर्कल"}}}, {"category": "So", "key": "25D7", "mappings": {"default": {"default": "राइट हाफ ब्लैक सर्कल"}}}, {"category": "So", "key": "25D8", "mappings": {"default": {"default": "उलटा बुलेट"}}}, {"category": "So", "key": "25D9", "mappings": {"default": {"default": "उलटा व्हाइट सर्कल"}}}, {"category": "So", "key": "25DA", "mappings": {"default": {"default": "ऊपरी आधा उलटा सफेद सर्कल"}}}, {"category": "So", "key": "25DB", "mappings": {"default": {"default": "निचला आधा उलटा सफेद सर्कल"}}}, {"category": "So", "key": "25DC", "mappings": {"default": {"default": "अपर लेफ्ट क्वाड्रेंट सर्कुलर आर्क"}}}, {"category": "So", "key": "25DD", "mappings": {"default": {"default": "अपर राइट क्वाड्रंट सर्कुलर आर्क"}}}, {"category": "So", "key": "25DE", "mappings": {"default": {"default": "लोअर राइट क्वाड्रंट सर्कुलर आर्क"}}}, {"category": "So", "key": "25DF", "mappings": {"default": {"default": "लोअर लेफ्ट क्वाड्रेंट सर्कुलर आर्क"}}}, {"category": "So", "key": "25E0", "mappings": {"default": {"default": "अपर हाफ सर्कल"}}}, {"category": "So", "key": "25E1", "mappings": {"default": {"default": "निचला आधा वृत्त"}}}, {"category": "So", "key": "25E2", "mappings": {"default": {"default": "ब्लैक लोअर राइट ट्रायंगल"}}}, {"category": "So", "key": "25E3", "mappings": {"default": {"default": "ब्लैक लोअर लेफ्ट ट्रायंगल"}}}, {"category": "So", "key": "25E4", "mappings": {"default": {"default": "ब्लैक अपर लेफ्ट ट्रायंगल"}}}, {"category": "So", "key": "25E5", "mappings": {"default": {"default": "ब्लैक अपर राइट ट्रायंगल"}}}, {"category": "So", "key": "25E6", "mappings": {"default": {"default": "सफेद गोली"}}}, {"category": "So", "key": "25E7", "mappings": {"default": {"default": "लेफ्ट हाफ ब्लैक के साथ स्क्वायर"}}}, {"category": "So", "key": "25E8", "mappings": {"default": {"default": "स्क्वायर विथ राइट हाफ ब्लैक"}}}, {"category": "So", "key": "25E9", "mappings": {"default": {"default": "ऊपरी बाएं विकर्ण आधा काला के साथ वर्ग"}}}, {"category": "So", "key": "25EA", "mappings": {"default": {"default": "लोअर राइट विकर्ण हाफ ब्लैक के साथ स्क्वायर"}}}, {"category": "So", "key": "25EB", "mappings": {"default": {"default": "ऊर्ध्वाधर वर्ग रेखा के साथ सफेद वर्ग"}}}, {"category": "So", "key": "25EC", "mappings": {"default": {"default": "व्हाइट अप-पॉइंटिंग ट्राएंगल विद डॉट"}}}, {"category": "So", "key": "25ED", "mappings": {"default": {"default": "अप-पॉइन्टिंग ट्रायंगल विथ लेफ्ट हाफ ब्लैक"}}}, {"category": "So", "key": "25EE", "mappings": {"default": {"default": "राइट-हाफ ब्लैक के साथ अप-पॉइंटिंग ट्राएंगल"}}}, {"category": "So", "key": "25EF", "mappings": {"default": {"default": "बड़ा वृत्त"}}}, {"category": "So", "key": "25F0", "mappings": {"default": {"default": "ऊपरी बाएं चतुर्भुज के साथ सफेद वर्ग"}}}, {"category": "So", "key": "25F1", "mappings": {"default": {"default": "निचले बाएँ वृत्त का चतुर्थ भाग के साथ सफेद वर्ग"}}}, {"category": "So", "key": "25F2", "mappings": {"default": {"default": "लोअर राइट क्वाड्रंट के साथ व्हाइट स्क्वायर"}}}, {"category": "So", "key": "25F3", "mappings": {"default": {"default": "ऊपरी दाएं चतुर्थांश के साथ सफेद वर्ग"}}}, {"category": "So", "key": "25F4", "mappings": {"default": {"default": "ऊपरी बाएँ वृत्त का चतुर्थ भाग के साथ सफेद वृत्त"}}}, {"category": "So", "key": "25F5", "mappings": {"default": {"default": "लोअर लेफ्ट क्वाड्रेंट के साथ व्हाइट सर्कल"}}}, {"category": "So", "key": "25F6", "mappings": {"default": {"default": "व्हाइट सर्किल लोअर राइट क्वाड्रंट के साथ"}}}, {"category": "So", "key": "25F7", "mappings": {"default": {"default": "ऊपरी दाएं चतुर्थांश के साथ सफेद वृत्त"}}}, {"category": "Sm", "key": "25F8", "mappings": {"default": {"default": "ऊपरी बाएँ त्रिभुज"}}}, {"category": "Sm", "key": "25F9", "mappings": {"default": {"default": "ऊपरी दाएँ त्रिभुज"}}}, {"category": "Sm", "key": "25FA", "mappings": {"default": {"default": "निचला बाएँ त्रिभुज"}}}, {"category": "Sm", "key": "25FB", "mappings": {"default": {"default": "सफेद मध्यम वर्ग"}}}, {"category": "Sm", "key": "25FC", "mappings": {"default": {"default": "काला मध्यम वर्ग"}}}, {"category": "Sm", "key": "25FD", "mappings": {"default": {"default": "सफेद मध्यम छोटा वर्ग"}}}, {"category": "Sm", "key": "25FE", "mappings": {"default": {"default": "ब्लैक मीडियम स्मॉल स्क्वायर"}}}, {"category": "Sm", "key": "25FF", "mappings": {"default": {"default": "निचला दायाँ त्रिभुज"}}}, {"category": "So", "key": "2B12", "mappings": {"default": {"default": "टॉप हाफ ब्लैक के साथ स्क्वायर"}}}, {"category": "So", "key": "2B13", "mappings": {"default": {"default": "बॉटम हाफ ब्लैक के साथ स्क्वायर"}}}, {"category": "So", "key": "2B14", "mappings": {"default": {"default": "ऊपरी दाएं विकर्ण आधा काला के साथ वर्ग"}}}, {"category": "So", "key": "2B15", "mappings": {"default": {"default": "लोअर लेफ्ट विकर्ण हाफ ब्लैक के साथ स्क्वायर"}}}, {"category": "So", "key": "2B16", "mappings": {"default": {"default": "लेफ्ट हाफ ब्लैक के साथ डायमंड"}}}, {"category": "So", "key": "2B17", "mappings": {"default": {"default": "राइट हाफ ब्लैक के साथ डायमंड"}}}, {"category": "So", "key": "2B18", "mappings": {"default": {"default": "डायमंड टॉप हाफ ब्लैक के साथ"}}}, {"category": "So", "key": "2B19", "mappings": {"default": {"default": "बॉटम हाफ ब्लैक के साथ डायमंड"}}}, {"category": "So", "key": "2B1A", "mappings": {"default": {"default": "बिंदीदार वर्ग"}}}, {"category": "So", "key": "2B1B", "mappings": {"default": {"default": "काले बड़े वर्ग"}}}, {"category": "So", "key": "2B1C", "mappings": {"default": {"default": "सफेद बड़ा वर्ग"}}}, {"category": "So", "key": "2B1D", "mappings": {"default": {"default": "काले बहुत छोटे वर्ग"}}}, {"category": "So", "key": "2B1E", "mappings": {"default": {"default": "सफेद बहुत छोटा वर्ग"}}}, {"category": "So", "key": "2B1F", "mappings": {"default": {"default": "ब्लैक पेंटागन"}}}, {"category": "So", "key": "2B20", "mappings": {"default": {"default": "सफेद पेंटागन"}}}, {"category": "So", "key": "2B21", "mappings": {"default": {"default": "सफेद षट्कोण"}}}, {"category": "So", "key": "2B22", "mappings": {"default": {"default": "काला षट्कोण"}}}, {"category": "So", "key": "2B23", "mappings": {"default": {"default": "क्षैतिज काले षट्कोण"}}}, {"category": "So", "key": "2B24", "mappings": {"default": {"default": "ब्लैक लार्ज सर्कल"}}}, {"category": "So", "key": "2B25", "mappings": {"default": {"default": "ब्लैक मीडियम डायमंड"}}}, {"category": "So", "key": "2B26", "mappings": {"default": {"default": "सफेद मध्यम हीरा"}}}, {"category": "So", "key": "2B27", "mappings": {"default": {"default": "ब्लैक मीडियम लोज़ेंज"}}}, {"category": "So", "key": "2B28", "mappings": {"default": {"default": "व्हाइट मीडियम लोज़ेंज"}}}, {"category": "So", "key": "2B29", "mappings": {"default": {"default": "काला छोटा हीरा"}}}, {"category": "So", "key": "2B2A", "mappings": {"default": {"default": "ब्लैक स्मॉल लोजेंज"}}}, {"category": "So", "key": "2B2B", "mappings": {"default": {"default": "सफेद छोटा लोजेंज"}}}, {"category": "So", "key": "2B2C", "mappings": {"default": {"default": "काला क्षैतिज दीर्घवृत्त"}}}, {"category": "So", "key": "2B2D", "mappings": {"default": {"default": "सफेद क्षैतिज दीर्घवृत्त"}}}, {"category": "So", "key": "2B2E", "mappings": {"default": {"default": "ब्लैक वर्टिकल इलिप्स"}}}, {"category": "So", "key": "2B2F", "mappings": {"default": {"default": "व्हाइट वर्टिकल इलिप्स"}}}, {"category": "So", "key": "2B50", "mappings": {"default": {"default": "व्हाइट मीडियम स्टार"}}}, {"category": "So", "key": "2B51", "mappings": {"default": {"default": "ब्लैक स्माल स्टार"}}}, {"category": "So", "key": "2B52", "mappings": {"default": {"default": "सफेद छोटा तारा"}}}, {"category": "So", "key": "2B53", "mappings": {"default": {"default": "ब्लैक राइट-पॉइंटिंग पेंटागन"}}}, {"category": "So", "key": "2B54", "mappings": {"default": {"default": "व्हाइट राइट-पॉइंटिंग पेंटागन"}}}, {"category": "So", "key": "2B55", "mappings": {"default": {"default": "भारी बड़ा घेरा"}}}, {"category": "So", "key": "2B56", "mappings": {"default": {"default": "ओवल इनसाइड के साथ हैवी ओवल"}}}, {"category": "So", "key": "2B57", "mappings": {"default": {"default": "सर्किल के अंदर हैवी सर्किल"}}}, {"category": "So", "key": "2B58", "mappings": {"default": {"default": "भारी घेरा"}}}, {"category": "So", "key": "2B59", "mappings": {"default": {"default": "भारी गोलाकार नमकीन"}}}], "hi/symbols/math_harpoons.min": [{"locale": "hi"}, {"category": "So", "key": "21BC", "mappings": {"default": {"default": "बारबेक उपर की तरफ लेफ्ट हार्पून"}}}, {"category": "So", "key": "21BD", "mappings": {"default": {"default": "बार्बड डाउनवर्ड के साथ बायीं ओर हार्पून"}}}, {"category": "So", "key": "21BE", "mappings": {"default": {"default": "ऊपर की ओर बारब के साथ हार्पून"}}}, {"category": "So", "key": "21BF", "mappings": {"default": {"default": "ऊपर की तरफ बारब लेफ्ट के साथ हार्पून"}}}, {"category": "So", "key": "21C0", "mappings": {"default": {"default": "बार्ब अपवर्ड के साथ राइट हर्पून"}}}, {"category": "So", "key": "21C1", "mappings": {"default": {"default": "नीचे की ओर बारब के साथ हर्पून"}}}, {"category": "So", "key": "21C2", "mappings": {"default": {"default": "नीचे की ओर बारब के साथ हार्पून"}}}, {"category": "So", "key": "21C3", "mappings": {"default": {"default": "नीचे की ओर बार्ब के साथ हार्पून"}}}, {"category": "So", "key": "21CB", "mappings": {"default": {"default": "बाएं ओर हार्पून ओवर राइट हार्पून"}}}, {"category": "So", "key": "21CC", "mappings": {"default": {"default": "दायीं ओर हार्पून ओवर लेफ्टवर्ड हार्पून"}}}, {"category": "Sm", "key": "294A", "mappings": {"default": {"default": "बाएं बार्ब अप राईट बार्ब डाउन हार्पून"}}}, {"category": "Sm", "key": "294B", "mappings": {"default": {"default": "लेफ्ट बार्ब डाउन राइट बारब अप हारपून"}}}, {"category": "Sm", "key": "294C", "mappings": {"default": {"default": "ऊपर कंटिया राइट डाउन बारब लेफ्ट हार्पून"}}}, {"category": "Sm", "key": "294D", "mappings": {"default": {"default": "ऊपर बारब लेफ्ट डाउन बारब राइट हार्पून"}}}, {"category": "Sm", "key": "294E", "mappings": {"default": {"default": "लेफ्ट बार्ब अप अप राइट बार अप हार्पून"}}}, {"category": "Sm", "key": "294F", "mappings": {"default": {"default": "ऊपर कंटिया राइट डाउन बारब राइट हार्पून"}}}, {"category": "Sm", "key": "2950", "mappings": {"default": {"default": "लेफ्ट बार्ब डाउन राइट बार बार डाउन हार्पून"}}}, {"category": "Sm", "key": "2951", "mappings": {"default": {"default": "ऊपर बार्ब लेफ्ट डाउन बारब लेफ्ट हार्पून"}}}, {"category": "Sm", "key": "2952", "mappings": {"default": {"default": "बारबड अप टू बार के साथ लेफ्टवर्ड हार्पून"}}}, {"category": "Sm", "key": "2953", "mappings": {"default": {"default": "बारबेक अप टू बार के साथ सही हर्पून"}}}, {"category": "Sm", "key": "2954", "mappings": {"default": {"default": "ऊपर की ओर बारब के साथ हार्पून बार"}}}, {"category": "Sm", "key": "2955", "mappings": {"default": {"default": "नीचे की ओर बारप बार के साथ हार्पून"}}}, {"category": "Sm", "key": "2956", "mappings": {"default": {"default": "बारबड डाउन टू बार के साथ लेफ्टवर्ड हार्पून"}}}, {"category": "Sm", "key": "2957", "mappings": {"default": {"default": "बारबड डाउन टू बार के साथ दाहिना हार्पून"}}}, {"category": "Sm", "key": "2958", "mappings": {"default": {"default": "ऊपर की ओर बारप लेफ्ट बार के साथ हार्पून"}}}, {"category": "Sm", "key": "2959", "mappings": {"default": {"default": "नीचे की ओर बारप लेफ्ट बार के साथ हार्पून"}}}, {"category": "Sm", "key": "295A", "mappings": {"default": {"default": "बारवर्ड से बारप के साथ लेफ्ट हार्पून"}}}, {"category": "Sm", "key": "295B", "mappings": {"default": {"default": "बार से बारब अप के साथ दाहिने हार्पून"}}}, {"category": "Sm", "key": "295C", "mappings": {"default": {"default": "ऊपर की तरफ बार से राइट हार्पून"}}}, {"category": "Sm", "key": "295D", "mappings": {"default": {"default": "बार की तरफ बार के साथ नीचे की ओर हार्पून"}}}, {"category": "Sm", "key": "295E", "mappings": {"default": {"default": "बारवर्ड से बारप के साथ लेफ्टवर्ड हार्पून"}}}, {"category": "Sm", "key": "295F", "mappings": {"default": {"default": "बार से बारब डाउन के साथ दाहिनी ओर हार्पून"}}}, {"category": "Sm", "key": "2960", "mappings": {"default": {"default": "ऊपर से हार्पून बार से बारब लेफ्ट के साथ"}}}, {"category": "Sm", "key": "2961", "mappings": {"default": {"default": "नीचे की ओर हार्पून बार से बारब लेफ्ट के साथ"}}}, {"category": "Sm", "key": "2962", "mappings": {"default": {"default": "नीचे की तरफ बारबेक के साथ लेफ्ट हार्पून ऊपर की ओर बारबेक के साथ बाईं ओर हार्पून"}}}, {"category": "Sm", "key": "2963", "mappings": {"default": {"default": "ऊपर की तरफ बारब लेफ्ट के साथ हार्पून ऊपर की तरफ बारब राइट के साथ हार्पून"}}}, {"category": "Sm", "key": "2964", "mappings": {"default": {"default": "बारबेक के साथ ऊपर की तरफ बारबेक के साथ राइटप हार्पून"}}}, {"category": "Sm", "key": "2965", "mappings": {"default": {"default": "नीचे की ओर बारब लेफ्ट के साथ हार्पून नीचे की तरफ बारब के साथ हार्पून"}}}, {"category": "Sm", "key": "2966", "mappings": {"default": {"default": "बार्ब अप के साथ बार्ब अप के साथ लेफ्टवर्ड हार्पून। बारब अप के साथ हार्पून"}}}, {"category": "Sm", "key": "2967", "mappings": {"default": {"default": "बार्ब डाउन के साथ बार्ब डाउन के साथ लेफ्टवर्ड हार्पून। बारब डाउन के साथ हार्पून"}}}, {"category": "Sm", "key": "2968", "mappings": {"default": {"default": "बारबेक अप के साथ बारबेक अप के साथ दाहिना हार्पून"}}}, {"category": "Sm", "key": "2969", "mappings": {"default": {"default": "बारबेक के साथ नीचे की ओर बारबेक के साथ दाईं ओर वाली हार्पून, बारब की नीचे वाली हार्पून के साथ"}}}, {"category": "Sm", "key": "296A", "mappings": {"default": {"default": "लेफ्टवर्ड हार्पून बारबग अप अप लॉन्ग डैश के साथ"}}}, {"category": "Sm", "key": "296B", "mappings": {"default": {"default": "लंबे डैश के नीचे बारब के साथ बायीं ओर हरपून"}}}, {"category": "Sm", "key": "296C", "mappings": {"default": {"default": "ठीक है हार्पून बारबस अप एबव लॉन्ग डैश के साथ"}}}, {"category": "Sm", "key": "296D", "mappings": {"default": {"default": "दाहिने ओर हार्पून बारबस के साथ लॉन्ग डैश के नीचे"}}}, {"category": "Sm", "key": "296E", "mappings": {"default": {"default": "ऊपर की ओर बारब लेफ्ट के साथ हार्पून नीचे की तरफ बारब की ओर हार्पून"}}}, {"category": "Sm", "key": "296F", "mappings": {"default": {"default": "नीचे की ओर बारब लेफ्ट के साथ हार्पून बारब राइट के साथ हार्पून"}}}, {"category": "Sm", "key": "297C", "mappings": {"default": {"default": "लेफ्ट फिश टेल"}}}, {"category": "Sm", "key": "297D", "mappings": {"default": {"default": "सही मछली की पूंछ"}}}, {"category": "Sm", "key": "297E", "mappings": {"default": {"default": "ऊपर मछली की पूंछ"}}}, {"category": "Sm", "key": "297F", "mappings": {"default": {"default": "मछली की पूंछ नीचे"}}}], "hi/symbols/math_non_characters.min": [{"locale": "hi"}, {"category": "Ll", "key": "210F", "mappings": {"default": {"default": "प्लैंक कॉन्स्टेंट ओवर दो पाई"}}}, {"category": "So", "key": "2114", "mappings": {"default": {"default": "पौंड"}}}, {"category": "So", "key": "2116", "mappings": {"default": {"default": "न्यूमेरो साइन"}}}, {"category": "So", "key": "2117", "mappings": {"default": {"default": "ध्वनि रिकॉर्डिंग कॉपीराइट"}}}, {"category": "So", "key": "211E", "mappings": {"default": {"default": "पर्चे लेना"}}}, {"category": "So", "key": "211F", "mappings": {"default": {"default": "प्रतिक्रिया"}}}, {"category": "So", "key": "2120", "mappings": {"default": {"default": "सेवा चिन्ह"}}}, {"category": "So", "key": "2121", "mappings": {"default": {"default": "टेलीफोन साइन"}}}, {"category": "So", "key": "2122", "mappings": {"default": {"default": "ट्रेड मार्क साइन"}}}, {"category": "So", "key": "2123", "mappings": {"default": {"default": "छोटा पद्य"}}}, {"category": "So", "key": "2125", "mappings": {"default": {"default": "ऊँ संकेत"}}}, {"category": "<PERSON>", "key": "2126", "mappings": {"default": {"default": "ओम का चिन्ह"}}}, {"category": "So", "key": "2127", "mappings": {"default": {"default": "उलटा ओम का चिन्ह"}}}, {"category": "<PERSON>", "key": "212A", "mappings": {"default": {"default": "केल्विन साइन"}}}, {"category": "<PERSON>", "key": "212B", "mappings": {"default": {"default": "एंगस्ट्रॉम साइन"}}}, {"category": "So", "key": "212E", "mappings": {"default": {"default": "अनुमानित प्रतीक"}}}, {"category": "<PERSON>", "key": "2132", "mappings": {"default": {"default": "टर्नड कैपिटल एफ"}}}, {"category": "Ll", "key": "2139", "mappings": {"default": {"default": "जानकारी का श्रोत"}}}, {"category": "So", "key": "213A", "mappings": {"default": {"default": "घुमाई गई पूंजी Q"}}}, {"category": "So", "key": "213B", "mappings": {"default": {"default": "फेशियल साइन"}}}, {"category": "Sm", "key": "2141", "mappings": {"default": {"default": "सन्स-सेरिफ़ कैपिटल जी"}}}, {"category": "Sm", "key": "2142", "mappings": {"default": {"default": "सन्स-सेरिफ़ कैपिटल एल"}}}, {"category": "Sm", "key": "2143", "mappings": {"default": {"default": "उलटा सैंस-सेरिफ़ कैपिटल एल"}}}, {"category": "Sm", "key": "2144", "mappings": {"default": {"default": "सन्स-सेरिफ़ कैपिटल वाई"}}}], "hi/symbols/math_symbols.min": [{"locale": "hi"}, {"category": "Po", "key": "0021", "mappings": {"default": {"default": "विस्मयादिबोधक चिह्न"}}}, {"category": "Po", "key": "0022", "mappings": {"default": {"default": "उद्धरण चिन्ह"}}}, {"category": "Po", "key": "0023", "mappings": {"default": {"default": "हैश"}}}, {"category": "Sc", "key": "0024", "mappings": {"default": {"default": "डॉलर चिह्न"}}}, {"category": "Po", "key": "0025", "mappings": {"default": {"default": "प्रतिशत"}}}, {"category": "Po", "key": "0026", "mappings": {"default": {"default": "एन्ड चिह्न"}}}, {"category": "Po", "key": "0027", "mappings": {"default": {"default": "प्राईम चिह्न"}}}, {"category": "Po", "key": "002A", "mappings": {"default": {"default": "तारा<PERSON><PERSON>न"}}}, {"category": "Sm", "key": "002B", "mappings": {"default": {"default": "धन चिह्न"}}}, {"category": "Po", "key": "002C", "mappings": {"default": {"default": "अल्पविराम"}}}, {"category": "Pd", "key": "002D", "mappings": {"default": {"default": "ऋण चिह्न"}, "mathspeak": {"default": "समास चिह्न"}}}, {"category": "Po", "key": "002E", "mappings": {"default": {"default": "पूर्ण विराम"}}}, {"category": "Po", "key": "002F", "mappings": {"default": {"default": "विभाजन (For division.)"}}}, {"category": "Po", "key": "003A", "mappings": {"default": {"default": "विसर्ग"}}}, {"category": "Po", "key": "003B", "mappings": {"default": {"default": "अर्ध विराम"}}}, {"category": "Sm", "key": "003C", "mappings": {"default": {"default": "कम है"}}}, {"category": "Sm", "key": "003D", "mappings": {"default": {"default": "बरा<PERSON>र का चिह्न"}}}, {"category": "Sm", "key": "003E", "mappings": {"default": {"default": "अधिक है"}}}, {"category": "Po", "key": "003F", "mappings": {"default": {"default": "प्रश्न चिन्ह"}}}, {"category": "Po", "key": "0040", "mappings": {"default": {"default": "एट ध रेट"}}}, {"category": "Po", "key": "005C", "mappings": {"default": {"default": "बैकस्लेश"}}}, {"category": "Sk", "key": "005E", "mappings": {"default": {"default": "कैरट चिह्न"}}}, {"category": "Pc", "key": "005F", "mappings": {"default": {"default": "निम्न रेखा"}}}, {"category": "Sk", "key": "0060", "mappings": {"default": {"default": "ग्रेव चिह्न"}}}, {"category": "Sm", "key": "007C", "mappings": {"default": {"default": "सीधी खड़ी रेखा"}}}, {"category": "Sm", "key": "007E", "mappings": {"default": {"default": "टिल्ड चिह्न"}}}, {"category": "Po", "key": "00A1", "mappings": {"default": {"default": "उलटा विस्मयबोधक चिह्न"}}}, {"category": "Sc", "key": "00A2", "mappings": {"default": {"default": "सेन्ट चिह्न"}}}, {"category": "Sc", "key": "00A3", "mappings": {"default": {"default": "पाउंड स्टर्लिंग"}}}, {"category": "Sc", "key": "00A4", "mappings": {"default": {"default": "मुद्रा चिन्ह"}}}, {"category": "Sc", "key": "00A5", "mappings": {"default": {"default": "येन चिन्ह"}}}, {"category": "So", "key": "00A6", "mappings": {"default": {"default": "टूटी हुई ऊर्ध्वाधर पट्टी"}}}, {"category": "Po", "key": "00A7", "mappings": {"default": {"default": "अनुभाग चिह्न"}}}, {"category": "Sk", "key": "00A8", "mappings": {"default": {"default": "ऊपर बिंदुयुग्म"}}}, {"category": "So", "key": "00A9", "mappings": {"default": {"default": "कॉपीराइट"}}}, {"category": "Lo", "key": "00AA", "mappings": {"default": {"default": "मादावाची क्रमसूचक चिह्न"}}}, {"category": "Pi", "key": "00AB", "mappings": {"default": {"default": "बांया द्विकोणीय उद्धरण चिह्न"}}}, {"category": "Sm", "key": "00AC", "mappings": {"default": {"default": "नकार चिह्न"}}}, {"category": "So", "key": "00AE", "mappings": {"default": {"default": "रजिस्टर्ड ट्रेडमार्क चिह्न"}}}, {"category": "Sk", "key": "00AF", "mappings": {"default": {"default": "के ऊपर क्षैतिज रेखा"}}}, {"category": "So", "key": "00B0", "mappings": {"default": {"default": "अंश चिह्न"}}}, {"category": "Sm", "key": "00B1", "mappings": {"default": {"default": "योगवियोग"}}}, {"category": "Sk", "key": "00B4", "mappings": {"default": {"default": "एक्यूट एक्सेंट"}}}, {"category": "Ll", "key": "00B5", "mappings": {"default": {"default": "माइक्रो"}}}, {"category": "Po", "key": "00B6", "mappings": {"default": {"default": "परिच्छेद चिह्न"}}}, {"category": "Po", "key": "00B7", "mappings": {"default": {"default": "गुणन चिह्न"}}}, {"category": "Sk", "key": "00B8", "mappings": {"default": {"default": "सिडिला"}}}, {"category": "Lo", "key": "00BA", "mappings": {"default": {"default": "नरवाची क्रमसूचक चिह्न"}}}, {"category": "Pf", "key": "00BB", "mappings": {"default": {"default": "दांया द्विकोणीय उद्धरण चिह्न"}}}, {"category": "Po", "key": "00BF", "mappings": {"default": {"default": "उलटा प्रश्न चिह्न"}}}, {"category": "Sm", "key": "00D7", "mappings": {"default": {"default": "गुणन चिह्न"}}}, {"category": "Sm", "key": "00F7", "mappings": {"default": {"default": "विभाजन चिह्न"}}}, {"category": "Sk", "key": "02D8", "mappings": {"default": {"default": "ऊपर ब्रिव चिह्न"}}}, {"category": "Lm", "key": "02B9", "mappings": {"default": {"default": "प्राइम"}}}, {"category": "Lm", "key": "02BA", "mappings": {"default": {"default": "डबल प्राइम"}}}, {"category": "Sk", "key": "02D9", "mappings": {"default": {"default": "ऊपर बिंदु"}}}, {"category": "Sk", "key": "02DA", "mappings": {"default": {"default": "ऊपर छोटा वर्तुल"}}}, {"category": "Sk", "key": "02DB", "mappings": {"default": {"default": "ओगोनेक चिह्न"}}}, {"category": "Sk", "key": "02DC", "mappings": {"default": {"default": "छोटा टिल्ड"}}}, {"category": "Sk", "key": "02DD", "mappings": {"default": {"default": "डबल एक्यूट एक्सेंट"}}}, {"category": "Pd", "key": "2010", "mappings": {"default": {"default": "समास चिह्न"}}}, {"category": "Pd", "key": "2011", "mappings": {"default": {"default": "समास चिह्न"}}}, {"category": "Pd", "key": "2012", "mappings": {"default": {"default": "समास चिह्न"}}}, {"category": "Pd", "key": "2013", "mappings": {"default": {"default": "समास चिह्न"}}}, {"category": "Pd", "key": "2014", "mappings": {"default": {"default": "समास चिह्न"}}}, {"category": "Pd", "key": "2015", "mappings": {"default": {"default": "उद्धरण रेखा"}}}, {"category": "Po", "key": "2016", "mappings": {"default": {"default": "समान्तर रेखा चिह्न"}}}, {"category": "Po", "key": "2017", "mappings": {"default": {"default": "अधो द्विरेखा"}}}, {"category": "Pi", "key": "2018", "mappings": {"default": {"default": "एकल उद्धरण चिह्न आरंभ"}}}, {"category": "Pf", "key": "2019", "mappings": {"default": {"default": "एकल उद्धरण चिह्न अंत"}}}, {"category": "Ps", "key": "201A", "mappings": {"default": {"default": "एकल अल्पविराम उद्धरण चिह्न"}}}, {"category": "Pi", "key": "201B", "mappings": {"default": {"default": "उल्टा एकल उद्धरण चिह्न आरंभ"}}}, {"category": "Pi", "key": "201C", "mappings": {"default": {"default": "उद्धरण चिह्न आरंभ"}}}, {"category": "Pf", "key": "201D", "mappings": {"default": {"default": "उद्धरण चिह्न अंत"}}}, {"category": "Ps", "key": "201E", "mappings": {"default": {"default": "दुगना अल्पविराम उद्धरण चिह्न"}}}, {"category": "Pi", "key": "201F", "mappings": {"default": {"default": "उल्टा उद्धरण चिह्न आरंभ"}}}, {"category": "Po", "key": "2020", "mappings": {"default": {"default": "लंबा क्रॉस"}}}, {"category": "Po", "key": "2021", "mappings": {"default": {"default": "दो क्रॉस वाली ऊर्ध्वाधर रेखा"}}}, {"category": "Po", "key": "2022", "mappings": {"default": {"default": "छोटी काली बिंदी"}}}, {"category": "Po", "key": "2023", "mappings": {"default": {"default": "त्रिकोणीय बुलेट"}}}, {"category": "Po", "key": "2024", "mappings": {"default": {"default": "एक बिंदु"}}}, {"category": "Po", "key": "2025", "mappings": {"default": {"default": "दो बिंदु"}}}, {"category": "Po", "key": "2026", "mappings": {"default": {"default": "आदि आदि आदि"}}}, {"category": "Po", "key": "2027", "mappings": {"default": {"default": "समासवाची बिंदु"}}}, {"category": "Po", "key": "2030", "mappings": {"default": {"default": "प्रति सहस्र"}}}, {"category": "Po", "key": "2031", "mappings": {"default": {"default": "प्रति दस सहस्त्र"}}}, {"category": "Po", "key": "2032", "mappings": {"default": {"default": "प्राइम"}}}, {"category": "Po", "key": "2033", "mappings": {"default": {"default": "डबल प्राइम"}}}, {"category": "Po", "key": "2034", "mappings": {"default": {"default": "तिगुना प्राइम"}}}, {"category": "Po", "key": "2035", "mappings": {"default": {"default": "उल्टा प्राइम"}}}, {"category": "Po", "key": "2036", "mappings": {"default": {"default": "उल्टा डबल प्राइम"}}}, {"category": "Po", "key": "2037", "mappings": {"default": {"default": "उल्टा तिगुना प्राइम"}}}, {"category": "Po", "key": "2038", "mappings": {"default": {"default": "कैरट"}}}, {"category": "Pi", "key": "2039", "mappings": {"default": {"default": "कोण कोष्ठ आरंभ"}}}, {"category": "Pf", "key": "203A", "mappings": {"default": {"default": "कोण कोष्ठ अंत"}}}, {"category": "Po", "key": "203B", "mappings": {"default": {"default": "संदर्भ चिह्न"}}}, {"category": "Po", "key": "203C", "mappings": {"default": {"default": "दोहरा विस्मयादिबोधक चिह्न"}}}, {"category": "Po", "key": "203D", "mappings": {"default": {"default": "विस्मयादिबोधक प्रश्न चिह्न"}}}, {"category": "Po", "key": "203E", "mappings": {"default": {"default": "शिरोरेखा"}}}, {"category": "Pc", "key": "203F", "mappings": {"default": {"default": "निचला कोष्ठ"}}}, {"category": "Pc", "key": "2040", "mappings": {"default": {"default": "शीर्ष कोष्ठ"}}}, {"category": "Po", "key": "2041", "mappings": {"default": {"default": "प्रविष्टि सूचक कैरेट"}}}, {"category": "Po", "key": "2042", "mappings": {"default": {"default": "नक्षत्र"}}}, {"category": "Po", "key": "2043", "mappings": {"default": {"default": "समास चिह्न जैसी बुलेट"}}}, {"category": "Sm", "key": "2044", "mappings": {"default": {"default": "अपूर्णांक सूचक उर्ध्वगामी विकर्ण रेखा"}}}, {"category": "Po", "key": "2047", "mappings": {"default": {"default": "दोहरा प्रश्न चिह्न"}}}, {"category": "Po", "key": "2048", "mappings": {"default": {"default": "प्रश्न विस्मयादिबोधक चिह्न"}}}, {"category": "Po", "key": "2049", "mappings": {"default": {"default": "विस्मयादिबोधक प्रश्न चिह्न"}}}, {"category": "Po", "key": "204B", "mappings": {"default": {"default": "उल्टा परिच्छेद सूचक चिह्न"}}}, {"category": "Po", "key": "204C", "mappings": {"default": {"default": "बांयी और की बुलेट"}}}, {"category": "Po", "key": "204D", "mappings": {"default": {"default": "दांयी और की बुलेट"}}}, {"category": "Po", "key": "204E", "mappings": {"default": {"default": "तारा<PERSON><PERSON>न"}}}, {"category": "Po", "key": "204F", "mappings": {"default": {"default": "उल्टा सेमीकोलन"}}}, {"category": "Po", "key": "2050", "mappings": {"default": {"default": "क्लोज़ अप"}}}, {"category": "Po", "key": "2051", "mappings": {"default": {"default": "दो लंबवत तारांकन"}}}, {"category": "Sm", "key": "2052", "mappings": {"default": {"default": "वाणिज्यिक माइनस साइन"}}}, {"category": "Po", "key": "2053", "mappings": {"default": {"default": "स्वांग डैश"}}}, {"category": "Pc", "key": "2054", "mappings": {"default": {"default": "अंडरटेरी का उलटा"}}}, {"category": "Po", "key": "2055", "mappings": {"default": {"default": "पुष्प विराम चिह्न"}}}, {"category": "Po", "key": "2056", "mappings": {"default": {"default": "तीन डॉट पंचर"}}}, {"category": "Po", "key": "2057", "mappings": {"default": {"default": "चौगुना प्राइम"}}}, {"category": "Po", "key": "2058", "mappings": {"default": {"default": "चार डॉट विराम चिह्न"}}}, {"category": "Po", "key": "2059", "mappings": {"default": {"default": "पांच डॉट पंचर"}}}, {"category": "Po", "key": "205A", "mappings": {"default": {"default": "दो डॉट पंचर"}}}, {"category": "Po", "key": "205B", "mappings": {"default": {"default": "फोर डॉट मार्क"}}}, {"category": "Po", "key": "205C", "mappings": {"default": {"default": "बिंदीदार क्रॉस"}}}, {"category": "Po", "key": "205D", "mappings": {"default": {"default": "tricolon"}}}, {"category": "Po", "key": "205E", "mappings": {"default": {"default": "ऊर्ध्वाधर चार डॉट्स"}}}, {"category": "Sm", "key": "207A", "mappings": {"default": {"default": "सुपरस्क्रिप्ट प्लस चिन्ह"}}}, {"category": "Sm", "key": "207B", "mappings": {"default": {"default": "सुपरस्क्रिप्ट मिनेस"}}}, {"category": "Sm", "key": "207C", "mappings": {"default": {"default": "सुपरस्क्रिप्ट समान साइन"}}}, {"category": "Ps", "key": "207D", "mappings": {"default": {"default": "सुपरस्क्रिप्ट वाम कोष्ठक"}}}, {"category": "Pe", "key": "207E", "mappings": {"default": {"default": "सुपरस्क्रिप्ट राइट कोष्ठक"}}}, {"category": "Sm", "key": "208A", "mappings": {"default": {"default": "सबस्क्रिप्ट प्लस साइन"}}}, {"category": "Sm", "key": "208B", "mappings": {"default": {"default": "सबस्क्राइब माइनस"}}}, {"category": "Sm", "key": "208C", "mappings": {"default": {"default": "सदस्यता बराबर संकेत"}}}, {"category": "Ps", "key": "208D", "mappings": {"default": {"default": "सदस्यता लेफ्ट कोष्ठक"}}}, {"category": "Pe", "key": "208E", "mappings": {"default": {"default": "सबस्क्रिप्ट राइट कोष्ठक"}}}, {"category": "So", "key": "214A", "mappings": {"default": {"default": "संपत्ति रेखा"}}}, {"category": "Sm", "key": "214B", "mappings": {"default": {"default": "अमपर्संड बदल गया"}}}, {"category": "So", "key": "214C", "mappings": {"default": {"default": "प्रति संकेत"}}}, {"category": "So", "key": "214D", "mappings": {"default": {"default": "aKTIESELSKAB"}}}, {"category": "Ll", "key": "214E", "mappings": {"default": {"default": "स्मॉल एफ"}}}, {"category": "Sm", "key": "2200", "mappings": {"default": {"default": "सबके लिए"}}}, {"category": "Sm", "key": "2201", "mappings": {"default": {"default": "पूरक हैं"}}}, {"category": "Sm", "key": "2203", "mappings": {"default": {"default": "वहां मौजूद"}}}, {"category": "Sm", "key": "2204", "mappings": {"default": {"default": "वहाँ मौजूद नहीं है"}}}, {"category": "Sm", "key": "2205", "mappings": {"default": {"default": "खाली सेट"}}}, {"category": "Sm", "key": "2206", "mappings": {"default": {"default": "वेतन वृद्धि"}}}, {"category": "Sm", "key": "2208", "mappings": {"default": {"default": "का तत्व"}}}, {"category": "Sm", "key": "2209", "mappings": {"default": {"default": "का तत्व नहीं"}}}, {"category": "Sm", "key": "220A", "mappings": {"default": {"default": "का छोटा तत्व"}}}, {"category": "Sm", "key": "220B", "mappings": {"default": {"default": "का तत्व"}}}, {"category": "Sm", "key": "220C", "mappings": {"default": {"default": "का तत्व नहीं"}}}, {"category": "Sm", "key": "220D", "mappings": {"default": {"default": "छोटे सदस्य के रूप में शामिल हैं"}}}, {"category": "Sm", "key": "220E", "mappings": {"default": {"default": "सबूत का अंत"}}}, {"category": "Sm", "key": "220F", "mappings": {"default": {"default": "समूहगुणन चिह्न"}}}, {"category": "Sm", "key": "2210", "mappings": {"default": {"default": "एन-आर्य कपट"}}}, {"category": "Sm", "key": "2211", "mappings": {"default": {"default": "योग चिह्न"}}}, {"category": "Sm", "key": "2212", "mappings": {"default": {"default": "घटाव का चिन्ह"}}}, {"category": "Sm", "key": "2213", "mappings": {"default": {"default": "वियोगयोग अथवा माइनस प्लस चिह्न"}}}, {"category": "Sm", "key": "2214", "mappings": {"default": {"default": "डॉट प्लस"}}}, {"category": "Sm", "key": "2215", "mappings": {"default": {"default": "डिवीजन स्लैश"}}}, {"category": "Sm", "key": "2216", "mappings": {"default": {"default": "माइनस सेट करें"}}}, {"category": "Sm", "key": "2217", "mappings": {"default": {"default": "तारक संचालक"}}}, {"category": "Sm", "key": "2218", "mappings": {"default": {"default": "रिंग"}, "clearspeak": {"default": "संयुक्त फलन"}}}, {"category": "Sm", "key": "2219", "mappings": {"default": {"default": "बुलेट"}}}, {"category": "Sm", "key": "221A", "mappings": {"default": {"default": "वर्गमूल"}}}, {"category": "Sm", "key": "221B", "mappings": {"default": {"default": "घनमूल"}}}, {"category": "Sm", "key": "221C", "mappings": {"default": {"default": "चौथा मूल"}}}, {"category": "Sm", "key": "221D", "mappings": {"default": {"default": "आनुपातिक"}}}, {"category": "Sm", "key": "221E", "mappings": {"default": {"default": "अनन्तता"}}}, {"category": "Sm", "key": "221F", "mappings": {"default": {"default": "समकोण"}}}, {"category": "Sm", "key": "2220", "mappings": {"default": {"default": "कोण"}}}, {"category": "Sm", "key": "2221", "mappings": {"default": {"default": "मा<PERSON>ा कोण"}}}, {"category": "Sm", "key": "2222", "mappings": {"default": {"default": "गोलाकार कोण"}}}, {"category": "Sm", "key": "2223", "mappings": {"default": {"default": "विभाजित"}}}, {"category": "Sm", "key": "2224", "mappings": {"default": {"default": "बंटता नहीं है"}}}, {"category": "Sm", "key": "2225", "mappings": {"default": {"default": "समानांतर"}}}, {"category": "Sm", "key": "2226", "mappings": {"default": {"default": "समानांतर नहीं"}}}, {"category": "Sm", "key": "2227", "mappings": {"default": {"default": "तार्किक और"}}}, {"category": "Sm", "key": "2228", "mappings": {"default": {"default": "तार्किक या"}}}, {"category": "Sm", "key": "2229", "mappings": {"default": {"default": "प्रतिच्छेदन चिह्न"}}}, {"category": "Sm", "key": "222A", "mappings": {"default": {"default": "संयोग चिह्न"}}}, {"category": "Sm", "key": "222B", "mappings": {"default": {"default": "समाकल"}}}, {"category": "Sm", "key": "222C", "mappings": {"default": {"default": "दुगना समाकल"}}}, {"category": "Sm", "key": "222D", "mappings": {"default": {"default": "तिगुना समाकल"}}}, {"category": "Sm", "key": "222E", "mappings": {"default": {"default": "कॉन्टूर समाकल"}}}, {"category": "Sm", "key": "222F", "mappings": {"default": {"default": "सतह समाकल"}}}, {"category": "Sm", "key": "2230", "mappings": {"default": {"default": "क<PERSON> समाकल"}}}, {"category": "Sm", "key": "2231", "mappings": {"default": {"default": "दक्षिणावर्त समाकल"}}}, {"category": "Sm", "key": "2232", "mappings": {"default": {"default": "दक्षिणावर्त कॉन्टूर समाकल"}}}, {"category": "Sm", "key": "2233", "mappings": {"default": {"default": "वामावर्त कॉन्टूर समाकल"}}}, {"category": "Sm", "key": "2234", "mappings": {"default": {"default": "इसलिये"}}}, {"category": "Sm", "key": "2235", "mappings": {"default": {"default": "इसलिये"}}}, {"category": "Sm", "key": "2236", "mappings": {"default": {"default": "अनुपात"}}}, {"category": "Sm", "key": "2237", "mappings": {"default": {"default": "अनुपात"}}}, {"category": "Sm", "key": "2238", "mappings": {"default": {"default": "डॉट माइनस"}}}, {"category": "Sm", "key": "2239", "mappings": {"default": {"default": "अधिक"}}}, {"category": "Sm", "key": "223A", "mappings": {"default": {"default": "ज्यामितीय अनुपात"}}}, {"category": "Sm", "key": "223B", "mappings": {"default": {"default": "homothetic"}}}, {"category": "Sm", "key": "223C", "mappings": {"default": {"default": "टिल्ड संचालक"}}}, {"category": "Sm", "key": "223D", "mappings": {"default": {"default": "उलटा टिल्डे"}}}, {"category": "Sm", "key": "223E", "mappings": {"default": {"default": "उलटे आलसी एस"}}}, {"category": "Sm", "key": "223F", "mappings": {"default": {"default": "साइन तरंग"}}}, {"category": "Sm", "key": "2240", "mappings": {"default": {"default": "माल्यार्पण उत्पाद"}}}, {"category": "Sm", "key": "2241", "mappings": {"default": {"default": "टिल्डे नहीं"}}}, {"category": "Sm", "key": "2242", "mappings": {"default": {"default": "माइनस टिल्डे"}}}, {"category": "Sm", "key": "2243", "mappings": {"default": {"default": "समान रूप से समान"}}}, {"category": "Sm", "key": "2244", "mappings": {"default": {"default": "नहीं समान रूप से समान"}}}, {"category": "Sm", "key": "2245", "mappings": {"default": {"default": "लग<PERSON>ग बराबर"}}}, {"category": "Sm", "key": "2246", "mappings": {"default": {"default": "लगभग लेकिन वास्तव में समान नहीं है"}}}, {"category": "Sm", "key": "2247", "mappings": {"default": {"default": "न तो लगभग और न ही वास्तव में बराबर"}}}, {"category": "Sm", "key": "2248", "mappings": {"default": {"default": "लग<PERSON>ग बराबर"}}}, {"category": "Sm", "key": "2249", "mappings": {"default": {"default": "लगभग नहीं के बराबर"}}}, {"category": "Sm", "key": "224A", "mappings": {"default": {"default": "लग<PERSON>ग बराबर या बराबर"}}}, {"category": "Sm", "key": "224B", "mappings": {"default": {"default": "ट्रिपल टिल्ड"}}}, {"category": "Sm", "key": "224C", "mappings": {"default": {"default": "सभी को समान"}}}, {"category": "Sm", "key": "224D", "mappings": {"default": {"default": "के बराबर"}}}, {"category": "Sm", "key": "224E", "mappings": {"default": {"default": "ज्यामितीय समान रूप से"}}}, {"category": "Sm", "key": "224F", "mappings": {"default": {"default": "के बीच अंतर"}}}, {"category": "Sm", "key": "2250", "mappings": {"default": {"default": "सीमा का अनुमोदन करता है"}}}, {"category": "Sm", "key": "2251", "mappings": {"default": {"default": "ज्यामितीय रूप से समान"}}}, {"category": "Sm", "key": "2252", "mappings": {"default": {"default": "लगभग बराबर या की छवि"}}}, {"category": "Sm", "key": "2253", "mappings": {"default": {"default": "या लगभग बराबर की छवि"}}}, {"category": "Sm", "key": "2254", "mappings": {"default": {"default": "कोलोन बराबर"}}}, {"category": "Sm", "key": "2255", "mappings": {"default": {"default": "कोलोन के बराबर"}}}, {"category": "Sm", "key": "2256", "mappings": {"default": {"default": "बराबर में अँगूठी"}}}, {"category": "Sm", "key": "2257", "mappings": {"default": {"default": "रिंग के बराबर"}}}, {"category": "Sm", "key": "2258", "mappings": {"default": {"default": "से मेल खाती है"}}}, {"category": "Sm", "key": "2259", "mappings": {"default": {"default": "अनुमान"}}}, {"category": "Sm", "key": "225A", "mappings": {"default": {"default": "समबाहु को"}}}, {"category": "Sm", "key": "225B", "mappings": {"default": {"default": "स्टार बराबर"}}}, {"category": "Sm", "key": "225C", "mappings": {"default": {"default": "डेल्टा बराबर"}}}, {"category": "Sm", "key": "225D", "mappings": {"default": {"default": "परिभाषा के अनुसार समान"}}}, {"category": "Sm", "key": "225E", "mappings": {"default": {"default": "द्वारा मापा"}}}, {"category": "Sm", "key": "225F", "mappings": {"default": {"default": "के बराबर सवाल किया"}}}, {"category": "Sm", "key": "2260", "mappings": {"default": {"default": "बराबर नही है"}}}, {"category": "Sm", "key": "2261", "mappings": {"default": {"default": "के समान"}}}, {"category": "Sm", "key": "2262", "mappings": {"default": {"default": "पहचान नहीं है"}}}, {"category": "Sm", "key": "2263", "mappings": {"default": {"default": "कड़ाई से बराबर"}}}, {"category": "Sm", "key": "2264", "mappings": {"default": {"default": "से कम या बराबर"}}}, {"category": "Sm", "key": "2265", "mappings": {"default": {"default": "अधिक या समान है"}}}, {"category": "Sm", "key": "2266", "mappings": {"default": {"default": "कम या समान है"}}}, {"category": "Sm", "key": "2267", "mappings": {"default": {"default": "ग्रेटर-थान बराबरी पर"}}}, {"category": "Sm", "key": "2268", "mappings": {"default": {"default": "कम-से-कम लेकिन बराबर नहीं"}}}, {"category": "Sm", "key": "2269", "mappings": {"default": {"default": "ग्रेटर-थान लेकिन न के बराबर"}}}, {"category": "Sm", "key": "226A", "mappings": {"default": {"default": "की तुलना में बहुत कम"}}}, {"category": "Sm", "key": "226B", "mappings": {"default": {"default": "बहुत ग्रेटर-थान"}}}, {"category": "Sm", "key": "226C", "mappings": {"default": {"default": "के बीच"}}}, {"category": "Sm", "key": "226D", "mappings": {"default": {"default": "नहीं के बराबर"}}}, {"category": "Sm", "key": "226E", "mappings": {"default": {"default": "से कम नहीं"}}}, {"category": "Sm", "key": "226F", "mappings": {"default": {"default": "ग्रेटर-थान नहीं"}}}, {"category": "Sm", "key": "2270", "mappings": {"default": {"default": "न तो इससे कम और न ही बराबर"}}}, {"category": "Sm", "key": "2271", "mappings": {"default": {"default": "न तो ग्रेटर-थान और न ही बराबर"}}}, {"category": "Sm", "key": "2272", "mappings": {"default": {"default": "कम-से-अधिक या समकक्ष"}}}, {"category": "Sm", "key": "2273", "mappings": {"default": {"default": "ग्रेटर-थान या समकक्ष"}}}, {"category": "Sm", "key": "2274", "mappings": {"default": {"default": "न तो इससे कम और न ही बराबर"}}}, {"category": "Sm", "key": "2275", "mappings": {"default": {"default": "न तो ग्रेटर-थान और न ही समकक्ष"}}}, {"category": "Sm", "key": "2276", "mappings": {"default": {"default": "कम-थान या ग्रेटर-थान"}}}, {"category": "Sm", "key": "2277", "mappings": {"default": {"default": "ग्रेटर-थान या कम-थान"}}}, {"category": "Sm", "key": "2278", "mappings": {"default": {"default": "न तो लेस-थन और न ही ग्रेटर-थान"}}}, {"category": "Sm", "key": "2279", "mappings": {"default": {"default": "न तो ग्रेटर-थान और न ही कम-थान"}}}, {"category": "Sm", "key": "227A", "mappings": {"default": {"default": "पहले आता है"}}}, {"category": "Sm", "key": "227B", "mappings": {"default": {"default": "सफल होता है"}}}, {"category": "Sm", "key": "227C", "mappings": {"default": {"default": "प्रतिमान या बराबर"}}}, {"category": "Sm", "key": "227D", "mappings": {"default": {"default": "सफलता या बराबर"}}}, {"category": "Sm", "key": "227E", "mappings": {"default": {"default": "प्रतिसाद या समकक्ष"}}}, {"category": "Sm", "key": "227F", "mappings": {"default": {"default": "सफलता या समकक्ष"}}}, {"category": "Sm", "key": "2280", "mappings": {"default": {"default": "प्रीडेसी नहीं है"}}}, {"category": "Sm", "key": "2281", "mappings": {"default": {"default": "सफल नहीं होता"}}}, {"category": "Sm", "key": "2282", "mappings": {"default": {"default": "का भाग"}}}, {"category": "Sm", "key": "2283", "mappings": {"default": {"default": "का सुपरसेट"}}}, {"category": "Sm", "key": "2284", "mappings": {"default": {"default": "का सबसेट नहीं है"}}}, {"category": "Sm", "key": "2285", "mappings": {"default": {"default": "का सुपरसेट नहीं"}}}, {"category": "Sm", "key": "2286", "mappings": {"default": {"default": "के बराबर या के बराबर"}}}, {"category": "Sm", "key": "2287", "mappings": {"default": {"default": "सुपरसेट ऑफ़ या इक्वल टू"}}}, {"category": "Sm", "key": "2288", "mappings": {"default": {"default": "न तो सबसेट का और न ही बराबर का"}}}, {"category": "Sm", "key": "2289", "mappings": {"default": {"default": "न तो सुपरसेट का और न ही बराबर का"}}}, {"category": "Sm", "key": "228A", "mappings": {"default": {"default": "न के बराबर के साथ सबसेट"}}}, {"category": "Sm", "key": "228B", "mappings": {"default": {"default": "नहीं के बराबर के साथ सुपरसेट"}}}, {"category": "Sm", "key": "228C", "mappings": {"default": {"default": "मल्टीसेट"}}}, {"category": "Sm", "key": "228D", "mappings": {"default": {"default": "मल्टीसेट गुणा"}}}, {"category": "Sm", "key": "228E", "mappings": {"default": {"default": "मल्टीसेट यूनियन"}}}, {"category": "Sm", "key": "228F", "mappings": {"default": {"default": "वर्ग छवि"}}}, {"category": "Sm", "key": "2290", "mappings": {"default": {"default": "का वर्ग मूल"}}}, {"category": "Sm", "key": "2291", "mappings": {"default": {"default": "या के बराबर छवि"}}}, {"category": "Sm", "key": "2292", "mappings": {"default": {"default": "स्क्वायर ओरिजिनल ऑफ़ या इक्वल टू"}}}, {"category": "Sm", "key": "2293", "mappings": {"default": {"default": "स्क्वायर कैप"}}}, {"category": "Sm", "key": "2294", "mappings": {"default": {"default": "स्क्वायर कप"}}}, {"category": "Sm", "key": "2295", "mappings": {"default": {"default": "सर्किल प्लस"}}}, {"category": "Sm", "key": "2296", "mappings": {"default": {"default": "परिक्रमा माइनस"}}}, {"category": "Sm", "key": "2297", "mappings": {"default": {"default": "सर्किल टाइम्स"}}}, {"category": "Sm", "key": "2298", "mappings": {"default": {"default": "मंडल स्लैश"}}}, {"category": "Sm", "key": "2299", "mappings": {"default": {"default": "सर्किल डॉट"}}}, {"category": "Sm", "key": "229A", "mappings": {"default": {"default": "परिचालित रिंग संचालक"}}}, {"category": "Sm", "key": "229B", "mappings": {"default": {"default": "सर्किल का तारांकन"}}}, {"category": "Sm", "key": "229C", "mappings": {"default": {"default": "परिक्रमा के बराबर"}}}, {"category": "Sm", "key": "229D", "mappings": {"default": {"default": "चक्कर लगाया डैश"}}}, {"category": "Sm", "key": "229E", "mappings": {"default": {"default": "चुकता प्लस"}}}, {"category": "Sm", "key": "229F", "mappings": {"default": {"default": "चुकता माइनस"}}}, {"category": "Sm", "key": "22A0", "mappings": {"default": {"default": "चुकता समय"}}}, {"category": "Sm", "key": "22A1", "mappings": {"default": {"default": "चुकता डॉट"}}}, {"category": "Sm", "key": "22A2", "mappings": {"default": {"default": "राइट टैक"}}}, {"category": "Sm", "key": "22A3", "mappings": {"default": {"default": "लेफ्ट टैक"}}}, {"category": "Sm", "key": "22A4", "mappings": {"default": {"default": "डाउन टैक"}}}, {"category": "Sm", "key": "22A5", "mappings": {"default": {"default": "अप टाक"}}}, {"category": "Sm", "key": "22A6", "mappings": {"default": {"default": "अभिकथन"}}}, {"category": "Sm", "key": "22A7", "mappings": {"default": {"default": "मॉडल के"}}}, {"category": "Sm", "key": "22A8", "mappings": {"default": {"default": "सच"}}}, {"category": "Sm", "key": "22A9", "mappings": {"default": {"default": "ताकतों"}}}, {"category": "Sm", "key": "22AA", "mappings": {"default": {"default": "ट्रिपल वर्टिकल बार राइट टर्नस्टाइल"}}}, {"category": "Sm", "key": "22AB", "mappings": {"default": {"default": "डबल वर्टिकल बार डबल राइट टर्नस्टाइल"}}}, {"category": "Sm", "key": "22AC", "mappings": {"default": {"default": "सिद्ध नहीं होता है"}}}, {"category": "Sm", "key": "22AD", "mappings": {"default": {"default": "सच नहीं"}}}, {"category": "Sm", "key": "22AE", "mappings": {"default": {"default": "फोर्स नहीं है"}}}, {"category": "Sm", "key": "22AF", "mappings": {"default": {"default": "नेगेटिव डबल वर्टिकल बार डबल राइट टर्नस्टाइल"}}}, {"category": "Sm", "key": "22B0", "mappings": {"default": {"default": "रिलेशन के तहत प्रीयर करें"}}}, {"category": "Sm", "key": "22B1", "mappings": {"default": {"default": "संबंध के तहत सफलता"}}}, {"category": "Sm", "key": "22B2", "mappings": {"default": {"default": "का सामान्य उपसमूह"}}}, {"category": "Sm", "key": "22B3", "mappings": {"default": {"default": "सामान्य उपसमूह के रूप में शामिल है"}}}, {"category": "Sm", "key": "22B4", "mappings": {"default": {"default": "या के बराबर सामान्य उपसमूह"}}}, {"category": "Sm", "key": "22B5", "mappings": {"default": {"default": "सामान्य उपसमूह या समान के समान है"}}}, {"category": "Sm", "key": "22B6", "mappings": {"default": {"default": "का मूल"}}}, {"category": "Sm", "key": "22B7", "mappings": {"default": {"default": "की छवि"}}}, {"category": "Sm", "key": "22B8", "mappings": {"default": {"default": "मल्टीमैप"}}}, {"category": "Sm", "key": "22B9", "mappings": {"default": {"default": "हर्मिटियन कॉन्जुगेट मैट्रिक्स"}}}, {"category": "Sm", "key": "22BA", "mappings": {"default": {"default": "intercalate"}}}, {"category": "Sm", "key": "22BB", "mappings": {"default": {"default": "xor"}}}, {"category": "Sm", "key": "22BC", "mappings": {"default": {"default": "नंद"}}}, {"category": "Sm", "key": "22BD", "mappings": {"default": {"default": "न"}}}, {"category": "Sm", "key": "22BF", "mappings": {"default": {"default": "सही त्रिकोण"}}}, {"category": "Sm", "key": "22C0", "mappings": {"default": {"default": "एन-आर्य तार्किक और"}}}, {"category": "Sm", "key": "22C1", "mappings": {"default": {"default": "एन-आर्य तार्किक या"}}}, {"category": "Sm", "key": "22C2", "mappings": {"default": {"default": "एन-आर्य अंतःकरण"}}}, {"category": "Sm", "key": "22C3", "mappings": {"default": {"default": "एन-आर्य संघ"}}}, {"category": "Sm", "key": "22C4", "mappings": {"default": {"default": "हीरा संचालक"}}}, {"category": "Sm", "key": "22C5", "mappings": {"default": {"default": "गुणन चिह्न"}, "clearspeak": {"MultsymbolDot_Dot": "गुणन चिह्न"}}}, {"category": "Sm", "key": "22C6", "mappings": {"default": {"default": "स्टार संचालक"}}}, {"category": "Sm", "key": "22C7", "mappings": {"default": {"default": "डिवीजन टाइम्स"}}}, {"category": "Sm", "key": "22C8", "mappings": {"default": {"default": "बो टाई"}}}, {"category": "Sm", "key": "22C9", "mappings": {"default": {"default": "वाम सामान्य कारक सेमीडायरेक्ट उत्पाद"}}}, {"category": "Sm", "key": "22CA", "mappings": {"default": {"default": "राइट नॉर्मल फैक्टर सेमीडायरेक्ट प्रोडक्ट"}}}, {"category": "Sm", "key": "22CB", "mappings": {"default": {"default": "वाम सेमीडायरेक्ट उत्पाद"}}}, {"category": "Sm", "key": "22CC", "mappings": {"default": {"default": "राइट सेमीडायरेक्ट प्रोडक्ट"}}}, {"category": "Sm", "key": "22CD", "mappings": {"default": {"default": "उल्टे टिल्ड बराबर"}}}, {"category": "Sm", "key": "22CE", "mappings": {"default": {"default": "घुंघराले तार्किक या"}}}, {"category": "Sm", "key": "22CF", "mappings": {"default": {"default": "घुंघराले तार्किक और"}}}, {"category": "Sm", "key": "22D0", "mappings": {"default": {"default": "डबल सबसेट"}}}, {"category": "Sm", "key": "22D1", "mappings": {"default": {"default": "डबल सुपरसेट"}}}, {"category": "Sm", "key": "22D2", "mappings": {"default": {"default": "दोहरा अंतर"}}}, {"category": "Sm", "key": "22D3", "mappings": {"default": {"default": "डबल यूनियन"}}}, {"category": "Sm", "key": "22D4", "mappings": {"default": {"default": "सूखी घास इत्यादि की टाल लगाने का नोकदार डंडा"}}}, {"category": "Sm", "key": "22D5", "mappings": {"default": {"default": "बराबर और समानांतर"}}}, {"category": "Sm", "key": "22D6", "mappings": {"default": {"default": "डॉट के साथ कम-थान"}}}, {"category": "Sm", "key": "22D7", "mappings": {"default": {"default": "डॉट के साथ ग्रेटर-थान"}}}, {"category": "Sm", "key": "22D8", "mappings": {"default": {"default": "बहुत कम-से-कम"}}}, {"category": "Sm", "key": "22D9", "mappings": {"default": {"default": "बहुत ज्यादा ग्रेटर-थान"}}}, {"category": "Sm", "key": "22DA", "mappings": {"default": {"default": "कम-से-बराबर बराबर या ग्रेटर-थान से"}}}, {"category": "Sm", "key": "22DB", "mappings": {"default": {"default": "ग्रेटर-थान बराबर या उससे कम"}}}, {"category": "Sm", "key": "22DC", "mappings": {"default": {"default": "बराबर या उससे कम"}}}, {"category": "Sm", "key": "22DD", "mappings": {"default": {"default": "बराबर या ग्रेटर-थान"}}}, {"category": "Sm", "key": "22DE", "mappings": {"default": {"default": "बराबर या पूर्ववत"}}}, {"category": "Sm", "key": "22DF", "mappings": {"default": {"default": "बराबर या सफल होना"}}}, {"category": "Sm", "key": "22E0", "mappings": {"default": {"default": "प्रीडेसी या समान नहीं है"}}}, {"category": "Sm", "key": "22E1", "mappings": {"default": {"default": "सफल या बराबर नहीं है"}}}, {"category": "Sm", "key": "22E2", "mappings": {"default": {"default": "वर्ग छवि या समान नहीं"}}}, {"category": "Sm", "key": "22E3", "mappings": {"default": {"default": "स्क्वायर स्क्वायर ओरिजिनल ऑफ़ या इक्वल टू नहीं"}}}, {"category": "Sm", "key": "22E4", "mappings": {"default": {"default": "या नहीं के बराबर छवि"}}}, {"category": "Sm", "key": "22E5", "mappings": {"default": {"default": "स्क्वायर ओरिजिनल ऑफ़ या न के बराबर"}}}, {"category": "Sm", "key": "22E6", "mappings": {"default": {"default": "कम-से-कम लेकिन बराबर नहीं"}}}, {"category": "Sm", "key": "22E7", "mappings": {"default": {"default": "ग्रेटर-थान लेकिन न के बराबर"}}}, {"category": "Sm", "key": "22E8", "mappings": {"default": {"default": "प्रतिसाद लेकिन नहीं के बराबर"}}}, {"category": "Sm", "key": "22E9", "mappings": {"default": {"default": "सफल लेकिन नहीं के बराबर"}}}, {"category": "Sm", "key": "22EA", "mappings": {"default": {"default": "सामान्य उपसमूह नहीं"}}}, {"category": "Sm", "key": "22EB", "mappings": {"default": {"default": "सामान्य उपसमूह के रूप में कंटेनर नहीं है"}}}, {"category": "Sm", "key": "22EC", "mappings": {"default": {"default": "न के बराबर उपसमूह या बराबर"}}}, {"category": "Sm", "key": "22ED", "mappings": {"default": {"default": "सामान्य उपसमूह या समान के रूप में कंटेनर नहीं है"}}}, {"category": "Sm", "key": "22EE", "mappings": {"default": {"default": "ऊर्ध्वाधर एलिप्सिस"}}}, {"category": "Sm", "key": "22EF", "mappings": {"default": {"default": "आदि आदि आदि"}}}, {"category": "Sm", "key": "22F0", "mappings": {"default": {"default": "ऊपर राइट विकर्ण एलिप्सिस"}}}, {"category": "Sm", "key": "22F1", "mappings": {"default": {"default": "डाउन राइट विकर्ण एलिप्सिस"}}}, {"category": "Sm", "key": "22F2", "mappings": {"default": {"default": "लंबे क्षैतिज स्ट्रोक के साथ तत्व"}}}, {"category": "Sm", "key": "22F3", "mappings": {"default": {"default": "क्षैतिज स्ट्रोक के अंत में ऊर्ध्वाधर बार के साथ तत्व"}}}, {"category": "Sm", "key": "22F4", "mappings": {"default": {"default": "क्षैतिज स्ट्रोक के अंत में ऊर्ध्वाधर बार के साथ छोटे तत्व"}}}, {"category": "Sm", "key": "22F5", "mappings": {"default": {"default": "डॉट ऊपर के साथ तत्व"}}}, {"category": "Sm", "key": "22F6", "mappings": {"default": {"default": "ओवरबार के साथ तत्व"}}}, {"category": "Sm", "key": "22F7", "mappings": {"default": {"default": "ओवरबार के साथ छोटे तत्व"}}}, {"category": "Sm", "key": "22F8", "mappings": {"default": {"default": "अंडरबार के साथ तत्व"}}}, {"category": "Sm", "key": "22F9", "mappings": {"default": {"default": "दो क्षैतिज स्ट्रोक के साथ तत्व"}}}, {"category": "Sm", "key": "22FA", "mappings": {"default": {"default": "लंबे क्षैतिज स्ट्रोक के साथ सम्‍मिलित है"}}}, {"category": "Sm", "key": "22FB", "mappings": {"default": {"default": "क्षैतिज स्ट्रोक के अंत में वर्टिकल बार के साथ होता है"}}}, {"category": "Sm", "key": "22FC", "mappings": {"default": {"default": "क्षैतिज स्ट्रोक के अंत में वर्टिकल बार के साथ छोटे शामिल हैं"}}}, {"category": "Sm", "key": "22FD", "mappings": {"default": {"default": "ओवरबार के साथ सम्‍मिलित है"}}}, {"category": "Sm", "key": "22FE", "mappings": {"default": {"default": "ओवरबार के साथ छोटे कंटीन्यू"}}}, {"category": "Sm", "key": "22FF", "mappings": {"default": {"default": "जेड नोटेशन बैग सदस्यता"}}}, {"category": "So", "key": "2300", "mappings": {"default": {"default": "व्यास का चिन्ह"}}}, {"category": "So", "key": "2302", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"category": "So", "key": "2305", "mappings": {"default": {"default": "प्रक्षेपीय"}}}, {"category": "So", "key": "2306", "mappings": {"default": {"default": "परिप्रेक्ष्य"}}}, {"category": "So", "key": "2307", "mappings": {"default": {"default": "लहराती रेखा"}}}, {"category": "So", "key": "2310", "mappings": {"default": {"default": "उल्टे हस्ताक्षर नहीं"}}}, {"category": "So", "key": "2311", "mappings": {"default": {"default": "स्क्वायर लोजेंज"}}}, {"category": "So", "key": "2312", "mappings": {"default": {"default": "आर्क"}}}, {"category": "So", "key": "2313", "mappings": {"default": {"default": "खंड"}}}, {"category": "So", "key": "2314", "mappings": {"default": {"default": "क्षेत्र"}}}, {"category": "So", "key": "2795", "mappings": {"default": {"default": "हैवी प्लस साइन"}}}, {"category": "So", "key": "2796", "mappings": {"default": {"default": "भारी माइनस साइन"}}}, {"category": "So", "key": "2797", "mappings": {"default": {"default": "हैवी डिवीजन साइन"}}}, {"category": "So", "key": "27B0", "mappings": {"default": {"default": "घुंघराले पाश"}}}, {"category": "So", "key": "27BF", "mappings": {"default": {"default": "डबल कर्ली लूप"}}}, {"category": "Sm", "key": "27C1", "mappings": {"default": {"default": "व्हाइट ट्रायंगल जिसमें व्हाइट व्हाइट ट्रायंगल होता है"}}}, {"category": "Sm", "key": "27C2", "mappings": {"default": {"default": "सीधा"}}}, {"category": "Sm", "key": "27C3", "mappings": {"default": {"default": "सबसेट खोलें"}}}, {"category": "Sm", "key": "27C4", "mappings": {"default": {"default": "सुपरसेट खोलें"}}}, {"category": "Sm", "key": "27C7", "mappings": {"default": {"default": "या डॉट इन के साथ"}}}, {"category": "Sm", "key": "27C8", "mappings": {"default": {"default": "रिवर्स सॉलिडस पूर्ववर्ती सबसेट"}}}, {"category": "Sm", "key": "27C9", "mappings": {"default": {"default": "सुपरसैट पूर्ववर्ती सॉलिडस"}}}, {"category": "Sm", "key": "27CA", "mappings": {"default": {"default": "क्षैतिज स्ट्रोक के साथ लंबवत पट्टी"}}}, {"category": "Sm", "key": "27CB", "mappings": {"default": {"default": "गणितीय बढ़ती विकर्ण"}}}, {"category": "Sm", "key": "27CC", "mappings": {"default": {"default": "लम्बा विभाजन"}}}, {"category": "Sm", "key": "27CD", "mappings": {"default": {"default": "गणितीय पतनशील विकर्ण"}}}, {"category": "Sm", "key": "27CE", "mappings": {"default": {"default": "चुकता तार्किक और"}}}, {"category": "Sm", "key": "27CF", "mappings": {"default": {"default": "चुकता तार्किक या"}}}, {"category": "Sm", "key": "27D0", "mappings": {"default": {"default": "सफेद हीरे के साथ केंद्रित डॉट"}}}, {"category": "Sm", "key": "27D1", "mappings": {"default": {"default": "और डॉट के साथ"}}}, {"category": "Sm", "key": "27D2", "mappings": {"default": {"default": "ऊपर की ओर खुलने का तत्व"}}}, {"category": "Sm", "key": "27D3", "mappings": {"default": {"default": "डॉट के साथ लोअर राइट कॉर्नर"}}}, {"category": "Sm", "key": "27D4", "mappings": {"default": {"default": "डॉट के साथ ऊपरी बाएं कोने"}}}, {"category": "Sm", "key": "27D5", "mappings": {"default": {"default": "बाईं ओर का बाहरी जोड़"}}}, {"category": "Sm", "key": "27D6", "mappings": {"default": {"default": "राइट आउटर जॉइन"}}}, {"category": "Sm", "key": "27D7", "mappings": {"default": {"default": "पूर्ण बाहरी सम्मिलित हों"}}}, {"category": "Sm", "key": "27D8", "mappings": {"default": {"default": "बड़े अप टैक"}}}, {"category": "Sm", "key": "27D9", "mappings": {"default": {"default": "बड़े डाउन टैक"}}}, {"category": "Sm", "key": "27DA", "mappings": {"default": {"default": "लेफ्ट और राइट डबल टर्नस्टाइल"}}}, {"category": "Sm", "key": "27DB", "mappings": {"default": {"default": "लेफ्ट और राइट टैक"}}}, {"category": "Sm", "key": "27DC", "mappings": {"default": {"default": "वाम मुल्तिप"}}}, {"category": "Sm", "key": "27DD", "mappings": {"default": {"default": "लम्बी दाहिनी टाकी"}}}, {"category": "Sm", "key": "27DE", "mappings": {"default": {"default": "लंबे समय तक लेफ्ट टैक"}}}, {"category": "Sm", "key": "27DF", "mappings": {"default": {"default": "ऊपर के सर्कल के साथ अप टैक"}}}, {"category": "Sm", "key": "27E0", "mappings": {"default": {"default": "लोजेंज क्षैतिज नियम द्वारा विभाजित"}}}, {"category": "Sm", "key": "27E1", "mappings": {"default": {"default": "सफेद अवतल-पक्षीय हीरा"}}}, {"category": "Sm", "key": "27E2", "mappings": {"default": {"default": "व्हाइट कॉनक्वेव-साइडेड डायमंड विद लेफ्टवर्ड्स टिक"}}}, {"category": "Sm", "key": "27E3", "mappings": {"default": {"default": "व्हाइट कॉनक्लेव-साइडेड डायमंड विथ राइट्स टिक"}}}, {"category": "Sm", "key": "27E4", "mappings": {"default": {"default": "व्हाइट स्क्वायर बायीं ओर टिक के साथ"}}}, {"category": "Sm", "key": "27E5", "mappings": {"default": {"default": "राइट स्क्वायर टिक के साथ व्हाइट स्क्वायर"}}}, {"category": "Sm", "key": "292B", "mappings": {"default": {"default": "बढ़ती विकर्ण पार विकर्ण"}}}, {"category": "Sm", "key": "292C", "mappings": {"default": {"default": "गिरते हुए विकर्ण"}}}, {"category": "Sm", "key": "2980", "mappings": {"default": {"default": "ट्रिपल वर्टिकल बार डेलिमिटर"}}}, {"category": "Sm", "key": "2981", "mappings": {"default": {"default": "जेड नोटेशन स्पॉट"}}}, {"category": "Sm", "key": "2982", "mappings": {"default": {"default": "जेड नोटेशन प्रकार कोलोन"}}}, {"category": "Sm", "key": "2999", "mappings": {"default": {"default": "बिंदीदार बाड़"}}}, {"category": "Sm", "key": "299A", "mappings": {"default": {"default": "कार्यक्षेत्र Zigzag रेखा"}}}, {"category": "Sm", "key": "29B0", "mappings": {"default": {"default": "उल्टा खाली सेट"}}}, {"category": "Sm", "key": "29B1", "mappings": {"default": {"default": "ओवरबार के साथ खाली सेट"}}}, {"category": "Sm", "key": "29B2", "mappings": {"default": {"default": "ऊपर छोटे सर्कल के साथ खाली सेट"}}}, {"category": "Sm", "key": "29B5", "mappings": {"default": {"default": "क्षैतिज पट्टी के साथ सर्कल"}}}, {"category": "Sm", "key": "29B6", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> बार"}}}, {"category": "Sm", "key": "29B7", "mappings": {"default": {"default": "परलोक की परिक्रमा की"}}}, {"category": "Sm", "key": "29B8", "mappings": {"default": {"default": "उलटा सॉलिडस सर्किल"}}}, {"category": "Sm", "key": "29B9", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>क<PERSON> लगाया"}}}, {"category": "Sm", "key": "29BA", "mappings": {"default": {"default": "सर्कल को क्षैतिज पट्टी से विभाजित किया गया और शीर्ष आधा को ऊर्ध्वाधर बार से विभाजित किया गया"}}}, {"category": "Sm", "key": "29BB", "mappings": {"default": {"default": "सुपरिंपोज्ड एक्स के साथ सर्कल"}}}, {"category": "Sm", "key": "29BC", "mappings": {"default": {"default": "सर्किल एंटिक्लॉकवाइज-रोटेटेड डिवीजन साइन"}}}, {"category": "Sm", "key": "29BE", "mappings": {"default": {"default": "सफ़ेद गोली का गोला"}}}, {"category": "Sm", "key": "29BF", "mappings": {"default": {"default": "गोल घेरा"}}}, {"category": "Sm", "key": "29C0", "mappings": {"default": {"default": "कम से कम की परिक्रमा की"}}}, {"category": "Sm", "key": "29C1", "mappings": {"default": {"default": "ग्रेटर-थान परिक्रमा की"}}}, {"category": "Sm", "key": "29C2", "mappings": {"default": {"default": "सर्कल को छोटे सर्कल के साथ दाईं ओर"}}}, {"category": "Sm", "key": "29C3", "mappings": {"default": {"default": "सही करने के लिए दो क्षैतिज स्ट्रोक के साथ सर्कल"}}}, {"category": "Sm", "key": "29C4", "mappings": {"default": {"default": "स्क्वार्ड राइजिंग विकर्ण स्लैश"}}}, {"category": "Sm", "key": "29C5", "mappings": {"default": {"default": "स्क्वॉयर फ़ॉलिंग डायगोनल स्लैश"}}}, {"category": "Sm", "key": "29C6", "mappings": {"default": {"default": "चुकता किया हुआ तारक"}}}, {"category": "Sm", "key": "29C7", "mappings": {"default": {"default": "छोटे वृत्त का वर्ग"}}}, {"category": "Sm", "key": "29C8", "mappings": {"default": {"default": "चौकोर वर्ग"}}}, {"category": "Sm", "key": "29C9", "mappings": {"default": {"default": "दो वर्गों में शामिल हो गए"}}}, {"category": "Sm", "key": "29CA", "mappings": {"default": {"default": "डॉट ऊपर के साथ त्रिभुज"}}}, {"category": "Sm", "key": "29CB", "mappings": {"default": {"default": "अंडरबार के साथ त्रिकोण"}}}, {"category": "Sm", "key": "29CC", "mappings": {"default": {"default": "त्रिभुज में एस"}}}, {"category": "Sm", "key": "29CD", "mappings": {"default": {"default": "नीचे में सेरिफ़ के साथ त्रिभुज"}}}, {"category": "Sm", "key": "29CE", "mappings": {"default": {"default": "दाएँ त्रिभुज ऊपर बाएँ त्रिभुज"}}}, {"category": "Sm", "key": "29CF", "mappings": {"default": {"default": "वाम त्रिभुज बगल में खड़ी बार"}}}, {"category": "Sm", "key": "29D0", "mappings": {"default": {"default": "वर्टिकल बार बिसाइड राइट ट्राएंगल"}}}, {"category": "Sm", "key": "29D1", "mappings": {"default": {"default": "लेफ्ट हाफ ब्लैक के साथ बोवी"}}}, {"category": "Sm", "key": "29D2", "mappings": {"default": {"default": "सही हाफ ब्लैक के साथ बोवी"}}}, {"category": "Sm", "key": "29D3", "mappings": {"default": {"default": "काली कटोरी"}}}, {"category": "Sm", "key": "29D4", "mappings": {"default": {"default": "लेफ्ट हाफ ब्लैक के साथ टाइम्स"}}}, {"category": "Sm", "key": "29D5", "mappings": {"default": {"default": "राइट हाफ ब्लैक के साथ टाइम्स"}}}, {"category": "Sm", "key": "29D6", "mappings": {"default": {"default": "सफेद घंटा"}}}, {"category": "Sm", "key": "29D7", "mappings": {"default": {"default": "काला घंटा"}}}, {"category": "Sm", "key": "29DC", "mappings": {"default": {"default": "अधूरा इन्फिनिटी"}}}, {"category": "Sm", "key": "29DD", "mappings": {"default": {"default": "इन्फिनिटी पर टाई"}}}, {"category": "Sm", "key": "29DE", "mappings": {"default": {"default": "अनंत बार ऊर्ध्वाधर के साथ उपेक्षित"}}}, {"category": "Sm", "key": "29DF", "mappings": {"default": {"default": "डबल-एंडेड मल्टीमैप"}}}, {"category": "Sm", "key": "29E0", "mappings": {"default": {"default": "कंटूरेड आउटलाइन के साथ स्क्वायर"}}}, {"category": "Sm", "key": "29E1", "mappings": {"default": {"default": "के रूप में बढ़ता है"}}}, {"category": "Sm", "key": "29E2", "mappings": {"default": {"default": "शफल उत्पाद"}}}, {"category": "Sm", "key": "29E3", "mappings": {"default": {"default": "बराबर और धीमी गति से समानांतर"}}}, {"category": "Sm", "key": "29E4", "mappings": {"default": {"default": "टिल्डे अबव के साथ साइन और स्लेन्डेड पैरेलल"}}}, {"category": "Sm", "key": "29E5", "mappings": {"default": {"default": "आइडेंटिकल टू एंड स्लोड पैरेलल"}}}, {"category": "Sm", "key": "29E6", "mappings": {"default": {"default": "ग्लीच स्टार्क"}}}, {"category": "Sm", "key": "29E7", "mappings": {"default": {"default": "thermodynamic"}}}, {"category": "Sm", "key": "29E8", "mappings": {"default": {"default": "डाउन-पॉइंटिंग ट्राइएंगल विथ लेफ्ट हाफ ब्लैक"}}}, {"category": "Sm", "key": "29E9", "mappings": {"default": {"default": "डाउन-पाइंटिंग ट्रायंगल के साथ राइट हाफ ब्लैक"}}}, {"category": "Sm", "key": "29EB", "mappings": {"default": {"default": "काला लोजेंज"}}}, {"category": "Sm", "key": "29EE", "mappings": {"default": {"default": "त्रुटि-वर्जित श्वेत वर्ग"}}}, {"category": "Sm", "key": "29EF", "mappings": {"default": {"default": "त्रुटि-वर्जित ब्लैक स्क्वायर"}}}, {"category": "Sm", "key": "29F0", "mappings": {"default": {"default": "त्रुटि-वर्जित सफेद हीरा"}}}, {"category": "Sm", "key": "29F1", "mappings": {"default": {"default": "त्रुटि-वर्जित ब्लैक डायमंड"}}}, {"category": "Sm", "key": "29F2", "mappings": {"default": {"default": "त्रुटि-वर्जित श्वेत वृत्त"}}}, {"category": "Sm", "key": "29F3", "mappings": {"default": {"default": "त्रुटि-वर्जित ब्लैक सर्कल"}}}, {"category": "Sm", "key": "29F4", "mappings": {"default": {"default": "नियम विलंबित"}}}, {"category": "Sm", "key": "29F5", "mappings": {"default": {"default": "रिवर्स सॉलिडस"}}}, {"category": "Sm", "key": "29F6", "mappings": {"default": {"default": "ओवरबार के साथ सॉलिडस"}}}, {"category": "Sm", "key": "29F7", "mappings": {"default": {"default": "क्षैतिज स्ट्रोक के साथ सॉलिडस को उल्टा करें"}}}, {"category": "Sm", "key": "29F8", "mappings": {"default": {"default": "बड़ा सॉलिडस"}}}, {"category": "Sm", "key": "29F9", "mappings": {"default": {"default": "बिग रिवर्स सॉलिडस"}}}, {"category": "Sm", "key": "29FA", "mappings": {"default": {"default": "डबल प्लस"}}}, {"category": "Sm", "key": "29FB", "mappings": {"default": {"default": "ट्रिपल प्लस"}}}, {"category": "Sm", "key": "29FE", "mappings": {"default": {"default": "टिनी"}}}, {"category": "Sm", "key": "29FF", "mappings": {"default": {"default": "miny"}}}, {"category": "Sm", "key": "2A00", "mappings": {"default": {"default": "एन-ऐरी सर्किल डॉट"}}}, {"category": "Sm", "key": "2A01", "mappings": {"default": {"default": "एन-ऐरी सर्किल प्लस"}}}, {"category": "Sm", "key": "2A02", "mappings": {"default": {"default": "एन-ऐरी सर्किल टाइम्स"}}}, {"category": "Sm", "key": "2A03", "mappings": {"default": {"default": "डॉट के साथ एन-आर्य संघ संचालक"}}}, {"category": "Sm", "key": "2A04", "mappings": {"default": {"default": "प्लस के साथ एन-आर्य संघ संचालक"}}}, {"category": "Sm", "key": "2A05", "mappings": {"default": {"default": "एन-आर्य स्क्वायर इंटरसेक्शन"}}}, {"category": "Sm", "key": "2A06", "mappings": {"default": {"default": "एन-आर्य स्क्वायर यूनियन"}}}, {"category": "Sm", "key": "2A07", "mappings": {"default": {"default": "दो तार्किक और संचालक"}}}, {"category": "Sm", "key": "2A08", "mappings": {"default": {"default": "दो तार्किक या संचालक"}}}, {"category": "Sm", "key": "2A09", "mappings": {"default": {"default": "एन-आर्य टाइम्स"}}}, {"category": "Sm", "key": "2A0A", "mappings": {"default": {"default": "मोदुलो दो सम"}}}, {"category": "Sm", "key": "2A0B", "mappings": {"default": {"default": "इंटीग्रल के साथ योग"}}}, {"category": "Sm", "key": "2A0C", "mappings": {"default": {"default": "चौगुना इंटीग्रल"}}}, {"category": "Sm", "key": "2A0D", "mappings": {"default": {"default": "परिमित भाग अभिन्न"}}}, {"category": "Sm", "key": "2A0E", "mappings": {"default": {"default": "डबल स्ट्रोक के साथ अभिन्न"}}}, {"category": "Sm", "key": "2A0F", "mappings": {"default": {"default": "स्लैश के साथ इंटीग्रल औसत"}}}, {"category": "Sm", "key": "2A10", "mappings": {"default": {"default": "परिसंचरण समारोह"}}}, {"category": "Sm", "key": "2A11", "mappings": {"default": {"default": "एंटिक्लॉकवाइज इंटीग्रेशन"}}}, {"category": "Sm", "key": "2A12", "mappings": {"default": {"default": "ध्रुव के चारों ओर आयताकार पथ के साथ लाइन एकीकरण"}}}, {"category": "Sm", "key": "2A13", "mappings": {"default": {"default": "ध्रुव के चारों ओर अर्धवृत्ताकार पथ के साथ लाइन एकीकरण"}}}, {"category": "Sm", "key": "2A14", "mappings": {"default": {"default": "ध्रुव को शामिल करते हुए लाइन इंटीग्रेशन नहीं"}}}, {"category": "Sm", "key": "2A15", "mappings": {"default": {"default": "एक बिंदु के आसपास इंटीग्रल"}}}, {"category": "Sm", "key": "2A16", "mappings": {"default": {"default": "चतुर्भुज इंटीग्रल"}}}, {"category": "Sm", "key": "2A18", "mappings": {"default": {"default": "टाइम्स साइन के साथ अभिन्न"}}}, {"category": "Sm", "key": "2A19", "mappings": {"default": {"default": "अभिन्नता के साथ अभिन्न"}}}, {"category": "Sm", "key": "2A1A", "mappings": {"default": {"default": "संघ के साथ अभिन्न"}}}, {"category": "Sm", "key": "2A1B", "mappings": {"default": {"default": "ओवरबार के साथ अभिन्न"}}}, {"category": "Sm", "key": "2A1C", "mappings": {"default": {"default": "अंडरबार के साथ अभिन्न"}}}, {"category": "Sm", "key": "2A1D", "mappings": {"default": {"default": "शामिल हों"}}}, {"category": "Sm", "key": "2A1E", "mappings": {"default": {"default": "बड़े वाम त्रिभुज संचालक"}}}, {"category": "Sm", "key": "2A1F", "mappings": {"default": {"default": "जेड नोटेशन स्कीमा संरचना"}}}, {"category": "Sm", "key": "2A20", "mappings": {"default": {"default": "जेड नोटेशन स्कीम पाइपिंग"}}}, {"category": "Sm", "key": "2A21", "mappings": {"default": {"default": "जेड नोटेशन स्कीमा प्रोजेक्शन"}}}, {"category": "Sm", "key": "2A22", "mappings": {"default": {"default": "प्लस ऊपर छोटे सर्कल के साथ साइन इन करें"}}}, {"category": "Sm", "key": "2A23", "mappings": {"default": {"default": "इसके अलावा ऊपर सर्कमफ्लेक्स एक्सेंट के साथ साइन इन करें"}}}, {"category": "Sm", "key": "2A24", "mappings": {"default": {"default": "इसके अलावा Tilde ऊपर के साथ साइन इन करें"}}}, {"category": "Sm", "key": "2A25", "mappings": {"default": {"default": "प्लस नीचे के साथ साइन इन करें"}}}, {"category": "Sm", "key": "2A26", "mappings": {"default": {"default": "नीचे <PERSON>ilde के साथ साइन इन करें"}}}, {"category": "Sm", "key": "2A27", "mappings": {"default": {"default": "साथ ही सब्स्क्रिप्‍ट टू के साथ साइन करें"}}}, {"category": "Sm", "key": "2A28", "mappings": {"default": {"default": "काले त्रिकोण के साथ प्लस साइन"}}}, {"category": "Sm", "key": "2A29", "mappings": {"default": {"default": "माइनस साइन इन कॉमा ऊपर"}}}, {"category": "Sm", "key": "2A2A", "mappings": {"default": {"default": "नीचे के साथ माइनस साइन"}}}, {"category": "Sm", "key": "2A2B", "mappings": {"default": {"default": "गिरते डॉट्स के साथ माइनस साइन"}}}, {"category": "Sm", "key": "2A2C", "mappings": {"default": {"default": "राइजिंग डॉट्स के साथ माइनस साइन"}}}, {"category": "Sm", "key": "2A2D", "mappings": {"default": {"default": "प्लस साइन इन लेफ्ट हाफ सर्कल"}}}, {"category": "Sm", "key": "2A2E", "mappings": {"default": {"default": "प्लस साइन इन राइट हाफ सर्कल"}}}, {"category": "Sm", "key": "2A2F", "mappings": {"default": {"default": "वेक्टर या क्रॉस उत्पाद"}}}, {"category": "Sm", "key": "2A30", "mappings": {"default": {"default": "गुणन चिह्न ऊपर दिए गए डॉट के साथ"}}}, {"category": "Sm", "key": "2A31", "mappings": {"default": {"default": "अंडरबार के साथ गुणन चिह्न"}}}, {"category": "Sm", "key": "2A32", "mappings": {"default": {"default": "नीचे बंद के साथ सेमीडायरेक्ट प्रोडक्ट"}}}, {"category": "Sm", "key": "2A33", "mappings": {"default": {"default": "लूट उत्पाद"}}}, {"category": "Sm", "key": "2A34", "mappings": {"default": {"default": "बाएं आधे घेरे में गुणन चिह्न"}}}, {"category": "Sm", "key": "2A35", "mappings": {"default": {"default": "गुणन चिह्न सही आधे घेरे में"}}}, {"category": "Sm", "key": "2A36", "mappings": {"default": {"default": "सर्कुलमफ्लेक्स एक्सेंट के साथ सर्किल गुणा गुणा"}}}, {"category": "Sm", "key": "2A37", "mappings": {"default": {"default": "गुणन चिह्न डबल सर्कल में"}}}, {"category": "Sm", "key": "2A38", "mappings": {"default": {"default": "चक्रित चिह्न"}}}, {"category": "Sm", "key": "2A39", "mappings": {"default": {"default": "प्लस साइन इन ट्रायंगल"}}}, {"category": "Sm", "key": "2A3A", "mappings": {"default": {"default": "त्रिभुज में माइनस साइन"}}}, {"category": "Sm", "key": "2A3B", "mappings": {"default": {"default": "त्रिभुज में गुणन चिह्न"}}}, {"category": "Sm", "key": "2A3C", "mappings": {"default": {"default": "आंतरिक उत्पाद"}}}, {"category": "Sm", "key": "2A3D", "mappings": {"default": {"default": "राइटहैंड आंतरिक उत्पाद"}}}, {"category": "Sm", "key": "2A3E", "mappings": {"default": {"default": "z अंकन संबंधपरक रचना"}}}, {"category": "Sm", "key": "2A3F", "mappings": {"default": {"default": "समामेलन या कॉप्टर"}}}, {"category": "Sm", "key": "2A40", "mappings": {"default": {"default": "डॉट के साथ अंतरंगता"}}}, {"category": "Sm", "key": "2A41", "mappings": {"default": {"default": "माइनस साइन के साथ संघ"}}}, {"category": "Sm", "key": "2A42", "mappings": {"default": {"default": "ओवरबार के साथ संघ"}}}, {"category": "Sm", "key": "2A43", "mappings": {"default": {"default": "ओवरबार के साथ अंतरंगता"}}}, {"category": "Sm", "key": "2A44", "mappings": {"default": {"default": "तार्किक और के साथ अंतःक्रिया"}}}, {"category": "Sm", "key": "2A45", "mappings": {"default": {"default": "लॉजिकल या के साथ संघ"}}}, {"category": "Sm", "key": "2A46", "mappings": {"default": {"default": "संघ इन्टॉलरेंस"}}}, {"category": "Sm", "key": "2A47", "mappings": {"default": {"default": "संघ से ऊपर का अंतर"}}}, {"category": "Sm", "key": "2A48", "mappings": {"default": {"default": "संघ इन्टर् बार बार इन्टरसेक्शन"}}}, {"category": "Sm", "key": "2A49", "mappings": {"default": {"default": "संघ के ऊपर बार से ऊपर का अंतर"}}}, {"category": "Sm", "key": "2A4A", "mappings": {"default": {"default": "संघ के पास और संघ के साथ जुड़ गया"}}}, {"category": "Sm", "key": "2A4B", "mappings": {"default": {"default": "इंटरसैक्शन बेसाइड और इंटरसेक्शन के साथ जुड़ गया"}}}, {"category": "Sm", "key": "2A4C", "mappings": {"default": {"default": "सेरिफ के साथ बंद संघ"}}}, {"category": "Sm", "key": "2A4D", "mappings": {"default": {"default": "सेरिफ़ के साथ बंद अंतर्ज्ञान"}}}, {"category": "Sm", "key": "2A4E", "mappings": {"default": {"default": "डबल स्क्वायर इंटरसेक्शन"}}}, {"category": "Sm", "key": "2A4F", "mappings": {"default": {"default": "डबल स्क्वायर यूनियन"}}}, {"category": "Sm", "key": "2A50", "mappings": {"default": {"default": "serifs और लूट उत्पाद के साथ बंद संघ"}}}, {"category": "Sm", "key": "2A51", "mappings": {"default": {"default": "लॉजिकल और डॉट एबव के साथ"}}}, {"category": "Sm", "key": "2A52", "mappings": {"default": {"default": "लॉजिकल या डॉट एबव के साथ"}}}, {"category": "Sm", "key": "2A53", "mappings": {"default": {"default": "डबल तार्किक और"}}}, {"category": "Sm", "key": "2A54", "mappings": {"default": {"default": "डबल तार्किक या"}}}, {"category": "Sm", "key": "2A55", "mappings": {"default": {"default": "दो गहन तार्किक और"}}}, {"category": "Sm", "key": "2A56", "mappings": {"default": {"default": "दो गहन तार्किक या"}}}, {"category": "Sm", "key": "2A57", "mappings": {"default": {"default": "झुका हुआ बड़ा या"}}}, {"category": "Sm", "key": "2A58", "mappings": {"default": {"default": "झुका हुआ बड़ा और"}}}, {"category": "Sm", "key": "2A59", "mappings": {"default": {"default": "लॉजिकल या ओवरलैपिंग लॉजिकल एंड"}}}, {"category": "Sm", "key": "2A5A", "mappings": {"default": {"default": "तार्किक और मध्य स्टेम के साथ"}}}, {"category": "Sm", "key": "2A5B", "mappings": {"default": {"default": "तार्किक या मध्य स्टेम के साथ"}}}, {"category": "Sm", "key": "2A5C", "mappings": {"default": {"default": "तार्किक और क्षैतिज डैश के साथ"}}}, {"category": "Sm", "key": "2A5D", "mappings": {"default": {"default": "तार्किक या क्षैतिज डैश के साथ"}}}, {"category": "Sm", "key": "2A5E", "mappings": {"default": {"default": "तार्किक और डबल ओवरबार के साथ"}}}, {"category": "Sm", "key": "2A5F", "mappings": {"default": {"default": "तार्किक और अंडरबार के साथ"}}}, {"category": "Sm", "key": "2A60", "mappings": {"default": {"default": "लॉजिकल और डबल अंडरबार के साथ"}}}, {"category": "Sm", "key": "2A61", "mappings": {"default": {"default": "अंडरबार के साथ छोटा वी"}}}, {"category": "Sm", "key": "2A62", "mappings": {"default": {"default": "तार्किक या डबल ओवरबार के साथ"}}}, {"category": "Sm", "key": "2A63", "mappings": {"default": {"default": "तार्किक या डबल अंडरबार के साथ"}}}, {"category": "Sm", "key": "2A64", "mappings": {"default": {"default": "जेड नोटेशन डोमेन एंटीरेस्ट्रिशन"}}}, {"category": "Sm", "key": "2A65", "mappings": {"default": {"default": "जेड नोटेशन रेंज एंटीएस्ट्रिक्शन"}}}, {"category": "Sm", "key": "2A66", "mappings": {"default": {"default": "नीचे दिए गए डॉट के साथ साइन इन करें"}}}, {"category": "Sm", "key": "2A67", "mappings": {"default": {"default": "डॉट एबव के साथ पहचान"}}}, {"category": "Sm", "key": "2A68", "mappings": {"default": {"default": "डबल वर्टिकल स्ट्रोक के साथ ट्रिपल हॉरिजॉन्टल बार"}}}, {"category": "Sm", "key": "2A69", "mappings": {"default": {"default": "ट्रिपल वर्टिकल स्ट्रोक के साथ ट्रिपल हॉरिजॉन्टल बार"}}}, {"category": "Sm", "key": "2A6A", "mappings": {"default": {"default": "डॉट ऊपर के साथ टिल्ड"}}}, {"category": "Sm", "key": "2A6B", "mappings": {"default": {"default": "राइजिंग डॉट्स के साथ टिल्ड"}}}, {"category": "Sm", "key": "2A6C", "mappings": {"default": {"default": "समान माइनस समान"}}}, {"category": "Sm", "key": "2A6D", "mappings": {"default": {"default": "डॉट ऊपर के साथ बधाई"}}}, {"category": "Sm", "key": "2A6E", "mappings": {"default": {"default": "तारांकन के साथ बराबर"}}}, {"category": "Sm", "key": "2A6F", "mappings": {"default": {"default": "लगभग सर्कुलफ्लेक्स एक्सेंट के बराबर"}}}, {"category": "Sm", "key": "2A70", "mappings": {"default": {"default": "लग<PERSON>ग बराबर या बराबर"}}}, {"category": "Sm", "key": "2A71", "mappings": {"default": {"default": "बराबर साइन ऊपर प्लस साइन"}}}, {"category": "Sm", "key": "2A72", "mappings": {"default": {"default": "साथ ही साइन इन करें ऊपर बराबर साइन"}}}, {"category": "Sm", "key": "2A73", "mappings": {"default": {"default": "बराबर टिल्ड के बराबर साइन"}}}, {"category": "Sm", "key": "2A74", "mappings": {"default": {"default": "डबल कोलोन बराबर"}}}, {"category": "Sm", "key": "2A75", "mappings": {"default": {"default": "दो लगातार बराबर संकेत"}}}, {"category": "Sm", "key": "2A76", "mappings": {"default": {"default": "तीन लगातार बराबर संकेत"}}}, {"category": "Sm", "key": "2A77", "mappings": {"default": {"default": "बराबर दो डॉट्स ऊपर और दो डॉट्स नीचे के साथ साइन इन करें"}}}, {"category": "Sm", "key": "2A78", "mappings": {"default": {"default": "चार डॉट्स ऊपर के साथ बराबर"}}}, {"category": "Sm", "key": "2A79", "mappings": {"default": {"default": "सर्कल के अंदर कम-से-कम"}}}, {"category": "Sm", "key": "2A7A", "mappings": {"default": {"default": "सर्किल के अंदर ग्रेटर-थान"}}}, {"category": "Sm", "key": "2A7B", "mappings": {"default": {"default": "प्रश्न-चिह्न से ऊपर से कम"}}}, {"category": "Sm", "key": "2A7C", "mappings": {"default": {"default": "ग्रेटर-थान विथ प्रश्न मार्क एबव"}}}, {"category": "Sm", "key": "2A7D", "mappings": {"default": {"default": "कम-से-कम या slanted बराबर"}}}, {"category": "Sm", "key": "2A7E", "mappings": {"default": {"default": "ग्रेटर-थान या Slanted बराबर"}}}, {"category": "Sm", "key": "2A7F", "mappings": {"default": {"default": "कम-थान या slanted बराबर अंदर डॉट के साथ"}}}, {"category": "Sm", "key": "2A80", "mappings": {"default": {"default": "ग्रेटर-थान या स्लोटेड इक्वल टू डॉट इन इनसाइड"}}}, {"category": "Sm", "key": "2A81", "mappings": {"default": {"default": "कम-थन या स्लोड इक्वल को डॉट एबव के साथ"}}}, {"category": "Sm", "key": "2A82", "mappings": {"default": {"default": "ग्रेटर-थान या स्लोड इक्वल टू डॉट डॉट एबव"}}}, {"category": "Sm", "key": "2A83", "mappings": {"default": {"default": "राइट-थॉट या स्लोटेड इक्वल को डॉट एबट राइट के साथ"}}}, {"category": "Sm", "key": "2A84", "mappings": {"default": {"default": "ग्रेटर-थान या स्लोटेड इक्वल टू डॉट एबव लेफ्ट"}}}, {"category": "Sm", "key": "2A85", "mappings": {"default": {"default": "कम-थान या अनुमानित"}}}, {"category": "Sm", "key": "2A86", "mappings": {"default": {"default": "ग्रेटर-थान या अनुमानित"}}}, {"category": "Sm", "key": "2A87", "mappings": {"default": {"default": "कम-थान और एकल-रेखा समान नहीं"}}}, {"category": "Sm", "key": "2A88", "mappings": {"default": {"default": "ग्रेटर-थान और सिंगल-लाइन न के बराबर"}}}, {"category": "Sm", "key": "2A89", "mappings": {"default": {"default": "कम-से-अधिक और अनुमानित नहीं"}}}, {"category": "Sm", "key": "2A8A", "mappings": {"default": {"default": "ग्रेटर-थान और अनुमानित नहीं"}}}, {"category": "Sm", "key": "2A8B", "mappings": {"default": {"default": "ग्रेटर-थान के ऊपर डबल-लाइन के बराबर कम-से-कम"}}}, {"category": "Sm", "key": "2A8C", "mappings": {"default": {"default": "ग्रेटर-थान डबल-लाइन के ऊपर, कम-से-कम के बराबर"}}}, {"category": "Sm", "key": "2A8D", "mappings": {"default": {"default": "समान या समान से कम"}}}, {"category": "Sm", "key": "2A8E", "mappings": {"default": {"default": "ग्रेटर-थान ऊपर समान या समान"}}}, {"category": "Sm", "key": "2A8F", "mappings": {"default": {"default": "ग्रेटर-थान से कम समान"}}}, {"category": "Sm", "key": "2A90", "mappings": {"default": {"default": "ग्रेटर-थान ऊपर समान कम-से-ऊपर"}}}, {"category": "Sm", "key": "2A91", "mappings": {"default": {"default": "ग्रेटर-थान के ऊपर डबल-लाइन बराबर से कम"}}}, {"category": "Sm", "key": "2A92", "mappings": {"default": {"default": "ग्रेटर-थान से कम से कम डबल-लाइन बराबर"}}}, {"category": "Sm", "key": "2A93", "mappings": {"default": {"default": "ग्रेटर-थान से कम के बराबर स्लैटल से कम से कम से कम के बराबर"}}}, {"category": "Sm", "key": "2A94", "mappings": {"default": {"default": "ग्रेटर-थान से ऊपर स्लैन्ड इक्वेल से कम से कम-से-ऊपर स्लैन्ड इक्वेल"}}}, {"category": "Sm", "key": "2A95", "mappings": {"default": {"default": "slanted बराबर या उससे कम"}}}, {"category": "Sm", "key": "2A96", "mappings": {"default": {"default": "slanted बराबर या अधिक से अधिक"}}}, {"category": "Sm", "key": "2A97", "mappings": {"default": {"default": "डॉट इनसाइड के साथ Slanted बराबर या उससे कम"}}}, {"category": "Sm", "key": "2A98", "mappings": {"default": {"default": "डॉटल इनसाइड के साथ Slanted बराबर या ग्रेटर-थान"}}}, {"category": "Sm", "key": "2A99", "mappings": {"default": {"default": "डबल-लाइन समान या उससे कम"}}}, {"category": "Sm", "key": "2A9A", "mappings": {"default": {"default": "डबल-लाइन के बराबर या ग्रेटर-थान"}}}, {"category": "Sm", "key": "2A9B", "mappings": {"default": {"default": "डबल-लाइन slanted बराबर या उससे कम"}}}, {"category": "Sm", "key": "2A9C", "mappings": {"default": {"default": "डबल-लाइन Slanted बराबर या अधिक से अधिक-थान"}}}, {"category": "Sm", "key": "2A9D", "mappings": {"default": {"default": "समान या कम-से-अधिक"}}}, {"category": "Sm", "key": "2A9E", "mappings": {"default": {"default": "इसी तरह या ग्रेटर-थान"}}}, {"category": "Sm", "key": "2A9F", "mappings": {"default": {"default": "समान-से ऊपर समान कम-से-अधिक समान चिह्न"}}}, {"category": "Sm", "key": "2AA0", "mappings": {"default": {"default": "ग्रेटर-थान से ऊपर समान समान संकेत"}}}, {"category": "Sm", "key": "2AA1", "mappings": {"default": {"default": "डबल नेस्टेड कम-थान"}}}, {"category": "Sm", "key": "2AA2", "mappings": {"default": {"default": "डबल नेस्टेड ग्रेटर-थान"}}}, {"category": "Sm", "key": "2AA3", "mappings": {"default": {"default": "अंडरबार के साथ डबल नेस्टेड लेस-थैन"}}}, {"category": "Sm", "key": "2AA4", "mappings": {"default": {"default": "ग्रेटर-थान कम-से-अधिक ओवरलैपिंग"}}}, {"category": "Sm", "key": "2AA5", "mappings": {"default": {"default": "ग्रेटर-थान बगल में कम-से-कम"}}}, {"category": "Sm", "key": "2AA6", "mappings": {"default": {"default": "कम-से-कम बंद वक्र द्वारा"}}}, {"category": "Sm", "key": "2AA7", "mappings": {"default": {"default": "ग्रेटर-थान क्लोज्ड बाई कर्व"}}}, {"category": "Sm", "key": "2AA8", "mappings": {"default": {"default": "कम-से-कम बंद वक्र के ऊपर बंद बराबर के बराबर"}}}, {"category": "Sm", "key": "2AA9", "mappings": {"default": {"default": "ग्रेटर-थान को बंद करके कर्व से ऊपर स्लोटेड समान"}}}, {"category": "Sm", "key": "2AAA", "mappings": {"default": {"default": "से छोटा"}}}, {"category": "Sm", "key": "2AAB", "mappings": {"default": {"default": "से भी बड़ा"}}}, {"category": "Sm", "key": "2AAC", "mappings": {"default": {"default": "से छोटा या बराबर"}}}, {"category": "Sm", "key": "2AAD", "mappings": {"default": {"default": "से बड़ा या बराबर"}}}, {"category": "Sm", "key": "2AAE", "mappings": {"default": {"default": "बम्पी ऊपर के साथ साइन इन करें"}}}, {"category": "Sm", "key": "2AAF", "mappings": {"default": {"default": "सिंगल-लाइन बराबर साइन से ऊपर"}}}, {"category": "Sm", "key": "2AB0", "mappings": {"default": {"default": "सिंगल-लाइन के बराबर साइन के ऊपर सफलता"}}}, {"category": "Sm", "key": "2AB1", "mappings": {"default": {"default": "सिंगल-लाइन न के बराबर प्रीवर"}}}, {"category": "Sm", "key": "2AB2", "mappings": {"default": {"default": "एकल-पंक्ति से ऊपर की सफलता नहीं के बराबर"}}}, {"category": "Sm", "key": "2AB3", "mappings": {"default": {"default": "बराबरी से ऊपर के हस्ताक्षर"}}}, {"category": "Sm", "key": "2AB4", "mappings": {"default": {"default": "बराबर के ऊपर हस्ताक्षर"}}}, {"category": "Sm", "key": "2AB5", "mappings": {"default": {"default": "इसके बाद के संस्करण के बराबर नहीं है"}}}, {"category": "Sm", "key": "2AB6", "mappings": {"default": {"default": "इसके बाद के संस्करण के बराबर नहीं है"}}}, {"category": "Sm", "key": "2AB7", "mappings": {"default": {"default": "लग<PERSON>ग बराबर के बराबर"}}}, {"category": "Sm", "key": "2AB8", "mappings": {"default": {"default": "लगभग बराबर के ऊपर सफलता"}}}, {"category": "Sm", "key": "2AB9", "mappings": {"default": {"default": "लगभग बराबर नहीं के ऊपर से पहले"}}}, {"category": "Sm", "key": "2ABA", "mappings": {"default": {"default": "लगभग बराबर नहीं के ऊपर सफलता"}}}, {"category": "Sm", "key": "2ABB", "mappings": {"default": {"default": "डबल प्राथमिकताएं"}}}, {"category": "Sm", "key": "2ABC", "mappings": {"default": {"default": "डबल सफलता"}}}, {"category": "Sm", "key": "2ABD", "mappings": {"default": {"default": "डॉट के साथ सबसेट"}}}, {"category": "Sm", "key": "2ABE", "mappings": {"default": {"default": "डॉट के साथ सुपरसेट"}}}, {"category": "Sm", "key": "2ABF", "mappings": {"default": {"default": "प्लस साइन डाउन के साथ सबसेट"}}}, {"category": "Sm", "key": "2AC0", "mappings": {"default": {"default": "प्लस साइन डाउन के साथ सुपरसेट"}}}, {"category": "Sm", "key": "2AC1", "mappings": {"default": {"default": "नीचे गुणन चिह्न के साथ सबसेट"}}}, {"category": "Sm", "key": "2AC2", "mappings": {"default": {"default": "गुणन के साथ सुपरसेट नीचे साइन इन करें"}}}, {"category": "Sm", "key": "2AC3", "mappings": {"default": {"default": "उपर्युक्त या बराबर के साथ डॉट के ऊपर"}}}, {"category": "Sm", "key": "2AC4", "mappings": {"default": {"default": "या इसके बाद के संस्करण का डॉट के ऊपर के साथ समतुल्य"}}}, {"category": "Sm", "key": "2AC5", "mappings": {"default": {"default": "ऊपर के बराबर के संकेत पर हस्ताक्षर"}}}, {"category": "Sm", "key": "2AC6", "mappings": {"default": {"default": "ऊपर के साइन का सुपरसेट"}}}, {"category": "Sm", "key": "2AC7", "mappings": {"default": {"default": "सब से ऊपर टिल्ड का सबसेट"}}}, {"category": "Sm", "key": "2AC8", "mappings": {"default": {"default": "ऊपर टिल्डे का सुपरसेट"}}}, {"category": "Sm", "key": "2AC9", "mappings": {"default": {"default": "लगभग बराबर के ऊपर का सबसेट"}}}, {"category": "Sm", "key": "2ACA", "mappings": {"default": {"default": "लगभग बराबर के ऊपर का सुपरसेट"}}}, {"category": "Sm", "key": "2ACB", "mappings": {"default": {"default": "इसके बाद के संस्करण की तुलना बराबर नहीं"}}}, {"category": "Sm", "key": "2ACC", "mappings": {"default": {"default": "ऊपर का सुपरसेट न के बराबर"}}}, {"category": "Sm", "key": "2ACD", "mappings": {"default": {"default": "स्क्वायर लेफ्ट ओपन बॉक्स"}}}, {"category": "Sm", "key": "2ACE", "mappings": {"default": {"default": "स्क्वायर राइट ओपन बॉक्स"}}}, {"category": "Sm", "key": "2ACF", "mappings": {"default": {"default": "बंद सबसेट"}}}, {"category": "Sm", "key": "2AD0", "mappings": {"default": {"default": "बंद सुपरसेट"}}}, {"category": "Sm", "key": "2AD1", "mappings": {"default": {"default": "बंद सबसेट या बराबर"}}}, {"category": "Sm", "key": "2AD2", "mappings": {"default": {"default": "बंद सुपरसेट या समान करने के लिए"}}}, {"category": "Sm", "key": "2AD3", "mappings": {"default": {"default": "सुपरसेट के ऊपर सबसेट"}}}, {"category": "Sm", "key": "2AD4", "mappings": {"default": {"default": "उपरसेट उपरसेट"}}}, {"category": "Sm", "key": "2AD5", "mappings": {"default": {"default": "सबसेट के ऊपर सबसेट"}}}, {"category": "Sm", "key": "2AD6", "mappings": {"default": {"default": "सुपरसेट से ऊपर सुपरसेट"}}}, {"category": "Sm", "key": "2AD7", "mappings": {"default": {"default": "सुपरसेट बगल में सबसेट"}}}, {"category": "Sm", "key": "2AD8", "mappings": {"default": {"default": "सुपरसेट बिसाइड एंड ज्वाइन बाई डैश विद सबसेट"}}}, {"category": "Sm", "key": "2AD9", "mappings": {"default": {"default": "नीचे की ओर खुलने का तत्व"}}}, {"category": "Sm", "key": "2ADA", "mappings": {"default": {"default": "टी टॉप के साथ पिचफोर्क"}}}, {"category": "Sm", "key": "2ADB", "mappings": {"default": {"default": "ट्रांसवर्सल इंटरसेक्शन"}}}, {"category": "Sm", "key": "2ADC", "mappings": {"default": {"default": "forking"}}}, {"category": "Sm", "key": "2ADD", "mappings": {"default": {"default": "nonforking"}}}, {"category": "Sm", "key": "2ADE", "mappings": {"default": {"default": "शॉर्ट लेफ्ट टैक"}}}, {"category": "Sm", "key": "2ADF", "mappings": {"default": {"default": "शॉर्ट डाउन टैक"}}}, {"category": "Sm", "key": "2AE0", "mappings": {"default": {"default": "शॉर्ट अप टैक"}}}, {"category": "Sm", "key": "2AE1", "mappings": {"default": {"default": "एस के साथ लंबवत"}}}, {"category": "Sm", "key": "2AE2", "mappings": {"default": {"default": "वर्टिकल बार ट्रिपल राइट टर्नस्टाइल"}}}, {"category": "Sm", "key": "2AE3", "mappings": {"default": {"default": "डबल वर्टिकल बार लेफ्ट टर्नस्टाइल"}}}, {"category": "Sm", "key": "2AE4", "mappings": {"default": {"default": "वर्टिकल बार डबल लेफ्ट टर्नस्टाइल"}}}, {"category": "Sm", "key": "2AE5", "mappings": {"default": {"default": "डबल वर्टिकल बार डबल लेफ्ट टर्नस्टाइल"}}}, {"category": "Sm", "key": "2AE6", "mappings": {"default": {"default": "डबल वर्टिकल के लेफ्ट मेंबर से लॉन्ग डैश"}}}, {"category": "Sm", "key": "2AE7", "mappings": {"default": {"default": "ओवरबार के साथ शॉर्ट डाउन टैक"}}}, {"category": "Sm", "key": "2AE8", "mappings": {"default": {"default": "अंडरबार के साथ शॉर्ट अप टैक"}}}, {"category": "Sm", "key": "2AE9", "mappings": {"default": {"default": "शॉर्ट अप टैक ऊपर शॉर्ट डाउन टैक"}}}, {"category": "Sm", "key": "2AEA", "mappings": {"default": {"default": "डबल डाउन टैक"}}}, {"category": "Sm", "key": "2AEB", "mappings": {"default": {"default": "डबल अप टैक"}}}, {"category": "Sm", "key": "2AEC", "mappings": {"default": {"default": "डबल स्ट्रोक साइन नहीं"}}}, {"category": "Sm", "key": "2AED", "mappings": {"default": {"default": "उल्टा डबल स्ट्रोक नहीं साइन"}}}, {"category": "Sm", "key": "2AEE", "mappings": {"default": {"default": "रिवर्सेड नेगेशन स्लैश के साथ विभाजन नहीं करता है"}}}, {"category": "Sm", "key": "2AEF", "mappings": {"default": {"default": "सर्कल के ऊपर के साथ लंबवत रेखा"}}}, {"category": "Sm", "key": "2AF0", "mappings": {"default": {"default": "नीचे सर्कल के साथ खड़ी रेखा"}}}, {"category": "Sm", "key": "2AF1", "mappings": {"default": {"default": "नीचे सर्कल के साथ नीचे कील"}}}, {"category": "Sm", "key": "2AF2", "mappings": {"default": {"default": "क्षैतिज स्ट्रोक के साथ समानांतर"}}}, {"category": "Sm", "key": "2AF3", "mappings": {"default": {"default": "टिल्ड के साथ समानांतर"}}}, {"category": "Sm", "key": "2AF4", "mappings": {"default": {"default": "ट्रिपल वर्टिकल बार बाइनरी रिलेशन"}}}, {"category": "Sm", "key": "2AF5", "mappings": {"default": {"default": "क्षैतिज स्ट्रोक के साथ ट्रिपल वर्टिकल बार"}}}, {"category": "Sm", "key": "2AF6", "mappings": {"default": {"default": "ट्रिपल कर्नल"}}}, {"category": "Sm", "key": "2AF7", "mappings": {"default": {"default": "ट्रिपल नेस्ट कम-थान"}}}, {"category": "Sm", "key": "2AF8", "mappings": {"default": {"default": "ट्रिपल नेस्टेड ग्रेटर-थान"}}}, {"category": "Sm", "key": "2AF9", "mappings": {"default": {"default": "डबल-लाइन कम-से-कम या बराबर होना चाहिए"}}}, {"category": "Sm", "key": "2AFA", "mappings": {"default": {"default": "डबल-लाइन Slanted ग्रेटर-थान या समान टू"}}}, {"category": "Sm", "key": "2AFB", "mappings": {"default": {"default": "ट्रिपल सॉलिडस बाइनरी रिलेशन"}}}, {"category": "Sm", "key": "2AFC", "mappings": {"default": {"default": "बड़े ट्रिपल वर्टिकल बार"}}}, {"category": "Sm", "key": "2AFD", "mappings": {"default": {"default": "डबल सॉलिडस"}}}, {"category": "Sm", "key": "2AFE", "mappings": {"default": {"default": "सफेद खड़ी पट्टी"}}}, {"category": "Sm", "key": "2AFF", "mappings": {"default": {"default": "एन-आर्य व्हाइट वर्टिकल बार"}}}, {"category": "Pd", "key": "301C", "mappings": {"default": {"default": "वेव डैश"}}}, {"category": "Po", "key": "FE10", "mappings": {"default": {"default": "वर्टिकल कॉमा के लिए प्रस्तुति फॉर्म"}}}, {"category": "Po", "key": "FE13", "mappings": {"default": {"default": "कार्यक्षेत्र बृहदान्त्र के लिए प्रस्तुति प्रपत्र"}}}, {"category": "Po", "key": "FE14", "mappings": {"default": {"default": "कार्यक्षेत्र अर्धविराम के लिए प्रस्तुति प्रपत्र"}}}, {"category": "Po", "key": "FE15", "mappings": {"default": {"default": "कार्यक्षेत्र विस्मयादिबोधक मार्क के लिए प्रस्तुति फॉर्म"}}}, {"category": "Po", "key": "FE16", "mappings": {"default": {"default": "वर्टिकल क्वेश्चन मार्क के लिए प्रस्तुति फॉर्म"}}}, {"category": "Po", "key": "FE19", "mappings": {"default": {"default": "वर्टिकल हॉरिजॉन्टल एलिप्सिस के लिए प्रस्तुति फॉर्म"}}}, {"category": "Po", "key": "FE30", "mappings": {"default": {"default": "वर्टिकल टू डॉट लीडर के लिए प्रस्तुति फॉर्म"}}}, {"category": "Pd", "key": "FE31", "mappings": {"default": {"default": "वर्टिकल एम डैश के लिए प्रस्तुति फॉर्म"}}}, {"category": "Pd", "key": "FE32", "mappings": {"default": {"default": "वर्टिकल एन डैश के लिए प्रस्तुति फॉर्म"}}}, {"category": "Pc", "key": "FE33", "mappings": {"default": {"default": "वर्टिकल लो लाइन के लिए प्रस्तुति फॉर्म"}}}, {"category": "Pc", "key": "FE34", "mappings": {"default": {"default": "वर्टिकल वेवी लो लाइन के लिए प्रस्तुति फॉर्म"}}}, {"category": "Po", "key": "FE45", "mappings": {"default": {"default": "तिल डॉट"}}}, {"category": "Po", "key": "FE46", "mappings": {"default": {"default": "सफेद तिल डॉट"}}}, {"category": "Po", "key": "FE49", "mappings": {"default": {"default": "पानी का छींटा"}}}, {"category": "Po", "key": "FE4A", "mappings": {"default": {"default": "centreline Overline"}}}, {"category": "Po", "key": "FE4B", "mappings": {"default": {"default": "लहराती ओवरलाइन"}}}, {"category": "Po", "key": "FE4C", "mappings": {"default": {"default": "डबल लहराती ओवरलाइन"}}}, {"category": "Pc", "key": "FE4D", "mappings": {"default": {"default": "कम लाइन का धब्बा"}}}, {"category": "Pc", "key": "FE4E", "mappings": {"default": {"default": "सेंट्रलाइन लो लाइन"}}}, {"category": "Pc", "key": "FE4F", "mappings": {"default": {"default": "लहराती कम लाइन"}}}, {"category": "Po", "key": "FE50", "mappings": {"default": {"default": "छोटा कोमा"}}}, {"category": "Po", "key": "FE52", "mappings": {"default": {"default": "छोटा पूर्ण विराम"}}}, {"category": "Po", "key": "FE54", "mappings": {"default": {"default": "छोटा अर्धविराम"}}}, {"category": "Po", "key": "FE55", "mappings": {"default": {"default": "छोटा कोलोन"}}}, {"category": "Po", "key": "FE56", "mappings": {"default": {"default": "लघु प्रश्न चिह्न"}}}, {"category": "Po", "key": "FE57", "mappings": {"default": {"default": "छोटा विस्मयादिबोधक चिह्न"}}}, {"category": "Pd", "key": "FE58", "mappings": {"default": {"default": "छोटा एम डैश"}}}, {"category": "Po", "key": "FE5F", "mappings": {"default": {"default": "छोटी संख्या का चिन्ह"}}}, {"category": "Po", "key": "FE60", "mappings": {"default": {"default": "छोटा आम्रपांड"}}}, {"category": "Po", "key": "FE61", "mappings": {"default": {"default": "छोटा सरक"}}}, {"category": "Sm", "key": "FE62", "mappings": {"default": {"default": "छोटा प्लस साइन"}}}, {"category": "Pd", "key": "FE63", "mappings": {"default": {"default": "छोटा हाइफ़न-माइनस"}}}, {"category": "Sm", "key": "FE64", "mappings": {"default": {"default": "छोटा कम-थान साइन"}}}, {"category": "Sm", "key": "FE65", "mappings": {"default": {"default": "छोटा ग्रेटर-थान साइन"}}}, {"category": "Sm", "key": "FE66", "mappings": {"default": {"default": "छोटे बराबर का चिन्ह"}}}, {"category": "Po", "key": "FE68", "mappings": {"default": {"default": "छोटा रिवर्स सॉलिडस"}}}, {"category": "Sc", "key": "FE69", "mappings": {"default": {"default": "छोटा डॉलर चिन्ह"}}}, {"category": "Po", "key": "FE6A", "mappings": {"default": {"default": "छोटा सा प्रतिशत चिन्ह"}}}, {"category": "Po", "key": "FE6B", "mappings": {"default": {"default": "छोटे वाणिज्यिक पर"}}}, {"category": "Po", "key": "FF01", "mappings": {"default": {"default": "पूर्ण विस्मयादिबोधक चिह्न"}}}, {"category": "Po", "key": "FF02", "mappings": {"default": {"default": "फुल विमोचन कोटेशन मार्क"}}}, {"category": "Po", "key": "FF03", "mappings": {"default": {"default": "पूर्णविराम नंबर साइन"}}}, {"category": "Sc", "key": "FF04", "mappings": {"default": {"default": "फुलबॉर्न डॉलर साइन"}}}, {"category": "Po", "key": "FF05", "mappings": {"default": {"default": "पूर्णता प्रतिशतता संकेत"}}}, {"category": "Po", "key": "FF06", "mappings": {"default": {"default": "पूर्णविराम एम्परसेंड"}}}, {"category": "Po", "key": "FF07", "mappings": {"default": {"default": "पूर्ण विराम एपोस्ट्रोफ"}}}, {"category": "Po", "key": "FF0A", "mappings": {"default": {"default": "फ़ुल विस्‍तार Asterisk"}}}, {"category": "Sm", "key": "FF0B", "mappings": {"default": {"default": "फुलप्रूफ प्लस साइन"}}}, {"category": "Po", "key": "FF0C", "mappings": {"default": {"default": "पूर्णविराम कोमा"}}}, {"category": "Pd", "key": "FF0D", "mappings": {"default": {"default": "फुल एक्सपोर्टर हैफेन-माइनस"}}}, {"category": "Po", "key": "FF0E", "mappings": {"default": {"default": "पूर्णविराम पूर्ण विराम"}}}, {"category": "Po", "key": "FF0F", "mappings": {"default": {"default": "फुलफॉरमेंस सॉलिडस"}}}, {"category": "Po", "key": "FF1A", "mappings": {"default": {"default": "पूर्णविराम बृहदान्त्र"}}}, {"category": "Po", "key": "FF1B", "mappings": {"default": {"default": "पूर्णविराम अर्धविराम"}}}, {"category": "Sm", "key": "FF1C", "mappings": {"default": {"default": "पूर्ण विचलन कम-थान साइन"}}}, {"category": "Sm", "key": "FF1D", "mappings": {"default": {"default": "पूर्णविराम बराबर होता है"}}}, {"category": "Sm", "key": "FF1E", "mappings": {"default": {"default": "पूर्णता ग्रेटर-थन साइन"}}}, {"category": "Po", "key": "FF1F", "mappings": {"default": {"default": "पूर्णविराम प्रश्न मार्क"}}}, {"category": "Po", "key": "FF20", "mappings": {"default": {"default": "पूर्ण विचलन वाणिज्यिक पर"}}}, {"category": "Po", "key": "FF3C", "mappings": {"default": {"default": "फुलवॉर्शन रिवर्स सॉलिडस"}}}, {"category": "Sk", "key": "FF3E", "mappings": {"default": {"default": "पूर्णविराम सर्कुफ्लेक्स एक्सेंट"}}}, {"category": "Pc", "key": "FF3F", "mappings": {"default": {"default": "पूर्णविराम निम्न रेखा"}}}, {"category": "Sk", "key": "FF40", "mappings": {"default": {"default": "फुल विवर ग्रेव एक्सेंट"}}}, {"category": "Sm", "key": "FF5C", "mappings": {"default": {"default": "पूर्णविराम ऊर्ध्वाधर रेखा"}}}, {"category": "Sm", "key": "FF5E", "mappings": {"default": {"default": "पूर्णविराम टिल्डे"}}}, {"category": "Sc", "key": "FFE0", "mappings": {"default": {"default": "फुल एक्सपेंशन सेंट साइन"}}}, {"category": "Sc", "key": "FFE1", "mappings": {"default": {"default": "पूर्णविराम पाउंड साइन"}}}, {"category": "Sm", "key": "FFE2", "mappings": {"default": {"default": "पूर्णविराम नहीं हस्ताक्षर"}}}, {"category": "Sk", "key": "FFE3", "mappings": {"default": {"default": "फुलवार्ड मैक्रॉन"}}}, {"category": "So", "key": "FFE4", "mappings": {"default": {"default": "पूर्ण विराम ब्रोकन बार"}}}, {"category": "Sc", "key": "FFE5", "mappings": {"default": {"default": "पूर्णविराम येन चिन्ह"}}}, {"category": "Sc", "key": "FFE6", "mappings": {"default": {"default": "फुल विओक साइन"}}}, {"category": "So", "key": "FFE8", "mappings": {"default": {"default": "आधीर्फ़ॉर्म फॉर्म लाइट वर्टिकल"}}}, {"category": "So", "key": "FFED", "mappings": {"default": {"default": "हाफ मूवमेंट ब्लैक स्क्वायर"}}}, {"category": "So", "key": "FFEE", "mappings": {"default": {"default": "अर्धवार्षिक व्हाइट सर्कल"}}}], "hi/symbols/math_whitespace.min": [{"locale": "hi"}, {"category": "Zs", "key": "0020", "mappings": {"default": {"default": "अंतरिक्ष"}}}, {"category": "Zs", "key": "00A0", "mappings": {"default": {"default": "नो-ब्रेक स्पेस"}}}, {"category": "Cf", "key": "00AD", "mappings": {"default": {"default": "शीतल हाइफ़न"}}}, {"category": "Zs", "key": "2000", "mappings": {"default": {"default": "एन क्वाड"}}}, {"category": "Zs", "key": "2001", "mappings": {"default": {"default": "उन्हें क्वाड"}}}, {"category": "Zs", "key": "2002", "mappings": {"default": {"default": "एन स्पेस"}}}, {"category": "Zs", "key": "2003", "mappings": {"default": {"default": "उन्हें अंतरिक्ष"}}}, {"category": "Zs", "key": "2004", "mappings": {"default": {"default": "तीन-प्रति-एम स्पेस"}}}, {"category": "Zs", "key": "2005", "mappings": {"default": {"default": "चार-प्रति-एम अंतरिक्ष"}}}, {"category": "Zs", "key": "2006", "mappings": {"default": {"default": "सिक्स-पर-एम स्पेस"}}}, {"category": "Zs", "key": "2007", "mappings": {"default": {"default": "चित्रा स्थान"}}}, {"category": "Zs", "key": "2008", "mappings": {"default": {"default": "विराम चिह्न स्थान"}}}, {"category": "Zs", "key": "2009", "mappings": {"default": {"default": "पतली जगह"}}}, {"category": "Zs", "key": "200A", "mappings": {"default": {"default": "बाल स्थान"}}}, {"category": "Cf", "key": "200B", "mappings": {"default": {"default": "शून्य चौड़ाई स्थान"}}}, {"category": "Cf", "key": "200C", "mappings": {"default": {"default": "शून्य चौड़ाई गैर-योजक"}}}, {"category": "Cf", "key": "200D", "mappings": {"default": {"default": "शून्य चौड़ाई योजक"}}}, {"category": "Cf", "key": "200E", "mappings": {"default": {"default": "लेफ्ट-टू-राइट मार्क"}}}, {"category": "Cf", "key": "200F", "mappings": {"default": {"default": "दाएं-बाएं निशान"}}}, {"category": "Zl", "key": "2028", "mappings": {"default": {"default": "लाइन सेपरेटर"}}}, {"category": "Zp", "key": "2029", "mappings": {"default": {"default": "पैराग्राफ सेपरेटर"}}}, {"category": "Cf", "key": "202A", "mappings": {"default": {"default": "लेफ्ट-टू-राइट एंबेडिंग"}}}, {"category": "Cf", "key": "202B", "mappings": {"default": {"default": "राइट-टू-लेफ्ट एंबेडिंग"}}}, {"category": "Cf", "key": "202C", "mappings": {"default": {"default": "पॉप दिशात्मक स्वरूपण"}}}, {"category": "Cf", "key": "202D", "mappings": {"default": {"default": "बाएं से दाएं ओवरराइड"}}}, {"category": "Cf", "key": "202E", "mappings": {"default": {"default": "राइट-टू-लेफ्ट ओवरराइड"}}}, {"category": "Zs", "key": "202F", "mappings": {"default": {"default": "नैरो-ब्रेक स्पेस"}}}, {"category": "Zs", "key": "205F", "mappings": {"default": {"default": "मध्यम गणितीय स्थान"}}}, {"category": "Cf", "key": "2060", "mappings": {"default": {"default": "शब्द योजक"}}}, {"category": "Cf", "key": "2061", "mappings": {"default": {"default": "का फलन"}}}, {"category": "Cf", "key": "2062", "mappings": {"default": {"default": "गुणन चिह्न"}}}, {"category": "Cf", "key": "2063", "mappings": {"default": {"default": "अल्पविराम"}}}, {"category": "Cf", "key": "2064", "mappings": {"default": {"default": "पूर्णांक"}}}, {"category": "Cf", "key": "206A", "mappings": {"default": {"default": "इनहिबिट सिमेट्रिक स्वैपिंग"}}}, {"category": "Cf", "key": "206B", "mappings": {"default": {"default": "सममित स्वैपिंग सक्रिय करें"}}}, {"category": "Cf", "key": "206E", "mappings": {"default": {"default": "राष्ट्रीय अंक आकार"}}}, {"category": "Cf", "key": "206F", "mappings": {"default": {"default": "नाममात्र का अंक आकार"}}}, {"category": "Cf", "key": "FEFF", "mappings": {"default": {"default": "शून्य चौड़ाई नो-ब्रेक स्पेस"}}}, {"category": "Cf", "key": "FFF9", "mappings": {"default": {"default": "इंटरलीनियर एनोटेशन एंकर"}}}, {"category": "Cf", "key": "FFFA", "mappings": {"default": {"default": "इंटरलीनियर एनोटेशन सेपरेटर"}}}, {"category": "Cf", "key": "FFFB", "mappings": {"default": {"default": "इंटरलीनियर एनोटेशन टर्मिनेटर"}}}], "hi/symbols/other_stars.min": [{"locale": "hi"}, {"category": "So", "key": "23E8", "mappings": {"default": {"default": "दशम<PERSON>व प्रतिपादक प्रतीक"}}}, {"category": "So", "key": "2605", "mappings": {"default": {"default": "काला तारा"}}}, {"category": "So", "key": "2606", "mappings": {"default": {"default": "सफेद सितारा"}}}, {"category": "So", "key": "26AA", "mappings": {"default": {"default": "मध्यम सफेद वृत्त"}}}, {"category": "So", "key": "26AB", "mappings": {"default": {"default": "मीडियम ब्लैक सर्कल"}}}, {"category": "So", "key": "2705", "mappings": {"default": {"default": "सफेद हैवी चेक मार्क"}}}, {"category": "So", "key": "2713", "mappings": {"default": {"default": "सही का निशान"}}}, {"category": "So", "key": "2714", "mappings": {"default": {"default": "हैवी चेक मार्क"}}}, {"category": "So", "key": "2715", "mappings": {"default": {"default": "गुणन X"}}}, {"category": "So", "key": "2716", "mappings": {"default": {"default": "भारी गुणा X"}}}, {"category": "So", "key": "2717", "mappings": {"default": {"default": "बैलट एक्स"}}}, {"category": "So", "key": "2718", "mappings": {"default": {"default": "भारी मतवाला एक्स"}}}, {"category": "So", "key": "271B", "mappings": {"default": {"default": "ओपन सेंटर क्रॉस"}}}, {"category": "So", "key": "271C", "mappings": {"default": {"default": "भारी खुला केंद्र क्रॉस"}}}, {"category": "So", "key": "2720", "mappings": {"default": {"default": "माल्टीज़ क्रॉस"}}}, {"category": "So", "key": "2721", "mappings": {"default": {"default": "डेविड का सितारा"}}}, {"category": "So", "key": "2722", "mappings": {"default": {"default": "फोर टियरड्रॉप-स्पोक्ड एस्टरिस्क"}}}, {"category": "So", "key": "2723", "mappings": {"default": {"default": "फोर बैलून-स्पोक्ड एस्टरिस्क"}}}, {"category": "So", "key": "2724", "mappings": {"default": {"default": "हैवी फोर बलून-स्पोक्ड एस्टरिस्क"}}}, {"category": "So", "key": "2725", "mappings": {"default": {"default": "फोर क्लब-स्पोक्ड एस्टरिस्क"}}}, {"category": "So", "key": "2726", "mappings": {"default": {"default": "ब्लैक फोर पॉइंटेड स्टार"}}}, {"category": "So", "key": "2727", "mappings": {"default": {"default": "व्हाइट फोर पॉइंटेड स्टार"}}}, {"category": "So", "key": "2728", "mappings": {"default": {"default": "निखर उठती"}}}, {"category": "So", "key": "2729", "mappings": {"default": {"default": "तनाव की रूपरेखा सफेद सितारा"}}}, {"category": "So", "key": "272A", "mappings": {"default": {"default": "सफेद स्टार की परिक्रमा की"}}}, {"category": "So", "key": "272B", "mappings": {"default": {"default": "ओपन सेंटर ब्लैक स्टार"}}}, {"category": "So", "key": "272C", "mappings": {"default": {"default": "ब्लैक सेंटर व्हाइट स्टार"}}}, {"category": "So", "key": "272D", "mappings": {"default": {"default": "उल्लिखित ब्लैक स्टार"}}}, {"category": "So", "key": "272E", "mappings": {"default": {"default": "हैवी आउटलाइन्ड ब्लैक स्टार"}}}, {"category": "So", "key": "272F", "mappings": {"default": {"default": "पिनव्हील स्टार"}}}, {"category": "So", "key": "2730", "mappings": {"default": {"default": "छायांकित श्वेत तारा"}}}, {"category": "So", "key": "2731", "mappings": {"default": {"default": "भारी तारांकन"}}}, {"category": "So", "key": "2732", "mappings": {"default": {"default": "खुला केंद्र तारांकन चिह्न"}}}, {"category": "So", "key": "2733", "mappings": {"default": {"default": "आठ प्रवक्ता ने तारांकित किया"}}}, {"category": "So", "key": "2734", "mappings": {"default": {"default": "आठ प्वाइंट ब्लैक स्टार"}}}, {"category": "So", "key": "2735", "mappings": {"default": {"default": "आठ पॉइंट पिनव्हील स्टार"}}}, {"category": "So", "key": "2736", "mappings": {"default": {"default": "सिक्स पॉइंटेड ब्लैक स्टार"}}}, {"category": "So", "key": "2739", "mappings": {"default": {"default": "बारह अंक ब्लैक स्टार"}}}, {"category": "So", "key": "273A", "mappings": {"default": {"default": "सोलह पॉइंट वाला तारांकन चिह्न"}}}, {"category": "So", "key": "273B", "mappings": {"default": {"default": "टियरड्रॉप-स्पोक्ड एस्टरिस्क"}}}, {"category": "So", "key": "273C", "mappings": {"default": {"default": "ओपन सेंटर टियरड्रॉप-स्पोक्ड एस्टरिस्क"}}}, {"category": "So", "key": "273D", "mappings": {"default": {"default": "हेवी टियरड्रॉप-स्पोक्ड एस्टरिस्क"}}}, {"category": "So", "key": "273E", "mappings": {"default": {"default": "छह पेटेड ब्लैक एंड व्हाइट फ्लोरेट"}}}, {"category": "So", "key": "273F", "mappings": {"default": {"default": "ब्लैक फ्लोरेट"}}}, {"category": "So", "key": "2740", "mappings": {"default": {"default": "श्वेत पुष्प"}}}, {"category": "So", "key": "2741", "mappings": {"default": {"default": "आठ पेटेल्ड आउटलाइन्ड ब्लैक फ्लोरेट"}}}, {"category": "So", "key": "2742", "mappings": {"default": {"default": "सर्किल ओपन सेंटर आठ पॉइंटेड स्टार"}}}, {"category": "So", "key": "2743", "mappings": {"default": {"default": "हैवी टियरड्रॉप-स्पोकन पिनव्हील एस्टरिस्क"}}}, {"category": "So", "key": "2744", "mappings": {"default": {"default": "हिमपात का एक खंड"}}}, {"category": "So", "key": "2745", "mappings": {"default": {"default": "तंग त्रिफोलिएट स्नोफ्लेक"}}}, {"category": "So", "key": "2746", "mappings": {"default": {"default": "भारी शेवरॉन स्नोफ्लेक"}}}, {"category": "So", "key": "2747", "mappings": {"default": {"default": "चमक"}}}, {"category": "So", "key": "2748", "mappings": {"default": {"default": "भारी स्पार्कल"}}}, {"category": "So", "key": "2749", "mappings": {"default": {"default": "बैलून-स्पोक्ड एस्टरिस्क"}}}, {"category": "So", "key": "274A", "mappings": {"default": {"default": "आठ टियरड्रॉप-स्पोक प्रोपेलर एस्टरिस्क"}}}, {"category": "So", "key": "274B", "mappings": {"default": {"default": "हैवी आठ टियरड्रॉप-स्पोक्ड प्रोपेलर एस्टरिस्क"}}}, {"category": "So", "key": "274C", "mappings": {"default": {"default": "क्रॉस चिह्न"}}}, {"category": "So", "key": "274D", "mappings": {"default": {"default": "छायांकित सफेद वृत्त"}}}], "hi/units/area.min": [{"locale": "hi"}, {"locale": "hi"}, {"key": "sq", "category": "other", "names": ["sq", "sq."], "mappings": {"default": {"default": "वर्ग"}}}, {"key": "sq inch", "category": "area", "names": ["sq in", "sq. in.", "sq inch", "sq. inch"], "mappings": {"default": {"default": "वर्ग इंच"}}}, {"key": "sq rd", "category": "area", "names": ["sq rd", "sq. rd."], "mappings": {"default": {"default": "वर्ग रॉड"}}}, {"key": "sq ft", "category": "area", "names": ["sq ft", "sq. ft."], "mappings": {"default": {"default": "वर्ग फुट", "plural": "वर्ग फिट"}}}, {"key": "sq yd", "category": "area", "names": ["sq yd", "sq. yd."], "mappings": {"default": {"default": "वर्ग गज"}}}, {"key": "sq mi", "category": "area", "names": ["sq mi", "sq. mi."], "mappings": {"default": {"default": "वर्ग मील"}}}, {"key": "acr", "category": "area", "names": ["ac", "ac.", "acr", "acr."], "mappings": {"default": {"default": "एकर"}}}, {"key": "ha", "category": "area", "names": ["ha"], "mappings": {"default": {"default": "हेक्टर"}}}], "hi/units/currency.min": [{"locale": "hi"}, {"category": "currency", "key": "$", "mappings": {"default": {"default": "डॉलर"}}, "names": ["$", "💲", "＄", "﹩", "USD"]}, {"category": "currency", "key": "£", "mappings": {"default": {"default": "पाउंड"}}, "names": ["£", "￡", "GBP"]}, {"category": "currency", "key": "¥", "mappings": {"default": {"default": "येन"}}, "names": ["¥", "￥", "JPY"]}, {"category": "currency", "key": "€", "mappings": {"default": {"default": "यूरो"}}, "names": ["€", "EUR"]}, {"category": "currency", "key": "₡", "mappings": {"default": {"default": "कोलोन"}}, "names": ["₡", "CRC"]}, {"category": "currency", "key": "₢", "mappings": {"default": {"default": "क्रुज़ीरू"}}, "names": ["₢"]}, {"category": "currency", "key": "₣", "mappings": {"default": {"default": "फ्रांक"}}, "names": ["₣"]}, {"category": "currency", "key": "₤", "mappings": {"default": {"default": "लीरा"}}, "names": ["₤"]}, {"category": "currency", "key": "₥", "mappings": {"default": {"default": "मिल"}}, "names": ["₥"]}, {"category": "currency", "key": "₦", "mappings": {"default": {"default": "नाइरा"}}, "names": ["₦", "NGN"]}, {"category": "currency", "key": "₧", "mappings": {"default": {"default": "पेसेता"}}, "names": ["₧"]}, {"category": "currency", "key": "₨", "mappings": {"default": {"default": "रूपया", "plural": "रूपए"}}, "names": ["₨", "₹", "INR", "NPR", "PKR", "LKR"]}, {"category": "currency", "key": "₩", "mappings": {"default": {"default": "वॉन"}}, "names": ["₩", "￦", "KRW"]}, {"category": "currency", "key": "₪", "mappings": {"default": {"default": "शेकेल"}}, "names": ["₪"]}, {"category": "currency", "key": "₫", "mappings": {"default": {"default": "दोंग"}}, "names": ["₫"]}, {"category": "currency", "key": "₭", "mappings": {"default": {"default": "किप"}}, "names": ["₭"]}, {"category": "currency", "key": "₮", "mappings": {"default": {"default": "तुग्रिक"}}, "names": ["₮"]}, {"category": "currency", "key": "₯", "mappings": {"default": {"default": "द्राख़्मा"}}, "names": ["₯"]}, {"category": "currency", "key": "₰", "mappings": {"default": {"default": "फेनीग"}}, "names": ["₰"]}, {"category": "currency", "key": "₱", "mappings": {"default": {"default": "पेसो"}}, "names": ["₱"]}, {"category": "currency", "key": "₲", "mappings": {"default": {"default": "ग्वारानी"}}, "names": ["₲"]}, {"category": "currency", "key": "₳", "mappings": {"default": {"default": "ऑस्ट्राल"}}, "names": ["₳"]}, {"category": "currency", "key": "₴", "mappings": {"default": {"default": "ह्रीविन्या"}}, "names": ["₴", "UAH"]}, {"category": "currency", "key": "₵", "mappings": {"default": {"default": "सीडी"}}, "names": ["₵", "GHS"]}, {"category": "currency", "key": "₶", "mappings": {"default": {"default": "लिव्रे तूर्नवा"}}, "names": ["₶"]}, {"category": "currency", "key": "₷", "mappings": {"default": {"default": "स्पेसमीलो"}}, "names": ["₷"]}, {"category": "currency", "key": "₸", "mappings": {"default": {"default": "तेंगे"}}, "names": ["₸", "KZT"]}, {"category": "currency", "key": "₺", "mappings": {"default": {"default": "तुर्की लीरा"}}, "names": ["₺", "TRY"]}, {"category": "currency", "key": "元", "mappings": {"default": {"default": "युआन"}}, "names": ["元"]}, {"category": "currency", "key": "¢", "mappings": {"default": {"default": "सेंट"}}, "names": ["￠", "¢"]}], "hi/units/energy.min": [{"locale": "hi"}, {"key": "W", "category": "energy", "names": ["W", "w"], "si": true, "mappings": {"default": {"default": "वॉट"}}}, {"key": "kwh", "category": "energy", "names": ["kwh", "kWh"], "mappings": {"default": {"default": "किलोवॉट घंटा"}}}, {"key": "J", "category": "energy", "names": ["J"], "si": true, "mappings": {"default": {"default": "जूल"}}}, {"key": "N", "category": "energy", "names": ["N"], "si": true, "mappings": {"default": {"default": "न्यूटन"}}}, {"key": "A", "category": "energy", "names": ["A"], "si": true, "mappings": {"default": {"default": "एम्पियर"}}}, {"key": "V", "category": "energy", "names": ["V"], "si": true, "mappings": {"default": {"default": "वॉल्ट"}}}, {"key": "ohm", "category": "energy", "names": ["Ohm", "ohm"], "mappings": {"default": {"default": "ओह्म"}}}, {"key": "Ω", "category": "energy", "names": ["Ω"], "si": true, "mappings": {"default": {"default": "ओह्म"}}}], "hi/units/length.min": [{"locale": "hi"}, {"key": "ft", "category": "length", "names": ["ft", "ft."], "mappings": {"default": {"default": "फुट", "plural": "फिट"}}}, {"key": "in", "category": "length", "names": ["in", "in."], "mappings": {"default": {"default": "इंच"}}}, {"key": "mi", "category": "length", "names": ["mi", "mi."], "mappings": {"default": {"default": "म<PERSON><PERSON>"}}}, {"key": "yd", "category": "length", "names": ["yd", "yd."], "mappings": {"default": {"default": "गज"}}}, {"key": "link", "category": "length", "names": ["li", "li."], "mappings": {"default": {"default": "लिंक"}}}, {"key": "rod", "category": "length", "names": ["rd", "rd."], "mappings": {"default": {"default": "रॉड"}}}, {"key": "chain", "category": "length", "names": ["ch", "ch."], "mappings": {"default": {"default": "चेन"}}}, {"key": "furlong", "category": "length", "names": ["fur", "fur."], "mappings": {"default": {"default": "फ़र्लांग"}}}, {"key": "n.m.", "category": "length", "names": ["n.m."], "mappings": {"default": {"default": "समुद्री मील"}}}, {"key": "m", "category": "length", "names": ["m"], "si": true, "mappings": {"default": {"default": "मीटर"}}}], "hi/units/memory.min": [{"locale": "hi"}, {"key": "b", "category": "memory", "names": ["b"], "mappings": {"default": {"default": "बिट"}}}, {"key": "B", "category": "memory", "names": ["B"], "si": true, "mappings": {"default": {"default": "बाईट"}}}, {"key": "KB", "category": "memory", "names": ["KB"], "mappings": {"default": {"default": "किलोबाईट"}}}], "hi/units/other.min": [{"locale": "hi"}, {"key": "doz", "category": "other", "names": ["doz", "doz.", "dz", "dz."], "mappings": {"default": {"default": "डज़न"}}}], "hi/units/speed.min": [{"locale": "hi"}, {"key": "kt", "category": "speed", "names": ["kt", "kt."], "mappings": {"default": {"default": "नॉट"}}}, {"key": "mph", "category": "speed", "names": ["mph"], "mappings": {"default": {"default": "मील प्रति घंटा"}}}, {"key": "rpm", "category": "speed", "names": ["rpm"], "mappings": {"default": {"default": "परिक्रमण प्रति मिनट"}}}, {"key": "kmh", "category": "speed", "names": ["kmh"], "mappings": {"default": {"default": "किलोमीटर प्रति घंटा"}}}], "hi/units/temperature.min": [{"locale": "hi"}, {"key": "F", "category": "temperature", "names": ["F", "F.", "°F"], "mappings": {"default": {"default": "फेरनहाइट"}}}, {"key": "C", "category": "temperature", "names": ["C", "°C"], "mappings": {"default": {"default": "सेल्शियस"}}}, {"key": "K", "category": "temperature", "names": ["K", "°K"], "mappings": {"default": {"default": "केल्विन"}}}], "hi/units/time.min": [{"locale": "hi"}, {"key": "s", "category": "time", "names": ["s"], "si": true, "mappings": {"default": {"default": "सेकंड"}}}, {"key": "″", "category": "time", "names": ["″"], "mappings": {"default": {"default": "सेकंड"}}}, {"key": "min", "category": "time", "names": ["min", "′"], "mappings": {"default": {"default": "मिनट"}}}, {"key": "°", "category": "time", "names": ["°"], "mappings": {"default": {"default": "अंश"}}}, {"key": "h", "category": "time", "names": ["h", "hr"], "mappings": {"default": {"default": "घंटे", "plural": "घंटा"}}}], "hi/units/volume.min": [{"locale": "hi"}, {"key": "cu", "category": "volume", "names": ["cu", "cu."], "mappings": {"default": {"default": "घन"}}}, {"key": "cu inch", "category": "volume", "names": ["cu in", "cu. in."], "mappings": {"default": {"default": "घन इंच"}}}, {"key": "cu ft", "category": "volume", "names": ["cu ft", "cu. ft."], "mappings": {"default": {"default": "घन फुट", "plural": "घन फीट"}}}, {"key": "cu yd", "category": "volume", "names": ["cu yd", "cu. yd."], "mappings": {"default": {"default": "घन गज"}}}, {"key": "bbl", "category": "volume", "names": ["bbl.", "bbl"], "mappings": {"default": {"default": "बैरल"}}}, {"key": "fl. oz.", "category": "volume", "names": ["fl. oz.", "fl oz"], "mappings": {"default": {"default": "तरल औंस"}}}, {"key": "gal", "category": "volume", "names": ["gal", "gal."], "mappings": {"default": {"default": "गेलन"}}}, {"key": "pt", "category": "volume", "names": ["pt", "pt."], "mappings": {"default": {"default": "पाइंट"}}}, {"key": "qt", "category": "volume", "names": ["qt", "qt."], "mappings": {"default": {"default": "क़्वार्ट"}}}, {"key": "fluid dram", "category": "volume", "names": ["fl dr", "fl. dr."], "mappings": {"default": {"default": "तरल ड्राम"}}}, {"key": "tbsp", "category": "volume", "names": ["tbsp", "tbsp.", "Tbsp", "Tbsp."], "mappings": {"default": {"default": "बड़ी चम्मच"}}}, {"key": "tsp", "category": "volume", "names": ["tsp", "tsp."], "mappings": {"default": {"default": "छोटी चम्मच"}}}, {"key": "cup", "category": "volume", "names": ["cp", "cp."], "mappings": {"default": {"default": "कप"}}}, {"key": "cc", "category": "volume", "names": ["cc", "ccm"], "mappings": {"default": {"default": "घन सेंटीमीटर"}}}, {"key": "l", "category": "volume", "names": ["l"], "si": true, "mappings": {"default": {"default": "लीटर"}}}], "hi/units/weight.min": [{"locale": "hi"}, {"key": "dram", "category": "weight", "names": ["dr", "dr."], "mappings": {"default": {"default": "ड्राम"}}}, {"key": "oz", "category": "weight", "names": ["oz", "oz."], "mappings": {"default": {"default": "औंस"}}}, {"key": "lb", "category": "weight", "names": ["lb", "lb."], "mappings": {"default": {"default": "रतल"}}}, {"key": "st", "category": "weight", "names": ["st", "st."], "mappings": {"default": {"default": "स्टोन"}}}, {"key": "qtr", "category": "weight", "names": ["qtr", "qtr."], "mappings": {"default": {"default": "क़्वार्टर"}}}, {"key": "cwt", "category": "weight", "names": ["cwt", "cwt."], "mappings": {"default": {"default": "हन्ड्रेड्वेट"}}}, {"key": "LT", "category": "weight", "names": ["LT", "L.T."], "mappings": {"default": {"default": "इम्पीरियल टन"}}}, {"key": "gr", "category": "weight", "names": ["gr"], "mappings": {"default": {"default": "ग्राम"}}}, {"key": "g", "category": "weight", "names": ["g"], "si": true, "mappings": {"default": {"default": "ग्राम"}}}, {"key": "mcg", "category": "weight", "names": ["mcg"], "mappings": {"default": {"default": "माइक्रोग्राम"}}}, {"key": "t", "category": "weight", "names": ["t", "T"], "mappings": {"default": {"default": "टन"}}}], "hi/rules/clearspeak_hindi.min": {"domain": "clearspeak", "locale": "hi", "modality": "speech", "inherits": "base", "rules": [["Rule", "protected-number", "default", "[t] text()", "self::number[contains(@grammar, \"protected\")]"], ["Precondition", "appl-simple-inverse", "default", "self::appl", "@role=\"simple function\"", "name(children/*[1])=\"superscript\"", "children/*[1][@role=\"simple function\"]", "name(children/*[1]/children/*[2])=\"prefixop\"", "children/*[1]/children/*[2][@role=\"negative\"]", "children/*[1]/children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "vbar-args-such-that", "VerticalLine_SuchThat", "self::punctuated", "@role=\"sequence\"", "content/*[1][@role=\"vbar\"]", "not(parent::*[@embellished=\"punctuation\"])"], ["Precondition", "vbar-args-divides", "default", "self::punctuated", "@role=\"sequence\"", "content/*[1][@role=\"vbar\"]", "not(parent::*[@embellished=\"punctuation\"])"], ["Precondition", "vbar-args-always-divides", "VerticalLine_Divides", "self::punctuated", "@role=\"sequence\"", "content/*[1][@role=\"vbar\"]", "not(parent::*[@embellished=\"punctuation\"])"], ["Precondition", "vbar-args-given", "VerticalLine_Given", "self::punctuated", "@role=\"sequence\"", "content/*[1][@role=\"vbar\"]", "not(parent::*[@embellished=\"punctuation\"])"], ["Precondition", "element", "default", "self::infixop[contains(@role, \"element\")]"], ["Precondition", "element-in-set", "default", "self::infixop[contains(@role, \"element\")]", "contains(@annotation, \"set:intensional\")"], ["Precondition", "element-in-set-woall", "Sets_woAll", "self::infixop[contains(@role, \"element\")]", "contains(@annotation, \"set:intensional\")"], ["Precondition", "unit-square", "default", "self::superscript[@role=\"unit\"]", "children/*[2][text()=2]", "name(children/*[1])=\"identifier\"", "children/*[1][@category=\"unit:length\"]"], ["Precondition", "unit-cubic", "default", "self::superscript[@role=\"unit\"]", "children/*[2][text()=3]", "name(children/*[1])=\"identifier\"", "children/*[1][@category=\"unit:length\"]"]]}, "hi/rules/clearspeak_hindi_actions.min": {"domain": "clearspeak", "locale": "hi", "modality": "speech", "kind": "actions", "rules": [["Action", "collapsed", "[t] \"संकुचित\"; [n] . (engine:modality=summary, grammar:collapsed)"], ["Action", "font", "[t] @font (grammar:localFont); [n] . (pause:short, grammar:ignoreFont=@font)"], ["Action", "ellipsis", "[t] \"आदि, आदि\""], ["Action", "ellipsis-andsoon", "[t] \"आदि आदि आदि\""], ["Action", "vbar-evaluated", "[n] children/*[1] (pause:short); [n] content/*[1]/children/*[2]; [t] \"पर मूल्यांकित\" (pause:short)"], ["Action", "vbar-evaluated-both", "[n] children/*[1] (pause:short); [n] content/*[1]/children/*[2]; [t] \"पर मूल्यांकित\" (pause:short); [t] \"घटाव का चिह्न उसी व्यंजक जो\"; [n] content/*[1]/children/*[1]/children/*[2]; [t] \"पर मूल्यांकित\" (pause:short)"], ["Action", "vbar-args-such-that", "[n] children/*[1]; [t] \"जैसा की\"; [n] children/*[3]"], ["Action", "vbar-args-divides", "[n] children/*[3] (pause:short); [n] children/*[1]; [t] \"से विभाज्य है\""], ["Action", "vbar-args-always-divides", "[n] children/*[3] (pause:short); [n] children/*[1]; [t] \"से विभाज्य है\""], ["Action", "vbar-args-given", "[n] children/*[1]; [t] \"यदि निश्चित है\"; [n] children/*[3]"], ["Action", "element", "[n] children/*[1]; [n] children/*[2]; [n] content/*[1] (pause:short)"], ["Action", "element-in-set", "[n] children/*[2]; [n] content/*[1]; [t] \"सभीं\"; [n] children/*[1]"], ["Action", "element-in-set-woall", "[n] children/*[2]; [n] content/*[1]; [n] children/*[1]"], ["Action", "set-member", "[t] \"में\""], ["Action", "set-member-member", "[t] \"में\""], ["Action", "set-member-element", "[t] \"में\""], ["Action", "set-member-in", "[t] \"में\""], ["Action", "set-member-belongs", "[t] \"में\""], ["Action", "set-not-member", "[t] \"में नही\""], ["Action", "set-not-member-member", "[t] \"में नही\""], ["Action", "set-not-member-element", "[t] \"में नही\""], ["Action", "set-not-member-in", "[t] \"में नही\""], ["Action", "set-not-member-belongs", "[t] \"में नही\""], ["Action", "appl", "[p] (pause:short); [n] children/*[2]; [t] \"का फलन\"; [n] children/*[1] (pause:short)"], ["Action", "appl-simple", "[p] (pause:short); [n] children/*[2]; [t] \"का फलन\" (pause:short); [n] children/*[1] (pause:short)"], ["Action", "appl-simple-fenced", "[p] (pause:short); [n] children/*[2]; [t] \"का फलन\" (pause:short); [n] children/*[1] (pause:short)"], ["Action", "appl-simple-inverse", "[n] children/*[2]; [t] \"का प्रतिफलन\"; [n] children/*[1]/children/*[1] (pause:short)"], ["Action", "appl-times", "[p] (pause:short); [n] children/*[1]; [t] \"गुना\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-prefix-simple-arg", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-prefix-embell", "[p] (pause:short); [n] children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-fenced-or-frac-arg", "[p] (pause:short); [n] children/*[1]; [t] \"का फलन\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-subscript", "[p] (pause:short); [n] children/*[1]; [t] \"का फलन\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-ln", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-ln-pause", "[n] children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "function-ln-of", "[n] children/*[1]; [t] \"का फलन\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-as-exp", "[n] children/*[1]; [t] \"का फलन\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-subscript-as-exp", "[n] children/*[1]; [t] \"का फलन\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-hyper", "[p] (pause:short); [n] children/*[1]; [t] \"का फलन\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-inverse", "[p] (pause:short); [t] \"व्युत्क्रम\"; [n] children/*[1]/children/*[1]; [t] \"का फलन\"; [n] children/*[2] (pause:short)"], ["Action", "appl-triginverse", "[p] (pause:short); [n] children/*[1]; [t] \"का फलन\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc-simple", "[p] (pause:short); [t] \"चाप\"; [n] children/*[1]/children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc-simple-fenced", "[p] (pause:short); [t] \"चाप\"; [n] children/*[1]/children/*[1] (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc", "[p] (pause:short); [t] \"चाप\"; [n] children/*[1]/children/*[1]; [t] \"का फलन\"; [n] children/*[2] (pause:short)"], ["Action", "function-inverse", "[n] children/*[1]; [t] \"प्रतिलोम\""], ["Action", "superscript-prefix-function", "[n] children/*[1]; [n] children/*[2] (grammar:ordinal); [t] \"घातांक\""], ["Action", "superscript", "[n] children/*[1]; [t] \"का घात\" (pause:short); [n] children/*[2] (pause:short); [t] \"घातांक समाप्त\" (pause:short)"], ["Action", "superscript-simple-exponent", "[n] children/*[1]; [t] \"का घात\"; [n] children/*[2]; [t] \"घातांक\" (pause:short)"], ["Action", "superscript-simple-exponent-end", "[n] children/*[1]; [t] \"का घात\"; [n] children/*[2]; [t] \"घातांक\""], ["Action", "superscript-ordinal", "[n] children/*[1]; [t] \"का घात\"; [n] children/*[2] (grammar:ordinal); [t] \"घातांक\" (pause:short)"], ["Action", "superscript-non-ordinal", "[n] children/*[1]; [t] \"का घात\"; [n] children/*[2]; [t] \"घातांक\" (pause:short)"], ["Action", "superscript-simple-function", "[n] children/*[1]; [n] children/*[2] (grammar:ordinal); [t] \"घातांक\" (pause:short)"], ["Action", "superscript-simple-function-none", "[n] . (grammar:functions_none)"], ["Action", "superscript-ordinal-number", "[n] children/*[1]; [t] \"का घात\"; [n] children/*[2] (pause:short, grammar:ordinal)"], ["Action", "superscript-ordinal-negative", "[n] children/*[1]; [t] \"का घात\"; [n] children/*[2] (pause:short)"], ["Action", "superscript-ordinal-default", "[n] children/*[1]; [t] \"का घात\" (pause:short); [n] children/*[2] (pause:short); [t] \"घातांक समाप्त\" (pause:short)"], ["Action", "superscript-ordinal-power-number", "[n] children/*[1]; [t] \"का घात\"; [n] children/*[2] (grammar:ordinal); [t] \"घातांक\" (pause:short)"], ["Action", "superscript-ordinal-power-negative", "[n] children/*[1]; [t] \"का घात\"; [n] children/*[2]; [t] \"घातांक\" (pause:short)"], ["Action", "superscript-ordinal-power-identifier", "[n] children/*[1]; [t] \"का घात\"; [n] children/*[2] (grammar:ordinal); [t] \"घातांक\" (pause:short)"], ["Action", "superscript-ordinal-power-default", "[n] children/*[1]; [t] \"का घात\" (pause:short); [n] children/*[2] (pause:short); [t] \"घातांक समाप्त\" (pause:short)"], ["Action", "superscript-power", "[n] children/*[1]; [t] \"का घात\"; [n] children/*[2] (pause:short, grammar:afterPower)"], ["Action", "superscript-power-default", "[n] children/*[1]; [t] \"का घात\" (pause:short); [n] children/*[2] (pause:short); [t] \"घातांक समाप्त\" (pause:short)"], ["Action", "exponent", "[n] text() (join:\"-\"); [t] \"वां\""], ["Action", "exponent-number", "[t] CSFordinalExponent"], ["Action", "exponent-ordinal", "[t] CSFwordOrdinal"], ["Action", "exponent-ordinal-zero", "[t] \"शून्य\""], ["Action", "exponent-ordinalpower", "[t] CSFwordOrdinal"], ["Action", "exponent-ordinalpower-zero", "[t] \"शून्य\""], ["Action", "square", "[n] children/*[1]; [t] \"वर्गित\""], ["Action", "cube", "[n] children/*[1]; [t] \"घनित\""], ["Action", "fences-points", "[t] \"निर्देशांक वाला बिंदु\"; [n] children/*[1]"], ["Action", "fences-interval", "[n] children/*[1]/children/*[1]; [t] \"से\"; [n] children/*[1]/children/*[3]; [t] \"तक का अन्तराल\" (pause:short); [n] . (grammar:interval)"], ["Action", "interval-open", "[n] children/*[1]/children/*[1]; [t] \"अथवा\"; [n] children/*[1]/children/*[3]; [t] \"असम्मिलित\""], ["Action", "interval-closed-open", "[n] children/*[1]/children/*[1]; [t] \"सम्मिलित\" (pause:short); [t] \"परन्तु\"; [n] children/*[1]/children/*[3]; [t] \"असम्मिलित\""], ["Action", "interval-open-closed", "[n] children/*[1]/children/*[1]; [t] \"असम्मिलित\" (pause:short); [t] \"परन्तु\"; [n] children/*[1]/children/*[3]; [t] \"सम्मिलित\""], ["Action", "interval-closed", "[n] children/*[1]/children/*[1]; [t] \"और\"; [n] children/*[1]/children/*[3]; [t] \"सम्मिलित\""], ["Action", "interval-open-inf-r", "[n] children/*[1]/children/*[1]; [t] \"असम्मिलित\""], ["Action", "interval-open-inf-l", "[n] children/*[1]/children/*[3]; [t] \"असम्मिलित\""], ["Action", "interval-closed-open-inf", "[n] children/*[1]/children/*[1]; [t] \"सम्मिलित\""], ["Action", "interval-open-closed-inf", "[n] children/*[1]/children/*[3]; [t] \"सम्मिलित\""], ["Action", "set-empty", "[t] \"रिक्त समुच्चय\""], ["Action", "set-extended", "[n] children/*[1]/children/*[1]; [t] \"का समुच्चय\"; [t] \"जिस में\"; [n] children/*[1]/children/*[3]"], ["Action", "set-collection", "[t] \"समुच्चय\"; [n] children/*[1]"], ["Action", "subscript", "[p] (pause:short); [n] children/*[1]; [t] \"पाद\"; [n] children/*[2] (pause:short)"], ["Action", "logarithm-base", "[n] children/*[1]; [t] \"आधार\"; [n] children/*[2]"], ["Action", "subscript-index", "[n] children/*[1]; [t] \"पाद\"; [n] children/*[2]"], ["Action", "fraction", "[p] (pause:short); [t] \"भिन्न जिसका अंश\"; [n] children/*[1] (pause:short); [t] \"और हर\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-none", "[p] (pause:short); [t] \"भिन्न जिसका अंश\"; [n] children/*[1] (pause:short); [t] \"और हर\"; [n] children/*[2] (pause:short)"], ["Action", "simple-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"के हर में\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"के हर में\"; [n] children/*[2] (pause:short)"], ["Action", "simple-text-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"के हर में\"; [n] children/*[2] (pause:short)"], ["Action", "vulgar-fraction", "[t] CSFvulgarFraction"], ["Action", "fraction-over", "[p] (pause:short); [n] children/*[1]; [t] \"के हर में\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-overendfrac", "[p] (pause:short); [n] children/*[1]; [t] \"के हर में\"; [n] children/*[2] (pause:short); [t] \"भिन्न समाप्त\" (pause:short)"], ["Action", "fraction-fracover", "[p] (pause:short); [t] \"भिन्न\"; [n] children/*[1]; [t] \"के हर में\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-per", "[p] (pause:short); [n] children/*[1]; [t] \"प्रति\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-general<PERSON><PERSON><PERSON>", "[p] (pause:short); [t] \"भिन्न जिसका अंश\"; [n] children/*[1] (pause:short); [t] \"और हर\"; [n] children/*[2] (pause:short); [t] \"भिन्न समाप्त\" (pause:short)"], ["Action", "fraction-general", "[p] (pause:short); [t] \"भिन्न जिसका अंश\"; [n] children/*[1] (pause:short); [t] \"और हर\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction-ordinal", "[t] CSFvulgarFraction"], ["Action", "fraction-endfrac", "[p] (pause:short); [n] . (grammar:endfrac); [t] \"भिन्न समाप्त\" (pause:short)"], ["Action", "vulgar-fraction-endfrac", "[p] (pause:short); [n] children/*[1]; [t] \"के हर में\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction-endfrac", "[t] CSFvulgarFraction"], ["Action", "sqrt", "[n] children/*[1] (grammar:!EndRoot); [t] \"का वर्गमूल\" (pause:short)"], ["Action", "sqrt-nested", "[p] (pause:\"short\"); [n] children/*[1] (grammar:!EndRoot); [t] \"का वर्गमूल\" (pause:short)"], ["Action", "negative-sqrt", "[n] children/*[1]/children/*[1] (grammar:!EndRoot); [t] \"का ऋणात्मक वर्गमूल\" (pause:short)"], ["Action", "negative-sqrt-default", "[p] (pause:\"short\"); [n] children/*[1]/children/*[1] (grammar:!EndRoot); [t] \"का ऋणात्मक वर्गमूल\" (pause:short)"], ["Action", "sqrt-plus-minus", "[n] children/*[1] (grammar:!EndRoot); [t] \"का धनात्मक वर्गमूल\" (pause:short)"], ["Action", "sqrt-nested-plus-minus", "[n] children/*[1] (grammar:!EndRoot); [t] \"का धनात्मक वर्गमूल\" (pause:short)"], ["Action", "sqrt-plus-minus-posnegsqrootend", "[n] children/*[1] (grammar:!EndRoot); [t] \"का धनात्मक वर्गमूल\" (pause:short)"], ["Action", "sqrt-nested-plus-minus-posnegsqrootend", "[n] children/*[1] (grammar:!EndRoot); [t] \"का धनात्मक वर्गमूल\" (pause:short)"], ["Action", "sqrt-endroot", "[n] . (grammar:EndRoot); [t] \"मूल समाप्त\" (pause:short)"], ["Action", "negative-sqrt-endroot", "[n] . (grammar:EndRoot); [t] \"मूल समाप्त\" (pause:short)"], ["Action", "sqrt-posnegsqrootend", "[n] . (grammar:EndRoot); [t] \"मूल समाप्त\" (pause:short)"], ["Action", "negative-sqrt-posnegsqrootend", "[n] . (grammar:EndRoot); [t] \"मूल समाप्त\" (pause:short)"], ["Action", "cubic", "[n] children/*[2] (grammar:!EndRoot); [t] \"का घनमूल\" (pause:short)"], ["Action", "cubic-nested", "[n] children/*[2] (grammar:!EndRoot); [t] \"का घनमूल\" (pause:short)"], ["Action", "root", "[n] children/*[2] (grammar:!EndRoot); [n] children/*[1] (grammar:ordinal); [t] \"का मूल\" (pause:short)"], ["Action", "root-nested", "[n] children/*[2] (grammar:!EndRoot); [n] children/*[1] (grammar:ordinal); [t] \"का मूल\" (pause:short)"], ["Action", "root-endroot", "[n] . (grammar:EndRoot); [t] \"मूल समाप्त\" (pause:short)"], ["Action", "root-posnegsqrootend", "[n] . (grammar:EndRoot); [t] \"मूल समाप्त\" (pause:short)"], ["Action", "negative", "[t] \"ऋण\"; [n] children/*[1]"], ["Action", "positive", "[t] \"धनात्मक\"; [n] children/*[1]"], ["Action", "angle-measure", "[n] content/*[1]; [n] children/*[2] (grammar:angle); [t] \"का मापन\""], ["Action", "set-prefix-operators", "[n] . (grammar:!prefix); [t] \"का फलन\""], ["Action", "division", "[n] children/*[1]; [t] \"द्वारा विभाजित\"; [n] children/*[2]"], ["Action", "operators-after-power", "[m] children/* (rate:\"0.5\")"], ["Action", "natural-numbers", "[t] \"प्राकृत संख्याएं\""], ["Action", "integers", "[t] \"पूर्णांक\""], ["Action", "rational-numbers", "[t] \"परिमेय संख्याएं\""], ["Action", "real-numbers", "[t] \"वास्तविक संख्याएं\""], ["Action", "complex-numbers", "[t] \"सम्मिश्र संख्याएं\""], ["Action", "natural-numbers-with-zero", "[t] \"शून्य सहित प्राकृत संख्याएं\""], ["Action", "positive-integers", "[t] \"धनात्मक पूर्णांक\""], ["Action", "negative-integers", "[t] \"ऋणात्मक पूर्णांक\""], ["Action", "positive-rational-numbers", "[t] \"धनात्मक परिमेय संख्याएं\""], ["Action", "negative-rational-numbers", "[t] \"ऋणात्मक परिमेय संख्याएं\""], ["Action", "fences-neutral", "[p] (pause:short); [n] children/*[1]; [t] \"का निरपेक्ष मान\" (pause:short)"], ["Action", "fences-neutral-absend", "[p] (pause:short); [t] \"निरपेक्ष मान प्रारम्भ\"; [n] children/*[1] (pause:short); [t] \"निरपेक्ष मान समाप्त\" (pause:short)"], ["Action", "fences-neutral-cardinality", "[p] (pause:short); [n] children/*[1]; [t] \"की गणनीयता\" (pause:short)"], ["Action", "fences-neutral-determinant", "[p] (pause:short); [n] children/*[1]; [t] \"का सारणिक\" (pause:short)"], ["Action", "fences-metric", "[p] (pause:short); [t] \"की दूरी फलन\" (span:.); [n] children/*[1] (pause:short)"], ["Action", "fences-metric-absend", "[p] (pause:short); [t] \"की दूरी फलन\" (span:content/*[1]); [n] children/*[1] (pause:short); [t] \"दूरी फलन समाप्त\" (span:content/*[1], pause:short)"], ["Action", "matrix", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"आव्यूह\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"पंक्ति-:\", pause:long)"], ["Action", "matrix-simple", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"आव्यूह\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"पंक्ति-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-trivial", "[t] \"१ बाय १ आव्यूह में भर्ती\"; [n] children/*[1] (pause:long)"], ["Action", "determinant", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"आव्यूह\"; [t] \"का सारणिक\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"पंक्ति-:\", pause:long, grammar:simpleDet)"], ["Action", "determinant-simple", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"आव्यूह\"; [t] \"का सारणिक\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"पंक्ति-:\", pause:long)"], ["Action", "matrix-vector", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"स्तंभ आव्यूह\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"पंक्ति-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-vector-simple", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"स्तंभ आव्यूह\" (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-vector-simple-silentcolnum", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"स्तंभ आव्यूह\" (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"पंक्ति आव्यूह\" (pause:long); [m] children/*[1]/children/* (ctxtFunc:CTFnodeCounter, context:\"स्तंभ-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector-simple", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"पंक्ति आव्यूह\" (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector-simple-silentcolnum", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"पंक्ति आव्यूह\" (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFnodeCounter, context:\"स्तंभ-,- \", sepFunc:CTFpauseSeparator, separator:\"medium\", pause:long)"], ["Action", "matrix-end-matrix", "[n] . (grammar:EndMatrix); [t] \"आव्यूह समाप्त\""], ["Action", "matrix-end-vector", "[n] . (grammar:EndMatrix); [t] \"आव्यूह समाप्त\""], ["Action", "matrix-end-determinant", "[n] . (grammar:EndMatrix); [t] \"सारणिक समाप्त\""], ["Action", "vector", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"स्तंभ सदिश\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"पंक्ति-:\", pause:long, grammar:simpleDet)"], ["Action", "vector-simple", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"स्तंभ सदिश\" (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "row-vector", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"पंक्ति सदिश\" (pause:long); [m] children/*[1]/children/* (ctxtFunc:CTFnodeCounter, context:\"स्तंभ-:\", pause:long, grammar:simpleDet)"], ["Action", "row-vector-simple", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"पंक्ति सदिश\" (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "vector-end-matrix", "[n] . (grammar:EndMatrix); [t] \"आव्यूह समाप्त\""], ["Action", "vector-end-vector", "[n] . (grammar:EndMatrix); [t] \"सदिश समाप्त\""], ["Action", "vector-end-vector-endvector", "[n] . (grammar:EndMatrix); [t] \"सदिश समाप्त\""], ["Action", "vector-end-determinant", "[n] . (grammar:EndMatrix); [t] \"सारणिक समाप्त\""], ["Action", "binomial", "[n] children/*[1]/children/*[1]; [t] \"चयन करें\"; [n] children/*[2]/children/*[1]"], ["Action", "lines-summary", "[p] (pause:short); [t] count(children/*); [t] \"रेखाएं\"; [n] . (grammar:layoutSummary)"], ["Action", "cases-summary", "[p] (pause:short); [t] count(children/*); [t] \"फलन उपशर्त\"; [n] . (grammar:layoutSummary)"], ["Action", "lines", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"रेखा-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "blank-cell", "[t] \"रिक्त\""], ["Action", "blank-line", "[t] \"रिक्त\""], ["Action", "blank-cell-empty", "[t] \"रिक्त\""], ["Action", "blank-line-empty", "[t] \"रिक्त\""], ["Action", "cases", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"फलन उपशर्त-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-cases-summary", "[p] (pause:short); [t] count(children/*); [t] \"फलन उपशर्त\"; [n] . (grammar:layoutSummary)"], ["Action", "lines-cases", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"फलन उपशर्त-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-equations-summary", "[p] (pause:short); [t] count(children/*); [t] \"समीकरण\"; [n] . (grammar:layoutSummary)"], ["Action", "lines-equations", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"समीकरण-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-steps-summary", "[p] (pause:short); [t] count(children/*); [t] \"चरण\"; [n] . (grammar:layoutSummary)"], ["Action", "lines-steps", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"चरण-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-rows-summary", "[p] (pause:short); [t] count(children/*); [t] \"पंक्तियाँ\"; [n] . (grammar:layoutSummary)"], ["Action", "lines-rows", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"पंक्ति-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-constraints-summary", "[p] (pause:short); [t] count(children/*); [t] \"व्यवरोध\"; [n] . (grammar:layoutSummary)"], ["Action", "lines-constraints", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"व्यवरोध-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "bigop", "[n] children/*[1]; [t] \"का फलन\"; [n] children/*[2] (pause:short)"], ["Action", "limboth", "[n] children/*[1]; [t] \"से\"; [n] children/*[2]; [t] \"से\"; [n] children/*[3]"], ["Action", "limlower", "[n] children/*[1]; [t] \"के हर में\"; [n] children/*[2] (pause:short)"], ["Action", "limupper", "[n] children/*[1]; [t] \"के निचे\"; [n] children/*[2] (pause:short)"], ["Action", "integral", "[n] children/*[1]; [t] \"का फलन\"; [n] children/*[2] (pause:short); [n] children/*[3] (pause:short)"], ["Action", "integral-novar", "[n] children/*[1]; [t] \"का फलन\"; [n] children/*[2] (pause:short)"], ["Action", "overscript", "[n] children/*[1]; [t] \"के निचे\"; [n] children/*[2] (pause:short)"], ["Action", "overscript-limits", "[n] children/*[1]; [t] \"से\"; [n] children/*[2]"], ["Action", "underscript", "[n] children/*[1]; [t] \"के हर में\"; [n] children/*[2] (pause:short)"], ["Action", "underscript-limits", "[n] children/*[1]; [t] \"से\"; [n] children/*[2]"], ["Action", "mixed-number", "[n] children/*[1]; [t] \"पूर्णांक\"; [n] children/*[2]"], ["Action", "number-with-chars", "[t] \"संख्या\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "decimal-period", "[t] \"पुनरावर्तित दशमलव\"; [n] children/*[1] (grammar:spaceout); [t] \"दशमलव के बाद पुनरावर्तित अंक\"; [n] children/*[3]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-period-float", "[t] \"पुनरावर्तित दशमलव\"; [n] children/*[1] (grammar:spaceout); [t] \"के बाद पुनरावर्तित अंक\"; [n] children/*[2]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-period-singular", "[t] \"पुनरावर्तित दशमलव\"; [n] children/*[1] (grammar:spaceout); [t] \"दशमलव के बाद पुनरावर्तित अंक\"; [n] children/*[3]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-period-singular-float", "[t] \"पुनरावर्तित दशमलव\"; [n] children/*[1] (grammar:spaceout); [t] \"के बाद पुनरावर्तित अंक\"; [n] children/*[2]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-point", "[t] \"दशमलव\""], ["Action", "line-segment", "[t] \"रेखाखंड\"; [n] children/*[1]/children/*[1]; [n] children/*[1]/children/*[2] (pause:short)"], ["Action", "conjugate", "[n] children/*[1]; [t] \"का सम्मिश्र संयुग्मी\""], ["Action", "defined-by", "[t] \"की व्याख्या की जाती है\" (pause:short)"], ["Action", "adorned-sign", "[n] children/*[1]; [t] \"चिन्ह और\"; [n] children/*[2]; [t] \"इस पर\""], ["Action", "factorial", "[t] \"क्रमगुणित\""], ["Action", "left-super", "[t] \"वाम उर्ध्व\"; [n] text()"], ["Action", "left-super-list", "[t] \"वाम उर्ध्व\"; [m] children/*"], ["Action", "left-sub", "[t] \"वाम पाद\"; [n] text()"], ["Action", "left-sub-list", "[t] \"वाम पाद\"; [m] children/*"], ["Action", "right-super", "[t] \"दक्षिण उर्ध्व\"; [n] text()"], ["Action", "right-super-list", "[t] \"दक्षिण उर्ध्व\"; [m] children/*"], ["Action", "right-sub", "[t] \"दक्षिण पाद\"; [n] text()"], ["Action", "right-sub-list", "[t] \"दक्षिण पाद\"; [m] children/*"], ["Action", "choose", "[n] children/*[2] (grammar:combinatorics); [t] \"चयन करें\"; [n] children/*[4] (grammar:combinatorics)"], ["Action", "permute", "[n] children/*[2] (grammar:combinatorics); [t] \"क्रमचय\"; [n] children/*[4] (grammar:combinatorics)"], ["Action", "unit-square", "[t] \"वर्ग\"; [n] children/*[1]"], ["Action", "unit-cubic", "[t] \"घन\"; [n] children/*[1]"], ["Action", "unit-reciprocal", "[t] \"व्युत्क्रम\"; [n] children/*[1]"], ["Action", "unit-reciprocal-singular", "[t] \"प्रति\"; [n] children/*[1] (grammar:singular)"], ["Action", "unit-divide", "[n] children/*[1]; [t] \"प्रति\"; [n] children/*[2] (grammar:singular)"]]}, "hi/rules/mathspeak_hindi.min": {"domain": "mathspeak", "locale": "hi", "modality": "speech", "inherits": "base", "rules": [["Rule", "protected", "default", "[t] text()", "self::number[contains(@grammar, \"protected\")]"]]}, "hi/rules/mathspeak_hindi_actions.min": {"domain": "mathspeak", "locale": "hi", "modality": "speech", "kind": "actions", "rules": [["Action", "collapsed", "[t] \"निपातित\"; [n] . (engine:modality=summary, grammar:collapsed)"], ["Action", "blank-cell-empty", "[t] \"रिक्त\""], ["Action", "blank-line-empty", "[t] \"रिक्त\""], ["Action", "font", "[t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "mixed-number", "[n] children/*[1]; [t] \"पूर्णांक\"; [n] children/*[2]"], ["Action", "number-with-chars", "[t] \"संख्या\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "number-with-chars-brief", "[t] \"संख्या\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "number-as-upper-word", "[t] \"ऊपर का शब्द\"; [m] CQFspaceoutNumber"], ["Action", "number-baseline", "[t] \"आधार रेखा\"; [n] . (grammar:baseline)"], ["Action", "number-baseline-brief", "[t] \"आधार\"; [n] . (grammar:baseline)"], ["Action", "number-baseline-font", "[t] \"आधार रेखा\"; [t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "number-baseline-font-brief", "[t] \"आधार\"; [t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "negative-number", "[t] \"ऋण\"; [n] children/*[1]"], ["Action", "negative", "[t] \"बाकी\"; [n] children/*[1]"], ["Action", "division", "[n] children/*[1]; [t] \"divided by\"; [n] children/*[2]"], ["Action", "subtraction", "[m] children/* (separator:\"बाकी\")"], ["Action", "fences-neutral", "[t] \"निरपेक्ष मान आरंभ\"; [n] children/*[1]; [t] \"निरपेक्ष मान समाप्त\""], ["Action", "fences-neutral-sbrief", "[t] \"निरपेक्ष मान\"; [n] children/*[1]; [t] \"निरपेक्ष मान समाप्त\""], ["Action", "fences-metric", "[t] \"दूरी फलन आरंभ\"; [n] children/*[1]; [t] \"दूरी फलन समाप्त\""], ["Action", "fences-metric-sbrief", "[t] \"दूरी फलन\"; [n] children/*[1]; [t] \"दूरी फलन समाप्त\""], ["Action", "empty-set", "[t] \"रिक्त समुच्चय\""], ["Action", "fences-set", "[t] \"समुच्चय आरंभ\"; [n] children/*[1]; [t] \"समुच्चय समाप्त\""], ["Action", "fences-set-sbrief", "[t] \"समुच्चय\"; [n] children/*[1]; [t] \"समुच्चय समाप्त\""], ["Action", "factorial", "[t] \"क्रमगुणित\""], ["Action", "minus", "[t] \"बाकी\""], ["Action", "continued-fraction-outer", "[t] \"भिन्न चालू\"; [n] children/*[1]; [t] \"ऊपर\"; [n] children/*[2]"], ["Action", "continued-fraction-outer-brief", "[t] \"भिन्न चालू\"; [n] children/*[1]; [t] \"ऊपर\"; [n] children/*[2]"], ["Action", "continued-fraction-inner", "[t] \"आरंभ भिन्न\"; [n] children/*[1]; [t] \"ऊपर\"; [n] children/*[2]"], ["Action", "continued-fraction-inner-brief", "[t] \"आरंभ भिन्न\"; [n] children/*[1]; [t] \"ऊपर\"; [n] children/*[2]"], ["Action", "continued-fraction-inner-sbrief", "[t] \"भिन्न\"; [n] children/*[1]; [t] \"ऊपर\"; [n] children/*[2]"], ["Action", "integral", "[n] children/*[1]; [t] \"पादांक\"; [n] children/*[2]; [t] \"उर्ध्वान्क\"; [n] children/*[3]; [t] \"आधार रेखा\""], ["Action", "integral-brief", "[n] children/*[1]; [t] \"अधो\"; [n] children/*[2]; [t] \"उर्ध्व\"; [n] children/*[3]; [t] \"आधार\""], ["Action", "square", "[n] children/*[1]; [t] \"वर्ग\""], ["Action", "cube", "[n] children/*[1]; [t] \"घन\""], ["Action", "prime", "[n] children/*[1]; [n] children/*[2]"], ["Action", "counted-prime", "[t] count(children/*) (grammar:numbers2alpha); [t] \"प्राइम\""], ["Action", "counted-prime-multichar", "[t] string-length(text()) (grammar:numbers2alpha); [t] \"प्राइम\""], ["Action", "overscore", "[t] \"ऊपर संशोधित\"; [n] children/*[1]; [t] \"के साथ\"; [n] children/*[2]"], ["Action", "overscore-brief", "[t] \"ऊपर संशोधित\"; [n] children/*[1]; [t] \"के साथ\"; [n] children/*[2]"], ["Action", "double-overscore", "[t] \"ऊपर के ऊपर संशोधित\"; [n] children/*[1]; [t] \"के साथ\"; [n] children/*[2]"], ["Action", "double-overscore-brief", "[t] \"ऊपर के ऊपर संशोधित\"; [n] children/*[1]; [t] \"के साथ\"; [n] children/*[2]"], ["Action", "underscore", "[t] \"निचे संशोधित\"; [n] children/*[1]; [t] \"के साथ\"; [n] children/*[2]"], ["Action", "underscore-brief", "[t] \"निचे संशोधित\"; [n] children/*[1]; [t] \"के साथ\"; [n] children/*[2]"], ["Action", "double-underscore", "[t] \"निचे के निचे संशोधित\"; [n] children/*[1]; [t] \"के साथ\"; [n] children/*[2]"], ["Action", "double-underscore-brief", "[t] \"निचे के निचे संशोधित\"; [n] children/*[1]; [t] \"के साथ\"; [n] children/*[2]"], ["Action", "overbar", "[n] children/*[1]; [t] \"उर्ध्व दंड\""], ["Action", "underbar", "[n] children/*[1]; [t] \"अधो दंड\""], ["Action", "overtilde", "[n] children/*[1]; [t] \"उर्ध्व अंतर-चिह्न\""], ["Action", "undertilde", "[n] children/*[1]; [t] \"अधो अंतर-चिह्न\""], ["Action", "matrix", "[t] \"आरंभ\"; [t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"आव्यूह\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"पंक्ति \", grammar:gender=\"f\"); [t] \"आव्यूह समाप्त\""], ["Action", "matrix-sbrief", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"आव्यूह\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"पंक्ति \", grammar:gender=\"f\"); [t] \"आव्यूह समाप्त\""], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFordinalCounter, context:\"स्तंभ\", pause:200)"], ["Action", "row-with-label", "[t] \"लेबल के साथ\"; [n] content/*[1]; [t] \"लेबल समाप्त\" (pause:200); [m] children/* (ctxtFunc:CTFordinalCounter, context:\"स्तंभ\")"], ["Action", "row-with-label-brief", "[t] \"लेबल\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"स्तंभ\")"], ["Action", "row-with-text-label", "[t] \"लेबल\"; [t] CSFRemoveParens; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"स्तंभ\")"], ["Action", "empty-row", "[t] \"रिक्त\""], ["Action", "empty-cell", "[t] \"रिक्त\" (pause:300)"], ["Action", "determinant", "[t] \"आरंभ\"; [t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"सारणिक\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"पंक्ति \", grammar:gender=\"f\"); [t] \"सारणिक समाप्त\""], ["Action", "determinant-sbrief", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"सारणिक\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"पंक्ति \", grammar:gender=\"f\"); [t] \"सारणिक समाप्त\""], ["Action", "determinant-simple", "[t] \"आरंभ\"; [t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"सारणिक\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"पंक्ति\", grammar:simpleDet); [t] \"सारणिक समाप्त\""], ["Action", "determinant-simple-sbrief", "[t] count(children/*); [t] \"बाय\"; [t] count(children/*[1]/children/*); [t] \"सारणिक\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"पंक्ति\", grammar:simpleDet); [t] \"सारणिक समाप्त\""], ["Action", "layout", "[t] \"खाका आरंभ\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"पंक्ति \", grammar:gender=\"f\"); [t] \"खाका समाप्त\""], ["Action", "layout-sbrief", "[t] \"खाका\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"पंक्ति \", grammar:gender=\"f\"); [t] \"खाका समाप्त\""], ["Action", "binomial", "[t] \"द्विपद अथवा आव्यूह आरंभ\"; [n] children/*[1]/children/*[1]; [t] \"चयन करें\"; [n] children/*[2]/children/*[1]; [t] \"द्विपद अथवा आव्यूह समाप्त\""], ["Action", "binomial-sbrief", "[t] \"द्विपद अथवा आव्यूह\"; [n] children/*[1]/children/*[1]; [t] \"चयन करें\"; [n] children/*[2]/children/*[1]; [t] \"द्विपद अथवा आव्यूह समाप्त\""], ["Action", "cases", "[t] \"खाका आरंभ\"; [t] \"विस्तारित\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"पंक्ति \", grammar:gender=\"f\"); [t] \"खाका समाप्त\""], ["Action", "cases-sbrief", "[t] \"खाका\"; [t] \"विस्तारित\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"पंक्ति \", grammar:gender=\"f\"); [t] \"खाका समाप्त\""], ["Action", "line-with-label", "[t] \"लेबल के साथ\"; [n] content/*[1]; [t] \"लेबल समाप्त\" (pause:200); [m] children/*"], ["Action", "line-with-label-brief", "[t] \"लेबल\"; [n] content/*[1] (pause:200); [m] children/*"], ["Action", "line-with-text-label", "[t] \"लेबल\"; [t] CSFRemoveParens; [m] children/*"], ["Action", "empty-line", "[t] \"रिक्त\""], ["Action", "empty-line-with-label", "[t] \"लेबल के साथ\"; [n] content/*[1]; [t] \"लेबल समाप्त\" (pause:200); [t] \"रिक्त\""], ["Action", "empty-line-with-label-brief", "[t] \"लेबल\"; [n] content/*[1] (pause:200); [t] \"रिक्त\""], ["Action", "enclose", "[t] \"घेरे का आरंभ\"; [t] @role (grammar:localEnclose); [n] children/*[1]; [t] \"घेरा समाप्त\""], ["Action", "leftbar", "[t] \"ऊर्ध्वाधर दंड\"; [n] children/*[1]"], ["Action", "rightbar", "[n] children/*[1]; [t] \"ऊर्ध्वाधर दंड\""], ["Action", "crossout", "[t] \"मिटाया हुआ\"; [n] children/*[1]; [t] \"मिटाया हुआ समाप्त\""], ["Action", "cancel", "[t] \"मिटाया हुआ\"; [n] children/*[1]/children/*[1]; [t] \"के साथ\"; [n] children/*[2]; [t] \"मिटाया हुआ समाप्त\""], ["Action", "cancel-reverse", "[t] \"मिटाया हुआ\"; [n] children/*[2]/children/*[1]; [t] \"के साथ\"; [n] children/*[1]; [t] \"मिटाया हुआ समाप्त\""], ["Action", "multi-inference", "[t] \"अनुमान नियम\"; [m] content/*; [t] \"निष्कर्ष के साथ\"; [n] children/*[1]; [t] \"और\"; [t] count(children/*[2]/children/*); [t] \"पूर्वपक्ष\""], ["Action", "inference", "[t] \"अनुमान नियम\"; [m] content/*; [t] \"निष्कर्ष के साथ\"; [n] children/*[1]; [t] \"और\"; [t] count(children/*[2]/children/*); [t] \"पूर्वपक्ष\""], ["Action", "premise", "[m] children/* (ctxtFunc:CTFordinalCounter, context:\"premise \")"], ["Action", "conclusion", "[n] children/*[1]"], ["Action", "label", "[t] \"सूचक पर्चा\"; [n] children/*[1]"], ["Action", "axiom", "[t] \"अभिगृहीत\"; [m] children/*[1]"], ["Action", "empty-axiom", "[t] \"रिक्त अभिगृहीत\""]]}, "hi/rules/prefix_hindi.min": {"modality": "prefix", "domain": "default", "locale": "hi", "inherits": "base", "rules": []}, "hi/rules/prefix_hindi_actions.min": {"modality": "prefix", "domain": "default", "locale": "hi", "kind": "actions", "rules": [["Action", "numerator", "[t] \"अंश\" (pause:200)"], ["Action", "denominator", "[t] \"हर\" (pause:200)"], ["Action", "base", "[t] \"आधार\" (pause:200)"], ["Action", "exponent", "[t] \"घातांक\" (pause:200)"], ["Action", "subscript", "[t] \"पादांक\" (pause:200)"], ["Action", "overscript", "[t] \"उर्ध्वान्क\" (pause:200)"], ["Action", "underscript", "[t] \"निम्नांक\" (pause:200)"], ["Action", "radicand", "[t] \"करणीगत राशि\" (pause:200)"], ["Action", "index", "[t] \"मूलांक\" (pause:200)"], ["Action", "leftsub", "[t] \"वाम पादांक\" (pause:200)"], ["Action", "leftsub-counted", "[t] CSFordinalPosition; [t] \"वाम पादांक\" (pause:200)"], ["Action", "leftsuper", "[t] \"वाम उर्ध्वान्क\" (pause:200)"], ["Action", "leftsuper-counted", "[t] CSFordinalPosition; [t] \"वाम उर्ध्वान्क\" (pause:200)"], ["Action", "rightsub", "[t] \"दक्षिण पादांक\" (pause:200)"], ["Action", "rightsub-counted", "[t] CSFordinalPosition; [t] \"दक्षिण पादांक\" (pause:200)"], ["Action", "<PERSON><PERSON>r", "[t] \"दक्षिण उर्ध्वान्क\" (pause:200)"], ["Action", "rightsuper-counted", "[t] CSFordinalPosition; [t] \"दक्षिण उर्ध्वान्क\" (pause:200)"], ["Action", "choice", "[t] \"विकल्प तादात\" (pause:200)"], ["Action", "select", "[t] \"चयन तादात\" (pause:200)"], ["Action", "row", "[t] CSFordinalPosition (grammar:gender=\"f\"); [t] \"पंक्ति\" (pause:200)"], ["Action", "cell", "[n] ../..; [t] CSFordinalPosition; [t] \"स्तंभ\" (pause:200)"], ["Action", "cell-simple", "[t] CSFordinalPosition; [t] \"स्तंभ\" (pause:200)"]]}, "hi/rules/summary_hindi.min": {"locale": "hi", "modality": "summary", "inherits": "base", "rules": []}, "hi/rules/summary_hindi_actions.min": {"locale": "hi", "modality": "summary", "kind": "actions", "rules": [["Action", "abstr-identifier-long", "[t] \"दीर्घ तत्समक\""], ["Action", "abstr-identifier", "[t] \"तत्समक\""], ["Action", "abstr-number-long", "[t] \"दीर्घ संख्या\""], ["Action", "abstr-number", "[t] \"संख्या\""], ["Action", "abstr-mixed-number-long", "[t] \"दीर्घ मिश्र संख्या\""], ["Action", "abstr-mixed-number", "[t] \"मिश्र संख्या\""], ["Action", "abstr-text", "[t] \"पाठ\""], ["Action", "abstr-function", "[t] \"फलन व्यंजक\""], ["Action", "abstr-function-brief", "[t] \"फलन\""], ["Action", "abstr-lim", "[t] \"फलन सीमा\""], ["Action", "abstr-lim-brief", "[t] \"सीमा\""], ["Action", "abstr-fraction", "[t] \"भिन्न\""], ["Action", "abstr-fraction-brief", "[t] \"अपूर्णांश\""], ["Action", "abstr-continued-fraction", "[t] \"सतत भिन्न\""], ["Action", "abstr-continued-fraction-brief", "[t] \"सतत अपूर्णांश\""], ["Action", "abstr-sqrt", "[t] \"वर्ग मूल\""], ["Action", "abstr-sqrt-nested", "[t] \"नीड़ित वर्ग मूल\""], ["Action", "abstr-root-end", "[t] \"वां मूल\"; [n] children/*[1] (engine:modality=speech); [t] \"मूलांक समाप्त\""], ["Action", "abstr-root", "[n] children/*[1] (engine:modality=speech); [t] \"वां मूल\""], ["Action", "abstr-root-brief", "[t] \"मूल\""], ["Action", "abstr-root-nested-end", "[t] \"नीड़ित मूल्यांक\"; [n] children/*[1] (engine:modality=speech); [t] \"मूलांक समाप्त\""], ["Action", "abstr-root-nested", "[t] \"नीड़ित मूल्यांक\"; [n] children/*[1] (engine:modality=speech)"], ["Action", "abstr-root-nested-brief", "[t] \"नीड़ित मूल\""], ["Action", "abstr-superscript", "[t] \"घातांक\""], ["Action", "abstr-subscript", "[t] \"पादांक\""], ["Action", "abstr-subsup", "[t] \"पादांक वाला घातांक\""], ["Action", "abstr-infixop", "[t] count(./children/*); [t] \"अवयव\"; [t] \"के साथ\"; [t] @role (grammar:localRole)"], ["Action", "abstr-infixop-var", "[t] \"चल तादात के अवयवों के साथ\"; [t] @role (grammar:localRole)"], ["Action", "abstr-infixop-brief", "[t] @role (grammar:localRole)"], ["Action", "abstr-addition", "[t] count(./children/*); [t] \"पद\"; [t] \"के साथ योग\""], ["Action", "abstr-addition-brief", "[t] \"योग\""], ["Action", "abstr-addition-var", "[t] \"चल तादात के पदों का योग\""], ["Action", "abstr-multiplication", "[t] count(./children/*); [t] \"गुणनखंड\"; [t] \"के साथ गुणनफल\""], ["Action", "abstr-multiplication-brief", "[t] \"गुणनफल\""], ["Action", "abstr-multiplication-var", "[t] \"चल तादात के गुणनखंडो का गुणनफल\""], ["Action", "abstr-vector", "[t] count(./children/*); [t] \"विमीय सदिश\""], ["Action", "abstr-vector-brief", "[t] \"सदिश\""], ["Action", "abstr-vector-var", "[t] \"एन विमीय सदिश\""], ["Action", "abstr-binomial", "[t] \"द्विपद\""], ["Action", "abstr-determinant", "[t] count(./children/*); [t] \"विमीय सारणिक\""], ["Action", "abstr-determinant-brief", "[t] \"सारणिक\""], ["Action", "abstr-determinant-var", "[t] \"एन विमीय सारणिक\""], ["Action", "abstr-squarematrix", "[t] count(./children/*); [t] \"विमीय वर्ग आव्यूह\""], ["Action", "abstr-squarematrix-brief", "[t] \"वर्ग आव्यूह\""], ["Action", "abstr-rowvector", "[t] count(./children/row/children/*); [t] \"विमीय पंक्ति सदिश\""], ["Action", "abstr-rowvector-brief", "[t] \"पंक्ति सदिश\""], ["Action", "abstr-rowvector-var", "[t] \"एन विमीय पंक्ति सदिश\""], ["Action", "abstr-matrix", "[t] count(children/*); [t] \"गुणा\"; [t] count(children/*[1]/children/*); [t] \"आव्यूह\""], ["Action", "abstr-matrix-brief", "[t] \"आव्यूह\""], ["Action", "abstr-matrix-var", "[t] \"एन गुणा एम आव्यूह\""], ["Action", "abstr-cases", "[t] count(children/*); [t] \"फलन उपशर्त\"; [t] \"के साथ\"; [t] \"फलन उपशर्त कथन\""], ["Action", "abstr-cases-brief", "[t] \"फलन उपशर्त कथन\""], ["Action", "abstr-cases-var", "[t] \"चल तादात की फलन उपशर्तों वाला फलन उपशर्त कथन\""], ["Action", "abstr-punctuated", "[t] count(children/*) - count(content/*); [t] \"लम्बाई की\"; [n] content/*[1]; [t] \"विभाजित सूचि\""], ["Action", "abstr-punctuated-brief", "[n] content/*[1]; [t] \"विभाजित सूचि\""], ["Action", "abstr-punctuated-var", "[t] \"चल लम्बाई की\"; [n] content/*[1]; [t] \"विभाजित सूचि\""], ["Action", "abstr-bigop", "[n] content/*[1]"], ["Action", "abstr-integral", "[t] \"समाकल\""], ["Action", "abstr-relation", "[t] @role (grammar:localRole)"], ["Action", "abstr-relation-seq", "[t] count(./children/*); [t] \"अवयव\"; [t] \"के साथ\"; [t] @role (grammar:localRole); [t] \"अनुक्रम\""], ["Action", "abstr-relation-seq-brief", "[t] @role (grammar:localRole); [t] \"अनुक्रम\""], ["Action", "abstr-relation-var", "[t] \"चल तादात के अवयवों के साथ\"; [t] @role (grammar:localRole); [t] \"अनुक्रम\""], ["Action", "abstr-multirel", "[t] count(./children/*); [t] \"अवयव\"; [t] \"के साथ\"; [t] \"संबंधानुक्रम\""], ["Action", "abstr-multirel-brief", "[t] \"संबंधानुक्रम\""], ["Action", "abstr-multirel-var", "[t] \"चल तादात के अवयवों का संबंधानुक्रम\""], ["Action", "abstr-table", "[t] count(children/*); [t] \"पक्तियां और\"; [t] count(children/*[1]/children/*); [t] \"स्तंभ\"; [t] \"के साथ सारणी\""], ["Action", "abstr-line", "[t] @role (grammar:localRole); [t] \"में\""], ["Action", "abstr-row", "[t] \"में\"; [t] @role (grammar:localRole); [t] count(preceding-sibling::..); [t] \"के साथ\"; [t] count(children/*); [t] \"स्तंभ\""], ["Action", "abstr-cell", "[t] @role (grammar:localRole); [t] \"में\""]]}}