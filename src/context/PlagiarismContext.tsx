import React, { createContext, useContext, useState, ReactNode } from 'react';
import { BookSearchResult } from '../interface/searching.ts';

interface PlagiarismContextType {
    selectedBooks: BookSearchResult[];
    addBook: (book: BookSearchResult) => void;
    removeBook: (bookId: string) => void;
    clearBooks: () => void;
    isBookSelected: (bookId: string) => boolean;
    getSelectedCount: () => number;
}

const PlagiarismContext = createContext<PlagiarismContextType | undefined>(undefined);

interface PlagiarismProviderProps {
    children: ReactNode;
}

export function PlagiarismProvider({ children }: PlagiarismProviderProps) {
    const [selectedBooks, setSelectedBooks] = useState<BookSearchResult[]>([]);

    const addBook = (book: BookSearchResult) => {
        setSelectedBooks(prev => {
            // 检查是否已经存在
            if (prev.some(b => b.id === book.id)) {
                return prev;
            }
            return [...prev, book];
        });
    };

    const removeBook = (bookId: string) => {
        setSelectedBooks(prev => prev.filter(book => book.id !== bookId));
    };

    const clearBooks = () => {
        setSelectedBooks([]);
    };

    const isBookSelected = (bookId: string) => {
        return selectedBooks.some(book => book.id === bookId);
    };

    const getSelectedCount = () => {
        return selectedBooks.length;
    };

    const value: PlagiarismContextType = {
        selectedBooks,
        addBook,
        removeBook,
        clearBooks,
        isBookSelected,
        getSelectedCount,
    };

    return (
        <PlagiarismContext.Provider value={value}>
            {children}
        </PlagiarismContext.Provider>
    );
}

export function usePlagiarism() {
    const context = useContext(PlagiarismContext);
    if (context === undefined) {
        throw new Error('usePlagiarism must be used within a PlagiarismProvider');
    }
    return context;
}
