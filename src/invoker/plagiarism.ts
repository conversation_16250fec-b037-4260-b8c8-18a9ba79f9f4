import { invoke } from "@tauri-apps/api/core";
import { ResponseVO } from "../interface";
import { isTauriEnv } from "../utils/system.ts";
import axios from "axios";
import {
    PlagiarismCompareParams,
    PlagiarismResultParams,
    PlagiarismResult,
    BatchListParams,
    BatchListResult,
    BatchStatistics,
    PlagiarismBatch
} from "../interface/plagiarism.ts";

// 开始查重对比
export const invokePlagiarismCompare = async (params: PlagiarismCompareParams): Promise<PlagiarismBatch> => {
    let resp: ResponseVO<PlagiarismBatch>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<PlagiarismBatch>>("plagiarism_compare", { params });
    } else {
        resp = (await axios.post<ResponseVO<PlagiarismBatch>>('/api/plagiarism/compare', { ...params })).data;
    }
    if (resp.data !== null) { 
        return resp.data; 
    }
    throw Error('Error while starting plagiarism comparison');
};

// 获取查重结果
export const invokePlagiarismResults = async (params: PlagiarismResultParams): Promise<PlagiarismResult> => {
    let resp: ResponseVO<PlagiarismResult>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<PlagiarismResult>>("plagiarism_results", { params });
    } else {
        resp = (await axios.post<ResponseVO<PlagiarismResult>>('/api/plagiarism/results', { ...params })).data;
    }
    if (resp.data !== null) { 
        return resp.data; 
    }
    throw Error('Error while fetching plagiarism results');
};

// 获取批次列表
export const invokeBatchList = async (params: BatchListParams): Promise<BatchListResult> => {
    let resp: ResponseVO<BatchListResult>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<BatchListResult>>("plagiarism_batch_list", { params });
    } else {
        resp = (await axios.post<ResponseVO<BatchListResult>>('/api/plagiarism/batches', { ...params })).data;
    }
    if (resp.data !== null) { 
        return resp.data; 
    }
    throw Error('Error while fetching batch list');
};

// 获取批次详情
export const invokeBatchDetail = async (batchId: string): Promise<PlagiarismBatch> => {
    let resp: ResponseVO<PlagiarismBatch>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<PlagiarismBatch>>("plagiarism_batch_detail", { batchId });
    } else {
        resp = (await axios.get<ResponseVO<PlagiarismBatch>>(`/api/plagiarism/batch/${batchId}`)).data;
    }
    if (resp.data !== null) { 
        return resp.data; 
    }
    throw Error('Error while fetching batch detail');
};

// 获取批次统计信息
export const invokeBatchStatistics = async (batchId: string): Promise<BatchStatistics> => {
    let resp: ResponseVO<BatchStatistics>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<BatchStatistics>>("plagiarism_batch_statistics", { batchId });
    } else {
        resp = (await axios.get<ResponseVO<BatchStatistics>>(`/api/plagiarism/batch/${batchId}/statistics`)).data;
    }
    if (resp.data !== null) { 
        return resp.data; 
    }
    throw Error('Error while fetching batch statistics');
};

// 删除批次
export const invokeDeleteBatch = async (batchId: string): Promise<boolean> => {
    let resp: ResponseVO<boolean>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<boolean>>("plagiarism_delete_batch", { batchId });
    } else {
        resp = (await axios.delete<ResponseVO<boolean>>(`/api/plagiarism/batch/${batchId}`)).data;
    }
    if (resp.data !== null) { 
        return resp.data; 
    }
    throw Error('Error while deleting batch');
};

// 取消正在进行的查重任务
export const invokeCancelComparison = async (batchId: string): Promise<boolean> => {
    let resp: ResponseVO<boolean>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<boolean>>("plagiarism_cancel_comparison", { batchId });
    } else {
        resp = (await axios.post<ResponseVO<boolean>>(`/api/plagiarism/batch/${batchId}/cancel`)).data;
    }
    if (resp.data !== null) { 
        return resp.data; 
    }
    throw Error('Error while canceling comparison');
};
