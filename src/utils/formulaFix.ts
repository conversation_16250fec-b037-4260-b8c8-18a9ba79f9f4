const LETTER_G = /([^\\][a-z]{2,}[{[])/gi
const LETTER = /([^\\][a-z]{2,}[{[])/
const right = '\\right'
const begin = '\\begin'


// 公式补全
export function formula(questionTypeMarks: any) {
  const KEY = ['stem', 'analysis', 'choicesList', 'answer']
  const text = JSON.parse(questionTypeMarks)
  text.forEach((item: { questions: any[] }) => {
    item.questions.forEach(_item => {
      KEY.forEach(_ => {
        if (_ !== 'choicesList') {
          if (_item[_]) _item[_] = handleDollar(formulaFix(_item[_]))
        } else {
          if (_item[_] && _item[_].length) {
            _item[_].forEach((content: any, index: string | number) => {
              _item[index] = handleDollar(formulaFix(content))
            })
          }
        }
      })
    })
  })
  return JSON.stringify(text)
}

export function formulaFix(text) {
  const hadReplace = []
  const hadReplaceText = []
  text = text
    .replaceAll('\begin', begin)
    .replaceAll('\right', right)
    .replaceAll('\big', '\\\\big')
    .replaceAll('\bot', '\\\\bot')
    .replaceAll('\\cdot', '\\\\cdot')
    .replaceAll('\boldsymbol', '\\\\boldsymbol')
    .replaceAll('\therefore', '\\\\therefore')
    .replaceAll('\\\therefore', '\\\\therefore')
    .replaceAll('\because', '\\\\because')
    .replaceAll('\\\because', '\\\\because')
    // .replaceAll('ight', right)
    .replaceAll('\\r\\right', right)
    // .replaceAll('egin', begin)
    .replaceAll('\\b\\begin', begin)
    .replaceAll('\\overr\\r', '\\overr')
    .replaceAll('{circ', '{\\circ')
    .replaceAll('{angle', '{\\angle')
  const matchList = text.match(LETTER_G)
  if (matchList) {
    matchList.forEach(item => {
      if (item < 'a' || item > 'z') {
        const group = LETTER.exec(item)
        if (group) {
          const rep = group[1].substring(1)
          if (hadReplaceText.indexOf(rep) < 0) {
            hadReplaceText.push(rep)
            hadReplace.push([rep, '\\' + rep])
          }
        }
      }
    })
    hadReplace.forEach(repl => {
      text = text.replaceAll(repl[0], repl[1])
    })
  }
  return text
    .replace(/\\left\|/g, '\\left|')
    .replace(/\\left\(/g, '\\left(')
    .replace(/\\{2,}left/g, '\\left')
    .replaceAll('imes', '\\times')
    .replaceAll('\\t\\times', '\\times')
    .replaceAll('alpha', '\\alpha')
    .replaceAll(/\\{2,}alpha/g, '\\alpha')
    .replaceAll('Delta', '\\Delta')
    .replaceAll(/\\{2,}Delta/g, '\\Delta')
    .replaceAll('geq', '\\geq')
    .replaceAll(/\\{2,}geq/g, '\\geq')
    .replaceAll('leq', '\\leq')
    .replaceAll(/\\{2,}leq/g, '\\leq')
    .replaceAll('infty', '\\infty')
    .replaceAll(/\\{2,}infty/g, '\\infty')
    .replaceAll('\\left{', '\\left\\{')
    .replaceAll('\\\n', '\\\\\n')
    .replaceAll('\\\\\\\n', '\\\\\n')
    .replaceAll(' eq ', ' = ')
    .replaceAll('\neq ', '\n= ')
    .replaceAll(' eq\n', ' =\n')
    .replaceAll('cap ', '\\cap ')
    // .replaceAll('leftlangle', '<')
}

const DOLLAR_G = /(\${1,2}[^$]+\${1,2})/gi
// const LETTAX = /[^\\][a-z]{2,}[\(\{].*?[\}]/
const LETTAX = /^[^\\][a-z]{2,}[({].*?[}]/

export function handleDollar(text) {
  let cpText = text
  const contents = []
  const contentRepls = []
  let mat
  const splitMark = '----qctchina----'
  // const nsplit = cpText.split('\n')
  // const texts = []
  // let tempText = ''
  // nsplit.forEach(n => {
  //   if (n.length > 0) {
  //     if (n.match(LETTAX)) {
  //       tempText = (tempText.length > 0) ? tempText + '\n' + n : n
  //     } else {
  //       if (tempText.length > 0) {
  //         texts.push(tempText)
  //         tempText = ''
  //       } else {
  //         texts.push(n)
  //       }
  //     }
  //   }
  // })
  // if (tempText.length > 0) {
  //   texts.push(tempText)
  // }
  const matchList = cpText.match(DOLLAR_G)
  if (matchList) {
    matchList.forEach(item => {
      cpText = cpText.replace(item, splitMark)
    })
    const split = cpText.split(splitMark)
    split.forEach(content => {
      mat = content.match(LETTAX)
      if (mat) {
        if (contents.indexOf(content) < 0) {
          contents.push(content)
          contentRepls.push([content, '$$' + content + '$$'])
        }
      }
    })
    contentRepls.forEach(repl => {
      text = text.replaceAll(repl[0], repl[1])
    })
  } else {
    mat = cpText.match(LETTAX)
    const matchImg = cpText.match(/<img.*?src=.*?>/)
    const mathMatch = cpText.match(/\\[cos,sin,tan].*?[0-9]{1,}\^\{\\circ\}/) || cpText.match(/[0-9]{1,}\^\{\\circ\}/)
    if (mat && matchImg) {
      // text = text.replaceAll('\n', '\\\\')
    } else if (mathMatch) {
      text = '$$' + text.replaceAll('\n', '\\\\') + '$$'
    } else if (mat) {
      text = '$$' + text.replaceAll('\n', '\\\\') + '$$'
    }
  }
  return text
}

