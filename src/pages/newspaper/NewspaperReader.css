.title_btn {
    display: inline-block;
    width: 95%;
    height: 50px;
    overflow: hidden; /* 隐藏超出部分 */
    white-space: nowrap;
    text-align: left;
    text-overflow: ellipsis; /* 超出部分显示省略号 */
}
.title_btn2 {
    display: inline-block;
    width: 95%;
    /*height: auto;*/
    overflow: hidden; /* 隐藏超出部分 */
    white-space: nowrap;
    text-align: left;
    text-overflow: ellipsis; /* 超出部分显示省略号 */
}
.thumb-image-group {
    display: flex;
    overflow-y: scroll;
    height: calc(100vh - 90px);
    flex-direction: column;
    align-items: center;
}

.thumb-img {
    display: flex;
    justify-content: center;
    border-radius: 10px;
    border: 1px #2D3A4A solid;
    margin-bottom: 5px;
    padding: 5px;
    width: 126px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
}

.touming {
    background-color: lightskyblue;
    opacity: 0.5;
    border-radius: 10px;
}


.new_image-group {
    display: flex;
    overflow-y: scroll;
    height: calc(100vh - 90px);
    width: 100%;
    flex-direction: column;
    align-items: center;
}

.content-img {
    width: 100%;
    border-radius: 10px;
    /* border: 1px #2D3A4A solid; */
    box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
}

.text-group {
    display: flex;
    overflow-y: scroll;
    height: calc(100vh - 90px);
    width: 100%;
    flex-direction: column;
    align-items: center;
}

.new_search-group-mid {
    background-color: #F0F2F5;
    overflow-y: auto;
    height: calc(100vh - 160px);
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.search-group-bottom {
    background-color: #F0F2F5;
    height: 32px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.highlight {
    background-color: #fffa98;
}