import {useParams} from "react-router-dom";
import {MutableRefObject, useEffect, useRef, useState} from "react";
import {DatePicker, DatePickerProps, Empty, FloatButton, Image, Pagination, TabsProps, Tooltip} from 'antd';
import {Col, Row, Tabs, Input, Card, Button, message, Spin} from 'antd';
import './NewspaperReader.css'
import {
    InteractionTwoTone,
    LeftCircleTwoTone,
    RightCircleTwoTone,
    SwapRightOutlined
} from '@ant-design/icons';
import {
    Content, Snapshot, CardSearchResult, Catalogue
} from "../../interface/NewspaperInterface.ts";
import Mathdown from "../mathdown/Mathdown.tsx";
import React from 'react';
// import jsPDF from 'jspdf';
import {Newspaper} from "../../interface/NewspaperInterface.ts";
import dayjs, {Dayjs} from "dayjs";
import 'dayjs/locale/zh-cn';
import {getCurrentWindow} from "@tauri-apps/api/window";
import {buildFileUrl, buildImgSrc} from "../../utils/fileUrl.ts";
import { getFeFileUrlPrefix } from "../../invoker/file-url.ts";
import {highlight_text,processContent} from "../../utils/text.ts";
import {invokeGetBookByBookId, invokeGetBookNewsInfoByBrandPubDate} from "../../invoker/book.ts";
import {invokeGetNewspaperCatalogueByBookId} from "../../invoker/catalogue.ts";
import {invokeGetCardContentByBookIdCardId} from "../../invoker/card.ts";
import {invokeGlobalSearchCards} from "../../invoker/searching.ts";
import {isTauriEnv} from "../../utils/system.ts";

// type SearchProps = GetProps<typeof Input.Search>;
const {Search} = Input;

const NewspaperThumbImage: React.FC<{
    snapshot: Snapshot; index: number;  highlight: boolean
}> = ({snapshot, index, highlight}) => {
    const className = highlight ? 'touming' : '';
    return (
        <div className={className}>
            <img
                className={"thumb-img"}
                src={snapshot.url}
                alt={'snapshot_' + index}
                style={{
                    width: "100%",   // 让图片宽度适应父容器
                    height: "80%"   // 保持图片的纵横比例，避免失真
                }}
            />
        </div>
    );
};

const NewspaperImage: React.FC<{
    snapshot: Snapshot; index: number; onMouseEnter?: () => void;
}> = ({snapshot, index, onMouseEnter}) => {
    const [showPreview, setSHowPreview] = useState(true);

    return (
        <div>
            <div onMouseEnter={onMouseEnter}>
                <Image
                    style={{display: "inline-block"}}
                    src={snapshot.url}
                    preview={{
                        onVisibleChange:()=>{setSHowPreview(!showPreview)},
                        visible:showPreview,
                        src: `${snapshot.url}`,
                        toolbarRender: (originalNode) => {
                            return (
                                <div style={{display: "flex", margin: 8}}>
                                    <div style={{
                                        ...originalNode.props.style,
                                        backgroundColor: '#333',
                                        color: '#fff',
                                        border: '1px solid #555',
                                        padding: '5px 10px',
                                        borderRadius: '4px',
                                    }}>
                                    {originalNode}
                                    </div>
                                </div>
                            )
                        }
                    }}/>
            </div>
        </div>
    );
};


const NewspaperContentText: React.FC<{
    content?: Content;
}> = ({content}) => {
    return (

        <div>
            <div style={{whiteSpace: 'pre-wrap'}}>
                {content !== undefined && content !== null && (
                    <Mathdown content={content.text}/>
                )}
                {(content == undefined || false) && (
                    <Empty description="此版面无文章"/>
                )}
            </div>
        </div>
    )
};


// @ts-ignore
function NewspaperReader() {
    const [messageApi, contextHolder] = message.useMessage();
    const {bookId} = useParams<{ bookId: string }>();
    const {page} = useParams<{ page: string }>();
    const {id} = useParams<{ id: string }>();
    const [highlights, setHighlights] = useState<string[]>(['']);
    const [naviTab, setNaviTab] = useState<string>("img");
    const [contentTab, setContentTab] = useState<string>("txt");
    const [searchInfo, setSearchInfo] = useState<{
        pageNo: number;
        pageSize: number;
        searchText: string;
        subjectCode: null;
        bookId: number;
    }>({
        pageNo: 0,
        pageSize: 10,
        searchText: "",
        subjectCode: null,
        bookId: 0
    });
    const [searchResult, setSearchResult] = useState<{
        highlights:string[],
        list:any[],
        totals:number
    }>({
        // duration: 0,
        highlights: [],
        list: [],
        // pageCount: 0,
        // pageNo: 0,
        // pageSize: 0,
        totals: 0
    })
    const [searchLoading, setSearchLoading] = useState<boolean>(false)
    const page_ = useRef<boolean>(false);

    const [book, setBook] = useState<Newspaper>({
        bookId: 0,
        batch:0,
        author: "",  bookName: "", edition: "", institutionCode: "", isbn: "", publisher: "", series: "",
        snapshots: [], contents: [], subjectCode: "", subjectName: "", type: "", updateTime: "", version: ""
    });
    const [cataloguesNodes, setCataloguesNodes] = useState<Catalogue[]>([{
        catalogueId: 0,
        children: [],
        title: ""
    }]);
    const [cataloguesNode, setCataloguesNode] = useState<Catalogue[]>([{
        catalogueId: 0,
        children: [],
        title: ""
    }]);
    const [targetThumbIndex, setTargetThumbIndex] = useState<number>(-2);
    const textRefs: MutableRefObject<any[]> = useRef([]);
    const imgRefs: MutableRefObject<any[]> = useRef([]);
    const thumbImgRefs: MutableRefObject<any[]> = useRef([]);
    // const [progress, setProgress] = useState(0);
    // const [showProgress, setShowProgress] = useState(false); // 控制进度条显示的状态
    const [treeData,setTreeData] = useState<Catalogue[]>([]);
    const [thumbImage,setThumbImage] = useState<{
        page:number;
        path:string;
        url:string;
    }>({
        page:0,
        path:"",
        url:""
    });
    const [image,setImage] = useState<{
        page:number;
        path:string;
        url:string;
    }>({
        page:0,
        path:"",
        url:""
    });
    const [clickIndex, setClickIndex] = useState(0);
    const [clickIndex2, setClickIndex2] = useState(0);
    const [brand, setBrand] = useState("");
    const [publishDate, setPublishDate] = useState<string>("");
    const initPage = useRef(true);
    let initData = false;
    const [ppage, setPpage] = useState(1);

    async function getBookInfo(bookId_?: number) {
        let res:any = [];
        let nBook!: Newspaper;
        let bookID!: string | undefined;
        bookId_ === undefined ?
            bookID = bookId
            : bookID = bookId_.toString();
        const urlPrefix = await getFeFileUrlPrefix();

        res = await invokeGetBookByBookId(bookID as string);
        res.data.length > 0 ?
            initData = true
            : initData = false;
        if (!initData) {
            messageApi.open({
                type: 'error',
                content: '查询日期无报纸',
            });
        } else {
            messageApi.open({
                key:"loading",
                type: 'loading',
                content: '查询中',
                duration: 0,
            });
            setPublishDate(res.data[0].publishDate);
            setBrand(res.data[0].brand);
            nBook = res.data[0];
        }
        nBook.contents = [];
        if (initData) {
            for (const snapshot of nBook.snapshots) {
                snapshot.url = buildFileUrl(urlPrefix, snapshot.path)
            }
            setThumbImage(nBook.snapshots[0])
            setImage(nBook.snapshots[0])
            const res0:any = await invokeGetNewspaperCatalogueByBookId(nBook.bookId.toString());
            const res2: {[index: number]:Catalogue} = res0.data
            setCataloguesNodes(res2)
            setCataloguesNode(res2)
            setTreeData(res2[0].children);
            setClickIndex(0);

            if (res2 && res2[0].children.length > 0) {
                const text_:any = await invokeGetCardContentByBookIdCardId({
                    "bookId": Number(bookID),
                    "cardId": Number(res2[0].children[0].cardId)
                });
                let text = text_.data[0].content;
                text = buildImgSrc(urlPrefix, text);
                nBook.contents = [{
                    page: 1,
                    cardId: res2[0].children[0].cardId,
                    text: text
                }];
            }

            if (page_.current) {
                setTreeData(res2[Number(page)-1].children);
                setThumbImage(nBook.snapshots[Number(page)-1])
                setImage(nBook.snapshots[Number(page)-1])
                setClickIndex(Number(page)-1)
                res2[Number(page)-1].children.forEach((e,index) => {
                    return e.cardId === Number(id) ? setClickIndex2(index) :null;
                })
                setContentTab('img')
                const text_:any = await invokeGetCardContentByBookIdCardId({
                    "bookId": Number(bookID),
                    "cardId": Number(id)
                });
                const text = buildImgSrc(urlPrefix, text_.data[0].content)
                nBook.contents = [{
                    page: 1,
                    cardId: Number(id),
                    text: text
                }];
                page_.current=false;
            }
            if (isTauriEnv) {
                await getCurrentWindow().setTitle(nBook.bookName);
            }
            setBook(nBook);
            messageApi.destroy("loading")
            messageApi.open({
                type: 'success',
                content: '查询报纸页成功',
                duration:3
            });
            setNaviTab('catalogue');
            setContentTab('text');
        }
    }
    async function changeBook(types:number) {
        const res3:{
            code:number,
            data:{
                publishDate: string,
                bookId: number
            }[],
            msg:string
        } = await invokeGetBookNewsInfoByBrandPubDate({
            "brand": brand,
            "publishDate": publishDate,
            "types": types
        });
        if (res3.data.length === 0) {
            types === 1 ?
                messageApi.open({
                    type: 'error',
                    content: '已是最新一期报纸',
                }) : messageApi.open({
                    type: 'error',
                    content: '已是最早一期报纸',
                })
        } else {
            await getBookInfo(res3.data[0].bookId);
            setClickIndex2(0);
            setSearchResult({
                highlights: [],
                list: [],
                totals: 0
            });
        }
    }

    useEffect(() => {
        if (initPage.current) {
            page? page_.current=true : page_.current=false;
            const asyncFunction = async () => {
                await getBookInfo()
            };
            asyncFunction().then(() => {
            });
        }
        initPage.current = false;
    }, [ppage,treeData,book,cataloguesNode,thumbImage,image,brand,publishDate, initPage.current]);
    useEffect(() => {
    }, [clickIndex,treeData,book,cataloguesNode,thumbImage,image,brand,publishDate,]);

    const selectCatalogues = (info,index) => {
        setClickIndex2(index);
        setContentTab('text');
        const arr:any = [];
        arr.push({
            page: info.page,
            cardId: info.cardId,
            text: ""
        });
        loadText(info.cardId).then()
        setBook((prevBook:any) => ({
            ...prevBook,
            contents: arr,
        }));
    };

    useEffect(() => {
        if (targetThumbIndex > -2 && thumbImgRefs.current[targetThumbIndex])
            thumbImgRefs.current[targetThumbIndex].scrollIntoView()
    }, [targetThumbIndex]);


    async function loadText(cardId:number) {
        const updatedContents = [...book.contents];
        const targetContent = updatedContents[0];
        const text_:any = await invokeGetCardContentByBookIdCardId({
            "bookId": Number(book.bookId),
            "cardId": Number(cardId)
        });
        const urlPrefix = await getFeFileUrlPrefix();
        let text = text_.data[0].content;
        text = buildImgSrc(urlPrefix, text);
        targetContent.text = text
        updatedContents[0] = targetContent;
        setBook((prevBook) => ({
            ...prevBook,
            contents: updatedContents,
        }));
    }

    const onNaviTabChange = (key: string) => {
        setNaviTab(key)
    };

    // useEffect(() => {
    //     if (targetThumbIndex > -2 && thumbImgRefs.current[targetThumbIndex])
    //         thumbImgRefs.current[targetThumbIndex].scrollIntoView()
    // }, [naviTab]);

    /// 搜索决定是否删除
    const clickSearchResult = (searchContent: CardSearchResult,page:number,cardId:number) => {
        changeCatalogue(page-1);
        setClickIndex(page-1);
        // let index2 = 0;
        cataloguesNode[page-1].children.forEach(item => {
            item.title === searchContent ? setClickIndex2(item.page-1) : null
        })
        setContentTab('text');
        const arr:any = [];
        arr.push({
            page: page-1,
            cardId: cardId,
            text: ""
        });
        loadText(cardId).then()
        setBook((prevBook:any) => ({
            ...prevBook,
            contents: arr,
        }));
    }

    const onContentTabChange = (key: string) => {
        setContentTab(key)
    };
    /// 搜索决定是否删除
    useEffect(() => {
        if (contentTab === 'search') {
            return
        }

        // if (targetThumbIndex > -2) {
        //     jump(targetThumbIndex, 'index')
        //     if (book.contents.length > 0) {
        //         loadText(targetThumbIndex).then(r => {
        //         })
        //     }
        // }
    }, [contentTab,naviTab]);

    /// 搜索决定是否删除
    const searchClick = async (value: string, event?: any, source?: any) => {
        if (!value) {
            message.error('搜索内容为空')
            // return
        }
        setPpage(1);
        const predictCardParams = {
            bookId: Number(book.bookId),
            subjectCode: null,
            pageSize: 10,
            searchText: value,
            pageNo: 0
        }
        setSearchInfo(predictCardParams)
        await searchBookContent(predictCardParams)
    }

    /// 搜索决定是否删除
    const paginationOnChange = async (pages: number, pageSize: number) => {
        setPpage(pages);
        const predictCardParams = {
            ...searchInfo,
            pageNo: pages-1,
            pageSize: pageSize,
            bookId: Number(book.bookId)
        }
        setSearchInfo(predictCardParams)
        await searchBookContent(predictCardParams)
    }

    /// 搜索决定是否删除
    const searchBookContent = async (predictCardParams: any) => {
        setSearchLoading(true);

        try {
            // 调用接口获取搜索结果
            const res = await invokeGlobalSearchCards(predictCardParams);
            setHighlights(res.highlights)
            // 如果没有搜索结果，提示用户
            if (res.totals === 0) {
                message.info('无搜索结果');
            }
            // 获取 URL 前缀
            const urlPrefix = await getFeFileUrlPrefix();

            // 遍历 list，为每个 content 中的 img 标签的 src 加上 urlPrefix
            const updatedList = res.list.map((item:any) => {
                const updatedContent = item.content.replace(
                    /<img[^>]+src="([^">]+)"/g,
                    (match: any, src: any) => {
                        // 在 src 前面拼接 urlPrefix
                        return `<img src="${urlPrefix}/${src}"`;
                    }
                );
                // 返回更新后的对象
                return {
                    ...item,
                    // content: updatedContent,
                    content:updatedContent,
                };
            });

            // 更新搜索结果
            setSearchResult({
                ...res,
                list: updatedList,
            });
        } catch (error) {
            console.error('搜索失败:', error);
            message.error('搜索失败，请重试');
        } finally {
            setSearchLoading(false);
        }
    };

    const naviTabs0: TabsProps['items'] = [
        {key: 'catalogue', label: '版面',}
    ];
    const naviTabs: TabsProps['items'] = [
        {key: 'img', label: '缩略图',},
        {key: 'catalogue', label: '目录',}
    ];
    const contentTabs: TabsProps['items'] = [
        {key: 'text', label: '文字版',},
        {key: 'img', label: '图片版',},
        {key: 'search', label: '内容搜索',}
    ];
    const changeCatalogue = async (index:number) => {
        setClickIndex(index)
        setClickIndex2(0)
        setTreeData(cataloguesNode[index].children)
        const targetNode = cataloguesNode[index];
        const arr: Content[] = [];
        if (targetNode && targetNode.children.length > 0) {
            const text_:any = await invokeGetCardContentByBookIdCardId({
                "bookId": Number(book.bookId),
                "cardId": Number(targetNode.children[0].cardId)
            });
            const urlPrefix = await getFeFileUrlPrefix();
            let text = text_.data[0].content;
            text = buildImgSrc(urlPrefix, text);
            arr.push({
                page: 1,
                cardId: targetNode.children[0].cardId,
                text,
            });
        }
        setBook((prevBook) => ({
            ...prevBook,
            contents: [...arr],
        }));
        setThumbImage(book.snapshots[index])
        setImage(book.snapshots[index])
        setNaviTab('catalogue');
        setContentTab('text');
    }
    /// 搜索决定是否删除
    // const exportPdf = async () => {
    //     setShowProgress(true); // 点击后显示进度条
    //     const doc = new jsPDF();
    //     const pageWidth = doc.internal.pageSize.getWidth();
    //     const pageHeight = doc.internal.pageSize.getHeight();
    //
    //     const totalSnapshots = book.snapshots.length;
    //
    //     for (let i = 0; i < totalSnapshots; i++) {
    //         const img = new Image();
    //         img.src = book.snapshots[i].url;
    //
    //         await new Promise<void>((resolve) => {
    //             img.onload = () => {
    //                 const originalWidth = img.width;
    //                 const originalHeight = img.height;
    //                 const aspectRatio = originalWidth / originalHeight;
    //
    //                 let newWidth = pageWidth;
    //                 let newHeight = pageWidth / aspectRatio;
    //
    //                 if (newHeight > pageHeight) {
    //                     newHeight = pageHeight;
    //                     newWidth = pageHeight * aspectRatio;
    //                 }
    //
    //                 doc.addImage(img, 'JPEG', 0, 0, newWidth, newHeight);
    //
    //                 if (i < totalSnapshots - 1) {
    //                     doc.addPage();
    //                 }
    //                 resolve();
    //             };
    //         });
    //
    //         // 更新进度条
    //         const newProgress = Math.round(((i + 1) / totalSnapshots) * 100);
    //         setProgress(newProgress);
    //     }
    //     doc.save(`${book.bookName}.pdf`);
    //     setProgress(0); // 导出完成后重置进度
    //     setShowProgress(false); // 隐藏进度条
    // };

    const getDate = (tempDate: string) => {
        const date = tempDate.substring(0,publishDate.indexOf(" "));
        return date.toString();
    }
    const onChange: DatePickerProps<Dayjs[]>['onChange'] = async (date, dateString) => {
        const res3 = await invokeGetBookNewsInfoByBrandPubDate({
            "brand": brand,
            "publishDate": dateString as string,
            "types": 2
        });
        if (res3.data.length === 0) {
            messageApi.open({
                type: 'error',
                content: '所选日期无报纸',
            });
        } else {
            await getBookInfo(res3.data[0].bookId);
            setClickIndex2(0);
            setSearchResult({
                highlights: [],
                list: [],
                totals: 0
            });
        }
    };

    // 新增的滚动控制逻辑
    useEffect(() => {
        if (contentTab === 'search') {
            const searchContainer = document.querySelector('.new_search-group-mid');
            if (searchContainer) {
                searchContainer.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }
        }
    }, [searchResult]); // 当搜索结果变化时触发

    return (
        <>
            {contextHolder}
            <Row gutter={16}
                 style={{height:"100%"}}
            >
                <Col span={6} push={3}
                     style={{overflowY:"auto",height:"100%"}}
                >
                    <DatePicker
                        value={[dayjs(getDate(publishDate),'YYYY-MM-DD')]}
                        onChange={onChange}
                    />
                    <FloatButton.Group
                        trigger="hover"
                        placement={"left"}
                        icon={<InteractionTwoTone />}
                        style={{ insetInlineEnd: 30 }}
                        description={"切换"}
                    >
                        <Tooltip placement="top" title={"上期"}>
                            <FloatButton
                                icon={<LeftCircleTwoTone />}
                                onClick={() =>{
                                    setContentTab('text');
                                    changeBook(0).then()
                                }}
                            />
                        </Tooltip>
                        <Tooltip placement="top" title={"下期"}>
                            <FloatButton
                                icon={<RightCircleTwoTone />}
                                onClick={() =>{
                                    setContentTab('text');
                                    changeBook(1).then()
                                }}
                            />
                        </Tooltip>
                    </FloatButton.Group>
                    <Tabs items={naviTabs0} onChange={onNaviTabChange}/>
                    <div
                        style={{overflowY:"auto",height:"34%",width:"100%"}}
                    >
                        <Row align={"middle"} gutter={[0, 4]}>
                            {
                                cataloguesNodes.map((title, index1) => (
                                    <Col span={12} key={index1}>
                                        <Tooltip title={title.title}>
                                            <Button
                                                key={index1}
                                                className={"title_btn"}
                                                type={index1 === clickIndex ? "primary" : "default" }
                                                onClick={() =>changeCatalogue(index1)}
                                            >
                                                {title.title}
                                            </Button>
                                        </Tooltip>

                                    </Col>
                                ))
                            }
                        </Row>
                    </div>
                    <Tabs items={naviTabs} onChange={onNaviTabChange} activeKey={naviTab}/>
                    {naviTab === 'img' ? (
                        <div className={'thumb-image-group'} key="thumb_img"
                             style={{overflowY:"auto",height:"50%"}}
                        >
                            <div
                                key={'thumb_img_' + clickIndex}
                                ref={(el) => {
                                    thumbImgRefs.current[clickIndex] = el
                                }}
                            >
                                <NewspaperThumbImage
                                    snapshot={thumbImage}
                                    index={clickIndex}
                                    highlight={targetThumbIndex === clickIndex}
                                />
                            </div>
                        </div>
                    ) : null}
                    {naviTab === 'catalogue' ? (
                        <Row align={"middle"} gutter={[8, 8]}>
                            {
                                treeData.map((item, index2) => (
                                    <Col span={22} key={index2}>
                                        <Tooltip title={item.title}>
                                            <Button
                                                key={index2}
                                                className={"title_btn2"}
                                                variant="filled"
                                                color={index2 === clickIndex2 ? "primary" : "default" }
                                                onClick={() =>selectCatalogues(item,index2)}
                                            >
                                                {item.title}
                                            </Button>
                                        </Tooltip>

                                    </Col>
                                ))
                            }
                            {treeData.length === 0 && <>
                                <Empty description="此版面无文章"/>
                            </>}
                        </Row>
                    ) : null}
                </Col>
                <Col span={12} push={3} style={{overflowY:"auto",height:"100%"}}>
                    <Row gutter={16} align="middle">
                        <Col span={10}>
                            <Tabs items={contentTabs} onChange={onContentTabChange} activeKey={contentTab} />
                        </Col>
                        <Col span={14}>
                            <p style={{
                                color: '#e03e2d',
                                textAlign: 'right',
                            }}>温馨提示：文字版仅供检索及参考，最终以图片版为准</p>
                        </Col>
                        {/*/// 搜索决定是否删除*/}
                        {/*<Col span={3}>*/}
                        {/*    {showProgress ? ( // 根据状态决定是否渲染进度条*/}
                        {/*        <Button>*/}
                        {/*            <Progress percent={progress} steps={5} size="small" strokeColor="#4caf50"/>*/}
                        {/*        </Button>*/}
                        {/*    ):<Button type={"primary"} onClick={exportPdf}>导出 PDF</Button>}*/}
                        {/*</Col>*/}
                    </Row>
                    {contentTab === 'text' ? (
                        <div className={'text-group'} key={"content_text"}
                        >
                            <div
                                key={clickIndex} style={{width: '100%'}}
                                ref={(el) => {
                                    textRefs.current[clickIndex] = el
                                    // loadText(book.contents[0].cardId).then()
                                }}>
                                <NewspaperContentText
                                    content={book.contents[0]}
                                />
                            </div>
                        </div>
                    ) : null}
                    {contentTab === 'img' ? (
                        <div className={'new_image-group'} key="content_img">
                            <div
                                key={'content_img_' + clickIndex}
                                ref={(el) => {
                                    imgRefs.current[clickIndex] = el
                                }}>
                                <NewspaperImage
                                    snapshot={image}
                                    index={clickIndex}
                                />
                            </div>
                        </div>
                    ) : null}
                    {contentTab === 'search' ? (
                        <div key="content_search">
                            <Search
                                size="large"
                                placeholder="输入搜索文本"
                                onSearch={searchClick}
                            />
                            <Spin size="large" spinning={searchLoading} style={{minHeight: '400px'}}>
                                <div className={'new_search-group-mid'}>
                                    {searchResult.list.map((contentItem, index) => (
                                        <Card
                                            key={'content_search_' + index}
                                            hoverable
                                            style={{width: '95%', marginTop: '12px'}}
                                        >
                                            <Card.Grid
                                                style={{
                                                    width: '100%',
                                                    padding: '16px',
                                                    paddingBottom: '8px'
                                                }}
                                            >
                                                <Mathdown content={ highlight_text(processContent(contentItem.content),highlights)}></Mathdown>
                                                <div
                                                    onClick={() => clickSearchResult(contentItem.title,contentItem.page,contentItem.id)}
                                                    style={{
                                                        width: '100%',
                                                        padding: '8px',
                                                        textAlign: 'right'
                                                    }}
                                                >
                                                    点击跳转
                                                    <SwapRightOutlined/>
                                                    <Button type="link">

                                                        {contentItem.title} |
                                                        (P{contentItem.page}) {cataloguesNode[contentItem.page-1].title}
                                                    </Button>
                                                </div>
                                            </Card.Grid>

                                        </Card>
                                    ))}
                                </div>
                            </Spin>
                            {searchResult.totals > 0 ?
                                <Pagination
                                    current={ppage}
                                    align={'center'}
                                    hideOnSinglePage
                                    total={searchResult.totals}
                                    showSizeChanger={true}
                                    onChange={paginationOnChange}
                                />
                                : null}
                        </div>
                    ) : null}
                </Col>
            </Row>
        </>
    );
}

export default NewspaperReader