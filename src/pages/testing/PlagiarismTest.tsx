import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, message, Space, Typography } from 'antd';
import { 
    invokePlagiarismCompare, 
    invokeBatchList, 
    invokePlagiarismResults 
} from '../../invoker/plagiarism.ts';
import { PlagiarismCompareParams } from '../../interface/plagiarism.ts';

const { Title, Text } = Typography;

function PlagiarismTest() {
    const [loading, setLoading] = useState(false);
    const [result, setResult] = useState<any>(null);

    const testPlagiarismCompare = async () => {
        setLoading(true);
        try {
            const params: PlagiarismCompareParams = {
                bookIds: ['book1', 'book2', 'book3'],
                batchName: '测试批次',
                description: '这是一个测试批次',
                similarityThreshold: 0.7,
                minSentenceLength: 10,
                enableExactMatch: true,
                enableSimilarMatch: true,
                enablePartialMatch: false
            };

            const batch = await invokePlagiarismCompare(params);
            setResult(batch);
            message.success('查重任务创建成功');
        } catch (error: any) {
            message.error(`测试失败: ${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    const testBatchList = async () => {
        setLoading(true);
        try {
            const batchList = await invokeBatchList({
                pageNo: 0,
                pageSize: 10
            });
            setResult(batchList);
            message.success('获取批次列表成功');
        } catch (error: any) {
            message.error(`测试失败: ${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    const testPlagiarismResults = async () => {
        if (!result || !result.id) {
            message.warning('请先创建一个批次');
            return;
        }

        setLoading(true);
        try {
            const results = await invokePlagiarismResults({
                batchId: result.id,
                pageNo: 0,
                pageSize: 10
            });
            setResult(results);
            message.success('获取查重结果成功');
        } catch (error: any) {
            message.error(`测试失败: ${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div style={{ padding: '20px' }}>
            <Title level={2}>查重功能测试</Title>
            
            <Card title="测试操作" style={{ marginBottom: '20px' }}>
                <Space>
                    <Button 
                        type="primary" 
                        loading={loading}
                        onClick={testPlagiarismCompare}
                    >
                        测试创建查重任务
                    </Button>
                    <Button 
                        loading={loading}
                        onClick={testBatchList}
                    >
                        测试获取批次列表
                    </Button>
                    <Button 
                        loading={loading}
                        onClick={testPlagiarismResults}
                        disabled={!result || !result.id}
                    >
                        测试获取查重结果
                    </Button>
                </Space>
            </Card>

            {result && (
                <Card title="测试结果">
                    <pre style={{ 
                        backgroundColor: '#f5f5f5', 
                        padding: '10px', 
                        borderRadius: '4px',
                        overflow: 'auto',
                        maxHeight: '400px'
                    }}>
                        {JSON.stringify(result, null, 2)}
                    </pre>
                </Card>
            )}
        </div>
    );
}

export default PlagiarismTest;
