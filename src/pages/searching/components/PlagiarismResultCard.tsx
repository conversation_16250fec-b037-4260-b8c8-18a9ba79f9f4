import React, { useState, useEffect } from 'react';
import {
    Card,
    List,
    Tag,
    Progress,
    Select,
    InputNumber,
    Row,
    Col,
    Typography,
    Space,
    Button,
    message,
    Empty,
    Pagination,
    Tooltip,
    Divider
} from 'antd';
import {
    ReloadOutlined,
    FilterOutlined,
    BookOutlined,
    PercentageOutlined
} from '@ant-design/icons';
import {
    SentenceMatch,
    PlagiarismResultParams,
    PlagiarismResult,
    PlagiarismBatch
} from '../../../interface/plagiarism.ts';
import {
    invokePlagiarismResults,
    invokeBatchList
} from '../../../invoker/plagiarism.ts';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

function PlagiarismResultCard() {
    const [selectedBatchId, setSelectedBatchId] = useState<string>('');
    const [availableBatches, setAvailableBatches] = useState<PlagiarismBatch[]>([]);
    const [results, setResults] = useState<SentenceMatch[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [pageNo, setPageNo] = useState<number>(0);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState<number>(0);
    const [batchInfo, setBatchInfo] = useState<PlagiarismBatch | null>(null);
    
    // 筛选条件
    const [minSimilarity, setMinSimilarity] = useState<number | undefined>(undefined);
    const [maxSimilarity, setMaxSimilarity] = useState<number | undefined>(undefined);
    const [matchTypeFilter, setMatchTypeFilter] = useState<string | undefined>(undefined);
    const [sourceBookFilter, setSourceBookFilter] = useState<string | undefined>(undefined);
    const [targetBookFilter, setTargetBookFilter] = useState<string | undefined>(undefined);

    // 获取可用批次列表
    const fetchAvailableBatches = async () => {
        try {
            const result = await invokeBatchList({
                pageNo: 0,
                pageSize: 100,
                status: 'completed'
            });
            setAvailableBatches(result.list);
        } catch (error: any) {
            message.error(`获取批次列表失败：${error.message}`);
        }
    };

    // 获取查重结果
    const fetchResults = async () => {
        if (!selectedBatchId) {
            setResults([]);
            setTotal(0);
            setBatchInfo(null);
            return;
        }

        setLoading(true);
        try {
            const params: PlagiarismResultParams = {
                batchId: selectedBatchId,
                pageNo,
                pageSize,
                minSimilarity,
                maxSimilarity,
                matchType: matchTypeFilter as any,
                sourceBookId: sourceBookFilter,
                targetBookId: targetBookFilter
            };
            const result: PlagiarismResult = await invokePlagiarismResults(params);
            setResults(result.list);
            setTotal(result.totals);
            setBatchInfo(result.batchInfo);
        } catch (error: any) {
            message.error(`获取查重结果失败：${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    // 相似度颜色映射
    const getSimilarityColor = (similarity: number) => {
        if (similarity >= 0.9) return '#ff4d4f'; // 红色 - 极高相似度
        if (similarity >= 0.8) return '#fa8c16'; // 橙色 - 高相似度
        if (similarity >= 0.7) return '#fadb14'; // 黄色 - 中等相似度
        if (similarity >= 0.6) return '#52c41a'; // 绿色 - 低相似度
        return '#1890ff'; // 蓝色 - 很低相似度
    };

    // 匹配类型标签
    const renderMatchTypeTag = (matchType: string) => {
        const typeConfig = {
            exact: { color: 'red', text: '精确匹配' },
            similar: { color: 'orange', text: '相似匹配' },
            partial: { color: 'blue', text: '部分匹配' }
        };
        const config = typeConfig[matchType as keyof typeof typeConfig];
        return config ? <Tag color={config.color}>{config.text}</Tag> : <Tag>{matchType}</Tag>;
    };

    // 高亮显示相似内容
    const highlightSimilarContent = (sourceContent: string, targetContent: string) => {
        // 简单的高亮逻辑，实际可以使用更复杂的文本比较算法
        const words = sourceContent.split('');
        const targetWords = new Set(targetContent.split(''));
        
        return words.map((word, index) => (
            <span
                key={index}
                style={{
                    backgroundColor: targetWords.has(word) ? '#fff2e8' : 'transparent',
                    padding: targetWords.has(word) ? '0 2px' : '0'
                }}
            >
                {word}
            </span>
        ));
    };

    useEffect(() => {
        fetchAvailableBatches();
    }, []);

    useEffect(() => {
        fetchResults();
    }, [selectedBatchId, pageNo, pageSize]);

    // 应用筛选
    const handleApplyFilter = () => {
        setPageNo(0);
        fetchResults();
    };

    // 重置筛选
    const handleResetFilter = () => {
        setMinSimilarity(undefined);
        setMaxSimilarity(undefined);
        setMatchTypeFilter(undefined);
        setSourceBookFilter(undefined);
        setTargetBookFilter(undefined);
        setPageNo(0);
        fetchResults();
    };

    // 获取唯一的书籍列表用于筛选
    const getUniqueBooks = () => {
        const books = new Map<string, string>();
        results.forEach(result => {
            books.set(result.sourceBookId, result.sourceBookName);
            books.set(result.targetBookId, result.targetBookName);
        });
        return Array.from(books.entries()).map(([id, name]) => ({ id, name }));
    };

    return (
        <Card title="查重结果" extra={
            <Button 
                icon={<ReloadOutlined />} 
                onClick={fetchResults}
                loading={loading}
            >
                刷新
            </Button>
        }>
            {/* 批次选择 */}
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
                <Col span={24}>
                    <Space>
                        <Text strong>选择批次：</Text>
                        <Select
                            placeholder="请选择查重批次"
                            style={{ width: 300 }}
                            value={selectedBatchId}
                            onChange={setSelectedBatchId}
                            showSearch
                            filterOption={(input, option) =>
                                (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                            }
                        >
                            {availableBatches.map(batch => (
                                <Option key={batch.id} value={batch.id}>
                                    {batch.name} ({batch.totalMatches} 个匹配)
                                </Option>
                            ))}
                        </Select>
                    </Space>
                </Col>
            </Row>

            {/* 批次信息 */}
            {batchInfo && (
                <Card size="small" style={{ marginBottom: 16 }}>
                    <Row gutter={[16, 8]}>
                        <Col span={6}>
                            <Text strong>批次名称：</Text>{batchInfo.name}
                        </Col>
                        <Col span={6}>
                            <Text strong>书籍数量：</Text>{batchInfo.bookIds.length}
                        </Col>
                        <Col span={6}>
                            <Text strong>总匹配数：</Text>{batchInfo.totalMatches}
                        </Col>
                        <Col span={6}>
                            <Text strong>创建时间：</Text>{new Date(batchInfo.createTime).toLocaleString('zh-CN')}
                        </Col>
                        {batchInfo.description && (
                            <Col span={24}>
                                <Text strong>描述：</Text>{batchInfo.description}
                            </Col>
                        )}
                    </Row>
                </Card>
            )}

            {/* 筛选条件 */}
            <Card size="small" title={<><FilterOutlined /> 筛选条件</>} style={{ marginBottom: 16 }}>
                <Row gutter={[16, 16]}>
                    <Col span={6}>
                        <Text>相似度范围：</Text>
                        <Space>
                            <InputNumber
                                placeholder="最小值"
                                min={0}
                                max={1}
                                step={0.1}
                                value={minSimilarity}
                                onChange={setMinSimilarity}
                                style={{ width: 80 }}
                            />
                            <Text>-</Text>
                            <InputNumber
                                placeholder="最大值"
                                min={0}
                                max={1}
                                step={0.1}
                                value={maxSimilarity}
                                onChange={setMaxSimilarity}
                                style={{ width: 80 }}
                            />
                        </Space>
                    </Col>
                    <Col span={6}>
                        <Text>匹配类型：</Text>
                        <Select
                            placeholder="全部"
                            value={matchTypeFilter}
                            onChange={setMatchTypeFilter}
                            allowClear
                            style={{ width: '100%' }}
                        >
                            <Option value="exact">精确匹配</Option>
                            <Option value="similar">相似匹配</Option>
                            <Option value="partial">部分匹配</Option>
                        </Select>
                    </Col>
                    <Col span={6}>
                        <Text>源书籍：</Text>
                        <Select
                            placeholder="全部"
                            value={sourceBookFilter}
                            onChange={setSourceBookFilter}
                            allowClear
                            style={{ width: '100%' }}
                        >
                            {getUniqueBooks().map(book => (
                                <Option key={book.id} value={book.id}>{book.name}</Option>
                            ))}
                        </Select>
                    </Col>
                    <Col span={6}>
                        <Space>
                            <Button type="primary" onClick={handleApplyFilter}>
                                应用筛选
                            </Button>
                            <Button onClick={handleResetFilter}>
                                重置
                            </Button>
                        </Space>
                    </Col>
                </Row>
            </Card>

            {/* 结果列表 */}
            {!selectedBatchId ? (
                <Empty description="请先选择一个查重批次" />
            ) : (
                <List
                    loading={loading}
                    dataSource={results}
                    renderItem={(item) => (
                        <List.Item>
                            <Card 
                                style={{ width: '100%' }}
                                size="small"
                                title={
                                    <Space>
                                        {renderMatchTypeTag(item.matchType)}
                                        <Tag 
                                            color={getSimilarityColor(item.similarity)}
                                            icon={<PercentageOutlined />}
                                        >
                                            {(item.similarity * 100).toFixed(1)}%
                                        </Tag>
                                    </Space>
                                }
                            >
                                <Row gutter={[16, 16]}>
                                    <Col span={12}>
                                        <Card 
                                            size="small" 
                                            title={
                                                <Space>
                                                    <BookOutlined />
                                                    <Text strong>源书籍</Text>
                                                </Space>
                                            }
                                            style={{ height: '100%' }}
                                        >
                                            <div style={{ marginBottom: 8 }}>
                                                <Text strong>《{item.sourceBookName}》</Text>
                                                <Text type="secondary"> - 第{item.sourcePage}页</Text>
                                            </div>
                                            <Paragraph 
                                                style={{ 
                                                    backgroundColor: '#f6ffed', 
                                                    padding: 8, 
                                                    borderRadius: 4,
                                                    margin: 0
                                                }}
                                            >
                                                {highlightSimilarContent(item.sourceContent, item.targetContent)}
                                            </Paragraph>
                                        </Card>
                                    </Col>
                                    <Col span={12}>
                                        <Card 
                                            size="small" 
                                            title={
                                                <Space>
                                                    <BookOutlined />
                                                    <Text strong>目标书籍</Text>
                                                </Space>
                                            }
                                            style={{ height: '100%' }}
                                        >
                                            <div style={{ marginBottom: 8 }}>
                                                <Text strong>《{item.targetBookName}》</Text>
                                                <Text type="secondary"> - 第{item.targetPage}页</Text>
                                            </div>
                                            <Paragraph 
                                                style={{ 
                                                    backgroundColor: '#fff2e8', 
                                                    padding: 8, 
                                                    borderRadius: 4,
                                                    margin: 0
                                                }}
                                            >
                                                {highlightSimilarContent(item.targetContent, item.sourceContent)}
                                            </Paragraph>
                                        </Card>
                                    </Col>
                                </Row>
                                <Divider style={{ margin: '8px 0' }} />
                                <Text type="secondary" style={{ fontSize: '12px' }}>
                                    匹配时间：{new Date(item.createTime).toLocaleString('zh-CN')}
                                </Text>
                            </Card>
                        </List.Item>
                    )}
                    pagination={{
                        current: pageNo + 1,
                        pageSize,
                        total,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条匹配`,
                        onChange: (page, size) => {
                            setPageNo(page - 1);
                            setPageSize(size || 10);
                        }
                    }}
                />
            )}
        </Card>
    );
}

export default PlagiarismResultCard;
