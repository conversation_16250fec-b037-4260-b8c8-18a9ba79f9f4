# Plagiarism Detection Database Persistence Implementation

## Overview

This implementation adds comprehensive database persistence for plagiarism detection batches and results, replacing the previous in-memory-only storage with a hybrid approach that combines SurrealDB persistence with memory caching for optimal performance.

## Architecture

### Database Models

#### PlagiarismBatchDb (`src-tauri/src/models/plagiarism.rs`)
- **Purpose**: Database representation of plagiarism detection batches
- **Key Fields**:
  - `batch_id`: Unique snowflake ID for the batch
  - `name`: Human-readable batch name
  - `description`: Optional batch description
  - `book_ids`: JSON string containing array of book IDs
  - `total_matches`: Number of matches found
  - `status`: Current batch status (pending, processing, completed, failed)
  - `create_time`, `update_time`, `completed_time`: Timestamps
  - `progress`: Optional progress percentage

#### SentenceMatchDb (`src-tauri/src/models/plagiarism.rs`)
- **Purpose**: Database representation of individual sentence matches
- **Key Fields**:
  - `match_id`: Unique snowflake ID for the match
  - `batch_id`: Reference to parent batch
  - `source_book_id`, `target_book_id`: Book identifiers
  - `source_content`, `target_content`: Matched text content
  - `similarity`: Similarity score (0.0 to 1.0)
  - `match_type`: Type of match (exact, similar, partial)
  - `create_time`: When the match was found

### Database Repositories

#### PlagiarismBatchRepository (`src-tauri/src/database/plagiarism_batch.rs`)
- **CRUD Operations**: Create, read, update, delete batches
- **Advanced Queries**: Paginated filtering by status and keywords
- **Status Management**: Update batch status and progress
- **Indexes**: Optimized for batch_id (unique) and status/createTime (normal)

#### SentenceMatchRepository (`src-tauri/src/database/sentence_match.rs`)
- **Batch Operations**: Save multiple matches efficiently
- **Advanced Filtering**: Database-level filtering by similarity, match type, book IDs
- **Pagination**: Efficient large result set handling
- **Statistics**: Calculate batch statistics from database
- **Indexes**: Optimized for match_id (unique) and batch_id/similarity/matchType (normal)

### Hybrid Storage Strategy

The implementation uses a hybrid approach:

1. **Database as Source of Truth**: All data is persisted to SurrealDB
2. **Memory Cache for Performance**: Frequently accessed data cached in memory
3. **Graceful Fallback**: If database operations fail, system falls back to memory cache
4. **Lazy Loading**: Data loaded from database when not in cache

## Key Features

### 1. Persistent Batch Management
- Batches survive application restarts
- Full audit trail with timestamps
- Status tracking throughout lifecycle
- Progress monitoring for long-running operations

### 2. Efficient Result Storage
- Batch insertion of sentence matches
- Database-level filtering reduces memory usage
- Pagination support for large result sets
- Optimized queries with proper indexing

### 3. Advanced Filtering
- **Similarity Range**: Filter by min/max similarity scores
- **Match Type**: Filter by exact, similar, or partial matches
- **Book Selection**: Filter by source or target book IDs
- **Status Filtering**: Filter batches by processing status
- **Keyword Search**: Search batch names and descriptions

### 4. Performance Optimizations
- **Database Indexes**: Strategic indexing for common query patterns
- **Memory Caching**: Hot data kept in memory for fast access
- **Batch Operations**: Efficient bulk operations for large datasets
- **Lazy Loading**: Data loaded only when needed

## Database Schema

### Tables Created

#### `plagiarism_batch`
```sql
-- Unique index on batchId for fast lookups
DEFINE INDEX plagiarism_batch_batchId_unique_index ON TABLE plagiarism_batch FIELDS batchId UNIQUE;

-- Normal index on status and createTime for filtering and sorting
DEFINE INDEX plagiarism_batch_status_createTime_normal_index ON TABLE plagiarism_batch FIELDS status, createTime;
```

#### `sentence_match`
```sql
-- Unique index on matchId for fast lookups
DEFINE INDEX sentence_match_matchId_unique_index ON TABLE sentence_match FIELDS matchId UNIQUE;

-- Normal index on batchId, similarity, and matchType for filtering
DEFINE INDEX sentence_match_batchId_similarity_matchType_normal_index ON TABLE sentence_match FIELDS batchId, similarity, matchType;
```

## API Changes

### Enhanced Commands

#### `plagiarism_results`
- Now uses database-level filtering instead of in-memory filtering
- Supports efficient pagination for large result sets
- Better performance with proper indexing

#### `plagiarism_batch_list`
- Database-backed pagination and filtering
- Keyword search across batch names and descriptions
- Status-based filtering

#### `plagiarism_batch_statistics`
- Real-time statistics calculated from database
- Accurate counts and averages
- Performance optimized with single query

## Usage Examples

### Creating a Batch
```rust
let batch_id = batch_manager.create_batch(
    "My Plagiarism Check".to_string(),
    Some("Checking books for similarities".to_string()),
    vec!["book1".to_string(), "book2".to_string()],
).await;
```

### Adding Results
```rust
batch_manager.add_matches(&batch_id, sentence_matches).await;
```

### Querying with Filters
```rust
let (matches, total) = SentenceMatchRepository::find_by_batch_id_paginated(
    batch_id_u64,
    page_no,
    page_size,
    Some(0.8), // min_similarity
    Some(1.0), // max_similarity
    Some("exact"), // match_type
    Some("book1"), // source_book_id
    None, // target_book_id
).await?;
```

## Migration and Compatibility

### Backward Compatibility
- Existing in-memory operations continue to work
- Gradual migration to database-backed operations
- Fallback mechanisms ensure system stability

### Data Migration
- New installations automatically use database persistence
- Existing data can be migrated by running comparison operations
- No breaking changes to existing APIs

## Performance Considerations

### Database Optimization
- Strategic indexing for common query patterns
- Efficient pagination with LIMIT/START clauses
- Batch operations for bulk data insertion

### Memory Management
- Hybrid caching reduces database load
- Lazy loading prevents memory bloat
- Automatic cleanup of completed batches

### Scalability
- Database handles large result sets efficiently
- Pagination prevents memory exhaustion
- Indexed queries scale with data size

## Error Handling

### Graceful Degradation
- Database failures fall back to memory cache
- Partial failures don't break entire operations
- Comprehensive error logging for debugging

### Recovery Mechanisms
- Automatic retry for transient database errors
- Data consistency checks on startup
- Manual recovery tools for edge cases

## Testing

Comprehensive test suite covers:
- Batch creation and persistence
- Sentence match storage and retrieval
- Filtering and pagination
- Error handling and recovery
- Performance under load

## Future Enhancements

### Planned Features
- Automatic batch cleanup for old data
- Export/import functionality for batch data
- Advanced analytics and reporting
- Real-time progress updates via WebSocket

### Performance Improvements
- Query optimization based on usage patterns
- Caching strategies for frequently accessed data
- Background processing for large operations
- Distributed processing support
