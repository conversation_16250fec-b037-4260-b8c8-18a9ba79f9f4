# TypeScript 与 Actix-web 实体属性命名统一方案

## 概述

本文档描述了如何统一 TypeScript 前端与 Rust Actix-web 后端之间的实体属性命名约定，确保数据传输的一致性和开发体验的统一性。

## 命名约定

### 1. 基本原则

- **Rust 后端**: 使用 `snake_case` 命名约定（符合 Rust 官方规范）
- **TypeScript 前端**: 使用 `camelCase` 命名约定（符合 JavaScript/TypeScript 规范）
- **自动转换**: 通过 serde 的 `rename_all` 属性实现自动转换

### 2. Serde 配置

#### 2.1 使用 `rename_all = "camelCase"`

对于所有需要与前端交互的 Rust 结构体，添加以下属性：

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ExampleStruct {
    pub user_name: String,        // 序列化为 "userName"
    pub email_address: String,    // 序列化为 "emailAddress"
    pub created_at: String,       // 序列化为 "createdAt"
    pub is_active: bool,          // 序列化为 "isActive"
}
```

#### 2.2 特殊情况处理

对于需要特殊命名的字段，使用 `#[serde(rename = "...")]`：

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SpecialStruct {
    pub user_id: String,
    #[serde(rename = "ID")]
    pub internal_id: String,      // 序列化为 "ID"
    #[serde(rename = "type")]
    pub item_type: String,        // 序列化为 "type" (避免 TypeScript 保留字冲突)
}
```

### 3. TypeScript 接口定义

TypeScript 接口应使用 camelCase，与 Rust 序列化后的格式保持一致：

```typescript
interface ExampleInterface {
    userName: string;
    emailAddress: string;
    createdAt: string;
    isActive: boolean;
}
```

## 实施步骤

### 步骤 1: 更新现有 Rust 结构体

为所有与前端交互的结构体添加 `#[serde(rename_all = "camelCase")]`：

1. **plagiarism.rs** - ✅ 已完成
   - `SentenceMatch`
   - `PlagiarismBatch`
   - `PlagiarismCompareParams`
   - `PlagiarismResultParams`
   - `PlagiarismResult`
   - `BatchListParams`
   - `BatchListResult`
   - `BatchStatistics`

2. **其他模块** - 待处理
   - `book.rs`
   - `card.rs`
   - `catalogue.rs`
   - 等等...

### 步骤 2: 验证 TypeScript 接口

确保 TypeScript 接口与 Rust 结构体序列化后的格式一致。

### 步骤 3: 测试数据传输

运行测试确保前后端数据传输正常。

## 最佳实践

### 1. 新增结构体规范

创建新的与前端交互的结构体时，必须：

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct NewStruct {
    // 字段定义...
}
```

### 2. 字段命名规范

- **Rust**: 使用描述性的 snake_case 名称
- **避免缩写**: 使用完整单词而非缩写
- **保持一致性**: 相同概念在不同结构体中使用相同命名

### 3. 可选字段处理

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OptionalFieldsStruct {
    pub required_field: String,
    pub optional_field: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub conditional_field: Option<String>,  // 为 None 时不序列化
}
```

### 4. 日期时间处理

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DateTimeStruct {
    #[serde(with = "chrono::serde::ts_milliseconds")]
    pub created_at: DateTime<Utc>,  // 序列化为毫秒时间戳
    
    pub updated_at: String,         // 或使用字符串格式
}
```

## 工具和验证

### 1. 自动化测试

创建单元测试验证序列化/反序列化：

```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_serialization() {
        let example = ExampleStruct {
            user_name: "test".to_string(),
            email_address: "<EMAIL>".to_string(),
            created_at: "2024-01-01".to_string(),
            is_active: true,
        };
        
        let json = serde_json::to_string(&example).unwrap();
        assert!(json.contains("userName"));
        assert!(json.contains("emailAddress"));
        assert!(json.contains("createdAt"));
        assert!(json.contains("isActive"));
    }
}
```

### 2. TypeScript 类型生成

考虑使用工具自动生成 TypeScript 类型定义：

- `ts-rs`: 从 Rust 结构体生成 TypeScript 类型
- `typeshare`: 跨语言类型共享

## 迁移计划

### 阶段 1: 核心模块 (当前)
- ✅ plagiarism 模块

### 阶段 2: 主要业务模块
- book 模块
- card 模块
- catalogue 模块

### 阶段 3: 辅助模块
- dictionary 模块
- maps 模块
- law 模块

### 阶段 4: 系统模块
- activation 模块
- system_feature 模块

## 注意事项

1. **向后兼容性**: 确保更改不会破坏现有 API
2. **测试覆盖**: 每次更改后运行完整测试套件
3. **文档更新**: 及时更新 API 文档
4. **团队沟通**: 确保团队成员了解新的命名约定

## 相关资源

- [Serde 官方文档](https://serde.rs/)
- [Rust 命名约定](https://rust-lang.github.io/api-guidelines/naming.html)
- [TypeScript 编码规范](https://typescript-eslint.io/rules/)
