# 查重功能页面重构说明

## 更新概述

根据您的要求，我已经将"查重对比"功能从标签页移动到了一个独立的新页面。这样做的好处是：

1. **更好的用户体验** - 查重功能有独立的页面空间，不会与搜索功能混在一起
2. **更清晰的导航** - 用户可以通过侧边栏直接访问查重功能
3. **更好的功能组织** - 每个功能模块都有自己的专用页面

## 主要变更

### 1. 新增独立查重页面

**文件**: `src/pages/plagiarism/PlagiarismPage.tsx`

**功能**:
- 完整的查重功能界面
- 三个主要标签页：
  - 查重设置 - 配置查重参数和选择书籍
  - 批次管理 - 管理所有查重批次
  - 查重结果 - 查看具体的查重结果
- 自动加载可用书籍列表
- 返回搜索和刷新书籍按钮

### 2. 路由配置更新

**文件**: `src/main.tsx`
- 添加了 `/plagiarism` 路由
- 导入了新的 `PlagiarismPage` 组件

### 3. 侧边栏导航更新

**文件**: `src/App.tsx`
- 在侧边栏菜单中添加了"查重对比"选项
- 使用 `BookOutlined` 图标
- 位置在"全文检索"之后

### 4. 全局搜索页面简化

**文件**: `src/pages/searching/GlobalSearching.tsx`
- 移除了查重对比标签页
- 保留了浮动按钮功能
- 简化了页面结构，专注于搜索功能

### 5. 浮动按钮增强

**文件**: `src/pages/searching/components/PlagiarismFloatButton.tsx`
- 启动查重后自动跳转到查重页面
- 提供更好的用户引导

## 用户使用流程

### 方式一：从搜索页面开始
1. 在"全文检索"页面搜索书籍
2. 点击书籍下方的"加入查重"按钮选择书籍
3. 点击右下角的浮动按钮管理选中的书籍
4. 配置查重参数并启动查重
5. 自动跳转到查重页面查看进度和结果

### 方式二：直接访问查重页面
1. 点击侧边栏的"查重对比"菜单
2. 在查重设置页面选择要对比的书籍
3. 配置参数并启动查重
4. 在批次管理和查重结果页面查看进度和结果

## 页面结构

```
查重对比页面 (/plagiarism)
├── 页面头部
│   ├── 标题和描述
│   ├── 返回搜索按钮
│   └── 刷新书籍按钮
├── 查重设置标签页
│   ├── 书籍选择
│   ├── 参数配置
│   └── 启动查重
├── 批次管理标签页
│   ├── 批次列表
│   ├── 进度跟踪
│   └── 批次操作
└── 查重结果标签页
    ├── 结果筛选
    ├── 匹配展示
    └── 统计信息
```

## 技术特点

1. **独立页面** - 完全独立的查重功能页面
2. **自动数据加载** - 页面加载时自动获取可用书籍
3. **智能导航** - 启动查重后自动跳转到结果页面
4. **状态保持** - 使用Context保持选中书籍状态
5. **响应式设计** - 适配不同屏幕尺寸

## 优势

1. **功能分离** - 搜索和查重功能完全分离，各司其职
2. **用户体验** - 更清晰的功能导航和操作流程
3. **扩展性** - 独立页面便于后续功能扩展
4. **维护性** - 代码结构更清晰，便于维护

现在用户可以通过两种方式使用查重功能：
- 从搜索页面选择书籍后启动查重
- 直接访问查重页面进行操作

这样的设计既保持了功能的完整性，又提供了更好的用户体验。
