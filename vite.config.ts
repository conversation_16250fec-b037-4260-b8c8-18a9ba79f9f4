import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from 'path';
import prismjs from 'vite-plugin-prismjs';

// https://vitejs.dev/config/
export default defineConfig(async () => ({
    plugins: [
        react(),
        prismjs({
            languages: 'all',
        }),
    ],
    define: {"process.env": {}},
    resolve: {
        alias: {
            '@editor': path.resolve(__dirname, 'src/editor-src'),
            '@public': path.resolve(__dirname, 'public')
        }
    },
    build: {
        outDir: './src-tauri/resources/dist',
    },
    // Vite options tailored for Tauri development and only applied in `tauri dev` or `tauri build`
    //
    // 1. prevent vite from obscuring rust errors
    clearScreen: false,
    // 2. tauri expects a fixed port, fail if that port is not available
    server: {
        port: 1420,
        strictPort: true,
    }
}));