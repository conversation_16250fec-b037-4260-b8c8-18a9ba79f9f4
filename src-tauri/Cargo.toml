[package]
name = "book-guard"
version = "1.0.0"
description = "Pre-Publish Book Dup Check App"
authors = ["you"]
license = ""
repository = ""
edition = "2021"

# [lib]
# name = "app_lib"
# crate-type = ["staticlib", "cdylib", "rlib"]
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = ["protocol-asset", "devtools"] }
serde = { version = "1.0.217", features = ["derive"] }
serde_json = "1.0.137"
walkdir = "2.5"
zip = "2"
once_cell = "1.20"
chrono = { version = "0.4.31", features = ["serde"] }
regex = "1.10.2"
anyhow = "1.0.86"
serde_derive = "1.0.188"
lazy_static = { version = "1.5.0", features = [] }
tokio = { version = "1.33.0", features = ["full"] }
tokio-tungstenite = "0.26"
log = "0.4.20"
bincode = { version = "1.3.3", features = ["i128"] }
surrealdb = { version = "2.1", features = ["kv-rocksdb"] }
tantivy = "0.22.0"
tantivy-jieba = "0.11.0"
rand = "0.8"
reqwest = { version = "0.12", features = ["json", "multipart"] }
tauri-plugin-dialog = "2"
tauri-plugin-notification = "2"
tauri-plugin-shell = "2"
tauri-plugin-fs = "2"
tauri-plugin-http = "2"
machine-uid = "0.5.3"
indexmap = "2.7.0"
actix = "0.13"
actix-http = "3.9"
actix-web = "4"
actix-files = "0.6"
actix-ws = "0.3"
serde_urlencoded = "0.7.1"
uuid = { version = "1", features = ["v4"] }
futures-util = "0.3.31"
tauri-plugin-process = "2.2"
tauri-plugin-opener = "2.2"
tauri-plugin-upload = "2.2"
human-panic = "2.0"
fuzzy-matcher = "0.3"
similar = "2.7"
strsim = "0.11"
[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

#[profile.dev]
#incremental = true # Compile your binary in smaller steps.
#codegen-units = 32 # Default is 16, but you can tweak this for your use case
#
#[profile.release]
#incremental = true
#codegen-units = 1  # Allows LLVM to perform better optimization.
#lto = true         # Enables link-time-optimizations.
#opt-level = "s"    # Prioritizes small binary size. Use `3` if you prefer speed.
#panic = "abort"    # Higher performance by disabling panic handlers.
#strip = true       # Ensures debug symbols are removed.
