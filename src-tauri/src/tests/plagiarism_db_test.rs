#[cfg(test)]
mod tests {
    use super::*;
    use crate::database::plagiarism_batch::PlagiarismBatchRepository;
    use crate::database::sentence_match::SentenceMatchRepository;
    use crate::models::plagiarism::{PlagiarismBatchDb, SentenceMatchDb};
    use crate::services::plagiarism::GLOBAL_PLAGIARISM_DETECTOR;

    #[tokio::test]
    async fn test_batch_creation_and_persistence() {
        // Test creating a batch and verifying it's saved to database
        let batch_manager = GLOBAL_PLAGIARISM_DETECTOR.get_batch_manager();
        
        let batch_id = batch_manager.create_batch(
            "Test Batch".to_string(),
            Some("Test Description".to_string()),
            vec!["book1".to_string(), "book2".to_string()],
        ).await;
        
        // Verify batch exists in memory
        let batch = batch_manager.get_batch(&batch_id).await;
        assert!(batch.is_some());
        
        let batch = batch.unwrap();
        assert_eq!(batch.name, "Test Batch");
        assert_eq!(batch.description, Some("Test Description".to_string()));
        assert_eq!(batch.book_ids, vec!["book1".to_string(), "book2".to_string()]);
        assert_eq!(batch.status, "pending");
        
        // Verify batch exists in database
        let batch_id_u64: u64 = batch_id.parse().unwrap();
        let db_batch = PlagiarismBatchRepository::find_by_batch_id(batch_id_u64).await.unwrap();
        assert!(db_batch.is_some());
        
        let db_batch = db_batch.unwrap();
        assert_eq!(db_batch.name, "Test Batch");
        assert_eq!(db_batch.description, Some("Test Description".to_string()));
        assert_eq!(db_batch.get_book_ids(), vec!["book1".to_string(), "book2".to_string()]);
        assert_eq!(db_batch.status, "pending");
    }

    #[tokio::test]
    async fn test_sentence_match_persistence() {
        // Test saving sentence matches to database
        let batch_id_u64 = 12345u64;
        
        let sentence_match = SentenceMatchDb::new(
            batch_id_u64,
            "book1".to_string(),
            "Book One".to_string(),
            1,
            "This is a test sentence.".to_string(),
            "book2".to_string(),
            "Book Two".to_string(),
            2,
            "This is a test sentence.".to_string(),
            0.95,
            "exact".to_string(),
        );
        
        // Save to database
        let result = SentenceMatchRepository::save(&sentence_match).await;
        assert!(result.is_ok());
        
        // Query back from database
        let (matches, total) = SentenceMatchRepository::find_by_batch_id_paginated(
            batch_id_u64,
            1,
            10,
            None, None, None, None, None
        ).await.unwrap();
        
        assert_eq!(total, 1);
        assert_eq!(matches.len(), 1);
        
        let retrieved_match = &matches[0];
        assert_eq!(retrieved_match.batch_id, batch_id_u64);
        assert_eq!(retrieved_match.source_book_id, "book1");
        assert_eq!(retrieved_match.target_book_id, "book2");
        assert_eq!(retrieved_match.similarity, 0.95);
        assert_eq!(retrieved_match.match_type, "exact");
    }

    #[tokio::test]
    async fn test_batch_filtering() {
        // Test database-level filtering for batches
        let (batches, total) = PlagiarismBatchRepository::find_batches_paginated(
            1,
            10,
            Some("pending"),
            Some("Test"),
        ).await.unwrap();
        
        // Should return batches that match the filter criteria
        for batch in &batches {
            assert_eq!(batch.status, "pending");
            assert!(batch.name.contains("Test") || 
                   batch.description.as_ref().map_or(false, |d| d.contains("Test")));
        }
    }

    #[tokio::test]
    async fn test_sentence_match_filtering() {
        // Test database-level filtering for sentence matches
        let batch_id_u64 = 12345u64;
        
        let (matches, total) = SentenceMatchRepository::find_by_batch_id_paginated(
            batch_id_u64,
            1,
            10,
            Some(0.9), // min_similarity
            Some(1.0), // max_similarity
            Some("exact"), // match_type
            Some("book1"), // source_book_id
            None, // target_book_id
        ).await.unwrap();
        
        // Verify filtering works
        for match_item in &matches {
            assert!(match_item.similarity >= 0.9);
            assert!(match_item.similarity <= 1.0);
            assert_eq!(match_item.match_type, "exact");
            assert_eq!(match_item.source_book_id, "book1");
        }
    }
}
