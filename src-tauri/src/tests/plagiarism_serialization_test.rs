#[cfg(test)]
mod plagiarism_serialization_tests {
    use crate::commands::plagiarism::*;
    use serde_json;

    #[test]
    fn test_plagiarism_compare_params_serialization() {
        let params = PlagiarismCompareParams {
            book_ids: vec!["book1".to_string(), "book2".to_string()],
            batch_name: "测试批次".to_string(),
            description: Some("测试描述".to_string()),
            similarity_threshold: 0.8,
            min_sentence_length: 10,
            enable_exact_match: true,
            enable_similar_match: true,
            enable_partial_match: false,
        };

        let json = serde_json::to_string(&params).unwrap();
        println!("PlagiarismCompareParams JSON: {}", json);

        // 验证字段名是否为 camelCase
        assert!(json.contains("\"bookIds\""));
        assert!(json.contains("\"batchName\""));
        assert!(json.contains("\"similarityThreshold\""));
        assert!(json.contains("\"minSentenceLength\""));
        assert!(json.contains("\"enableExactMatch\""));
        assert!(json.contains("\"enableSimilarMatch\""));
        assert!(json.contains("\"enablePartialMatch\""));

        // 验证不包含 snake_case 字段名
        assert!(!json.contains("\"book_ids\""));
        assert!(!json.contains("\"batch_name\""));
        assert!(!json.contains("\"similarity_threshold\""));
    }

    #[test]
    fn test_plagiarism_compare_params_deserialization() {
        let json = r#"{
            "bookIds": ["book1", "book2"],
            "batchName": "测试批次",
            "description": "测试描述",
            "similarityThreshold": 0.8,
            "minSentenceLength": 10,
            "enableExactMatch": true,
            "enableSimilarMatch": true,
            "enablePartialMatch": false
        }"#;

        let params: PlagiarismCompareParams = serde_json::from_str(json).unwrap();
        
        assert_eq!(params.book_ids, vec!["book1", "book2"]);
        assert_eq!(params.batch_name, "测试批次");
        assert_eq!(params.description, Some("测试描述".to_string()));
        assert_eq!(params.similarity_threshold, 0.8);
        assert_eq!(params.min_sentence_length, 10);
        assert_eq!(params.enable_exact_match, true);
        assert_eq!(params.enable_similar_match, true);
        assert_eq!(params.enable_partial_match, false);
    }

    #[test]
    fn test_sentence_match_serialization() {
        let sentence_match = SentenceMatch {
            id: "match1".to_string(),
            source_book_id: "book1".to_string(),
            source_book_name: "源书籍".to_string(),
            source_page: 1,
            source_content: "源内容".to_string(),
            target_book_id: "book2".to_string(),
            target_book_name: "目标书籍".to_string(),
            target_page: 2,
            target_content: "目标内容".to_string(),
            similarity: 0.95,
            match_type: "exact".to_string(),
            create_time: "2024-01-01T00:00:00Z".to_string(),
        };

        let json = serde_json::to_string(&sentence_match).unwrap();
        println!("SentenceMatch JSON: {}", json);

        // 验证字段名是否为 camelCase
        assert!(json.contains("\"sourceBookId\""));
        assert!(json.contains("\"sourceBookName\""));
        assert!(json.contains("\"sourcePage\""));
        assert!(json.contains("\"sourceContent\""));
        assert!(json.contains("\"targetBookId\""));
        assert!(json.contains("\"targetBookName\""));
        assert!(json.contains("\"targetPage\""));
        assert!(json.contains("\"targetContent\""));
        assert!(json.contains("\"matchType\""));
        assert!(json.contains("\"createTime\""));

        // 验证不包含 snake_case 字段名
        assert!(!json.contains("\"source_book_id\""));
        assert!(!json.contains("\"match_type\""));
        assert!(!json.contains("\"create_time\""));
    }

    #[test]
    fn test_plagiarism_batch_serialization() {
        let batch = PlagiarismBatch {
            id: "batch1".to_string(),
            name: "测试批次".to_string(),
            description: Some("批次描述".to_string()),
            book_ids: vec!["book1".to_string(), "book2".to_string()],
            total_matches: 100,
            status: "completed".to_string(),
            create_time: "2024-01-01T00:00:00Z".to_string(),
            update_time: "2024-01-01T01:00:00Z".to_string(),
            completed_time: Some("2024-01-01T02:00:00Z".to_string()),
            progress: Some(100),
        };

        let json = serde_json::to_string(&batch).unwrap();
        println!("PlagiarismBatch JSON: {}", json);

        // 验证字段名是否为 camelCase
        assert!(json.contains("\"bookIds\""));
        assert!(json.contains("\"totalMatches\""));
        assert!(json.contains("\"createTime\""));
        assert!(json.contains("\"updateTime\""));
        assert!(json.contains("\"completedTime\""));

        // 验证不包含 snake_case 字段名
        assert!(!json.contains("\"book_ids\""));
        assert!(!json.contains("\"total_matches\""));
        assert!(!json.contains("\"create_time\""));
    }

    #[test]
    fn test_plagiarism_result_params_serialization() {
        let params = PlagiarismResultParams {
            batch_id: "batch1".to_string(),
            page_no: 1,
            page_size: 20,
            min_similarity: Some(0.5),
            max_similarity: Some(1.0),
            match_type: Some("exact".to_string()),
            source_book_id: Some("book1".to_string()),
            target_book_id: Some("book2".to_string()),
        };

        let json = serde_json::to_string(&params).unwrap();
        println!("PlagiarismResultParams JSON: {}", json);

        // 验证字段名是否为 camelCase
        assert!(json.contains("\"batchId\""));
        assert!(json.contains("\"pageNo\""));
        assert!(json.contains("\"pageSize\""));
        assert!(json.contains("\"minSimilarity\""));
        assert!(json.contains("\"maxSimilarity\""));
        assert!(json.contains("\"matchType\""));
        assert!(json.contains("\"sourceBookId\""));
        assert!(json.contains("\"targetBookId\""));

        // 验证不包含 snake_case 字段名
        assert!(!json.contains("\"batch_id\""));
        assert!(!json.contains("\"page_no\""));
        assert!(!json.contains("\"source_book_id\""));
    }

    #[test]
    fn test_batch_statistics_serialization() {
        let stats = BatchStatistics {
            batch_id: "batch1".to_string(),
            total_books: 5,
            total_sentences: 1000,
            total_matches: 50,
            exact_matches: 20,
            similar_matches: 25,
            partial_matches: 5,
            average_similarity: 0.75,
            high_similarity_matches: 15,
            medium_similarity_matches: 25,
            low_similarity_matches: 10,
        };

        let json = serde_json::to_string(&stats).unwrap();
        println!("BatchStatistics JSON: {}", json);

        // 验证字段名是否为 camelCase
        assert!(json.contains("\"batchId\""));
        assert!(json.contains("\"totalBooks\""));
        assert!(json.contains("\"totalSentences\""));
        assert!(json.contains("\"totalMatches\""));
        assert!(json.contains("\"exactMatches\""));
        assert!(json.contains("\"similarMatches\""));
        assert!(json.contains("\"partialMatches\""));
        assert!(json.contains("\"averageSimilarity\""));
        assert!(json.contains("\"highSimilarityMatches\""));
        assert!(json.contains("\"mediumSimilarityMatches\""));
        assert!(json.contains("\"lowSimilarityMatches\""));

        // 验证不包含 snake_case 字段名
        assert!(!json.contains("\"batch_id\""));
        assert!(!json.contains("\"total_books\""));
        assert!(!json.contains("\"exact_matches\""));
    }

    #[test]
    fn test_round_trip_serialization() {
        // 测试完整的序列化-反序列化循环
        let original_params = PlagiarismCompareParams {
            book_ids: vec!["book1".to_string(), "book2".to_string()],
            batch_name: "测试批次".to_string(),
            description: Some("测试描述".to_string()),
            similarity_threshold: 0.8,
            min_sentence_length: 10,
            enable_exact_match: true,
            enable_similar_match: true,
            enable_partial_match: false,
        };

        // 序列化
        let json = serde_json::to_string(&original_params).unwrap();
        
        // 反序列化
        let deserialized_params: PlagiarismCompareParams = serde_json::from_str(&json).unwrap();

        // 验证数据一致性
        assert_eq!(original_params.book_ids, deserialized_params.book_ids);
        assert_eq!(original_params.batch_name, deserialized_params.batch_name);
        assert_eq!(original_params.description, deserialized_params.description);
        assert_eq!(original_params.similarity_threshold, deserialized_params.similarity_threshold);
        assert_eq!(original_params.min_sentence_length, deserialized_params.min_sentence_length);
        assert_eq!(original_params.enable_exact_match, deserialized_params.enable_exact_match);
        assert_eq!(original_params.enable_similar_match, deserialized_params.enable_similar_match);
        assert_eq!(original_params.enable_partial_match, deserialized_params.enable_partial_match);
    }
}
