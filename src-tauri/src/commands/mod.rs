use reqwest::Url;
use serde::de::DeserializeOwned;
use serde::Serialize;
use crate::models::ResponseVO;
use crate::services::system_feature::{SystemFeature, UsageMode};

pub mod activate;
pub mod system_feature;
pub mod increment;
pub mod searching;
pub mod plagiarism;
pub mod book;
pub mod assemble;
pub mod filter_data;
pub mod catalogue;
pub mod card;
pub mod dictionary;
pub mod maps;
pub mod file_url;
pub mod inc_file_upd;
pub mod law;
pub mod backup_recover;

/// 获取远程配置并构建基础 URL
async fn build_base_url<T>(path: &str) -> Result<Url, ResponseVO<T>>
where
    T: DeserializeOwned + Serialize,
{
    let usage_mode: UsageMode = SystemFeature::get_sys_feat_owned()
        .unwrap_or_default();
    let remote_url = usage_mode.url;
    // 尝试解析 URL
    let url = match Url::parse(&*remote_url) {
        Ok(mut url) => {
            url.set_path(&*("/api/".to_owned() + path));
            url
        }
        Err(err) => {
            log::error!("Failed to parse URL: {}", err);
            return Err(ResponseVO::error(Some(format!("URL 解析失败: {}", err))));
        }
    };

    Ok(url)
}

/// 处理响应
async fn handle_response<T>(response: Result<reqwest::Response, reqwest::Error>) -> ResponseVO<T>
where
    T: DeserializeOwned + Serialize,
{
    match response {
        Ok(resp) => {
            if resp.status().is_success() {
                // 读取响应体
                let body = match resp.text().await {
                    Ok(body) => body,
                    Err(err) => {
                        log::error!("Failed to read response text: {}", err);
                        return ResponseVO::server_error();
                    }
                };
                // 反序列化响应体
                serde_json::from_str::<ResponseVO<T>>(&body).unwrap_or_else(|err| {
                    log::error!("Failed to deserialize response: {}", err);
                    ResponseVO::server_error()
                })
            } else {
                let status = resp.status();

                log::error!("Request failed with status: {}", status);
                // 读取错误响应体
                let error_body = resp.text().await.unwrap_or_else(|err| {
                    log::error!("Failed to read error response text: {}", err);
                    "Unknown error".to_string()
                });
                serde_json::from_str::<ResponseVO<T>>(&error_body).unwrap_or_else(|err| {
                    log::error!("Failed to deserialize response: {}", err);
                    ResponseVO::server_error()
                })
            }
        }
        Err(err) => {
            log::error!("Request error: {}", err);
            ResponseVO::server_error()
        }
    }
}

/// 发送 GET 请求
pub async fn get_request_to_remote<T, E>(path: &str, params: Option<E>) -> ResponseVO<T>
where
    T: DeserializeOwned + Serialize,
    E: Serialize,
{
    let mut url = match build_base_url(path).await {
        Ok(url) => url,
        Err(err) => return err, // 直接返回错误 ResponseVO
    };

    log::info!("Sending GET request to: {}", url);

    // 添加查询参数
    if let Some(params) = params {
        let query_result = serde_urlencoded::to_string(params).map_err(|err| {
            log::error!("Failed to serialize query parameters: {}", err);
            ResponseVO::error(Some(format!(
                "Failed to serialize query parameters: {}",
                err
            )))
        });
        let query = match query_result {
            Ok(query) => query,
            Err(err) => return err,
        };
        url.set_query(Some(&query));
    }

    // 创建 Client 并设置 header
    let client = reqwest::Client::new();
    let response = client
        .get(url)
        .send()
        .await;

    handle_response(response).await
}

/// 发送 POST 请求
pub async fn post_request_to_remote<T, E>(path: &str, body: Option<E>) -> ResponseVO<T>
where
    T: DeserializeOwned + Serialize,
    E: Serialize,
{
    let url = match build_base_url(path).await {
        Ok(url) => url,
        Err(err) => return err, // 直接返回错误 ResponseVO
    };

    log::info!("Sending POST request to: {}", url);

    let client = reqwest::Client::new();

    let request_builder = client
        .post(url);

    // 添加请求体
    let response = if let Some(body) = body {
        request_builder.json(&body).send().await
    } else {
        request_builder.send().await
    };
    handle_response(response).await
}

pub async fn delete_request_to_remote<T, E>(path: &str, body: Option<E>) -> ResponseVO<T>
where
    T: DeserializeOwned + Serialize,
    E: Serialize,
{
    let url = match build_base_url(path).await {
        Ok(url) => url,
        Err(err) => return err, // 直接返回错误 ResponseVO
    };

    log::info!("Sending POST request to: {}", url);

    let client = reqwest::Client::new();

    let request_builder = client
        .delete(url);

    // 添加请求体
    let response = if let Some(body) = body {
        request_builder.json(&body).send().await
    } else {
        request_builder.send().await
    };
    handle_response(response).await
}
