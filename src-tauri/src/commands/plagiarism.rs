use crate::models::ResponseVO;
use crate::wrapper::command_wrapper::{forward, RequestMethod};
use crate::services::plagiarism::{GLOBAL_PLAGIARISM_DETECTOR};
use serde::{Deserialize, Serialize};

// 句子匹配结果
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SentenceMatch {
    pub id: String,
    pub source_book_id: String,
    pub source_book_name: String,
    pub source_page: i32,
    pub source_content: String,
    pub target_book_id: String,
    pub target_book_name: String,
    pub target_page: i32,
    pub target_content: String,
    pub similarity: f64,
    pub match_type: String, // "exact", "similar", "partial"
    pub create_time: String,
}

// 查重批次
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PlagiarismBatch {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub book_ids: Vec<String>,
    pub total_matches: i32,
    pub status: String, // "pending", "processing", "completed", "failed"
    pub create_time: String,
    pub update_time: String,
    pub completed_time: Option<String>,
    pub progress: Option<i32>,
}

// 查重对比参数
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PlagiarismCompareParams {
    pub book_ids: Vec<String>,
    pub batch_name: String,
    pub description: Option<String>,
    pub similarity_threshold: f64,
    pub min_sentence_length: i32,
    pub enable_exact_match: bool,
    pub enable_similar_match: bool,
    pub enable_partial_match: bool,
}

// 查重结果查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PlagiarismResultParams {
    pub batch_id: String,
    pub page_no: i32,
    pub page_size: i32,
    pub min_similarity: Option<f64>,
    pub max_similarity: Option<f64>,
    pub match_type: Option<String>,
    pub source_book_id: Option<String>,
    pub target_book_id: Option<String>,
}

// 分页查重结果
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PlagiarismResult {
    pub totals: i32,
    pub page_no: i32,
    pub page_count: i32,
    pub page_size: i32,
    pub list: Vec<SentenceMatch>,
    pub batch_info: PlagiarismBatch,
}

// 批次列表查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BatchListParams {
    pub page_no: i32,
    pub page_size: i32,
    pub status: Option<String>,
    pub keyword: Option<String>,
}

// 分页批次结果
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BatchListResult {
    pub totals: i32,
    pub page_no: i32,
    pub page_count: i32,
    pub page_size: i32,
    pub list: Vec<PlagiarismBatch>,
}

// 批次统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BatchStatistics {
    pub batch_id: String,
    pub total_books: i32,
    pub total_sentences: i32,
    pub total_matches: i32,
    pub exact_matches: i32,
    pub similar_matches: i32,
    pub partial_matches: i32,
    pub average_similarity: f64,
    pub high_similarity_matches: i32,
    pub medium_similarity_matches: i32,
    pub low_similarity_matches: i32,
}

// 开始查重对比
#[tauri::command]
pub async fn plagiarism_compare(
    params: PlagiarismCompareParams
) -> Result<ResponseVO<PlagiarismBatch>, ResponseVO<()>> {
    forward(RequestMethod::Post, "plagiarism/compare", Some(params.clone()), || async {
        // 使用全局查重检测器
        match GLOBAL_PLAGIARISM_DETECTOR.start_comparison(params.clone()).await {
            Ok(batch_id) => {
                // 获取创建的批次信息
                if let Some(batch) = GLOBAL_PLAGIARISM_DETECTOR.get_batch_manager().get_batch(&batch_id).await {
                    Ok(ResponseVO::success(Some(batch), None))
                } else {
                    Err(ResponseVO::error(Some("无法获取批次信息".to_string())))
                }
            }
            Err(error) => Err(ResponseVO::error(Some(error)))
        }
    }).await
}

// 获取查重结果
#[tauri::command]
pub async fn plagiarism_results(
    params: PlagiarismResultParams
) -> Result<ResponseVO<PlagiarismResult>, ResponseVO<()>> {
    forward(RequestMethod::Post, "plagiarism/results", Some(params.clone()), || async {
        use crate::database::sentence_match::SentenceMatchRepository;

        let batch_manager = GLOBAL_PLAGIARISM_DETECTOR.get_batch_manager();

        // 获取批次信息
        if let Some(batch) = batch_manager.get_batch(&params.batch_id).await {
            // 解析批次ID
            let batch_id_u64 = match params.batch_id.parse::<u64>() {
                Ok(id) => id,
                Err(_) => {
                    return Err(ResponseVO::error(Some("无效的批次ID".to_string())));
                }
            };

            // 直接从数据库获取带筛选条件的分页结果
            match SentenceMatchRepository::find_by_batch_id_paginated(
                batch_id_u64,
                params.page_no,
                params.page_size,
                params.min_similarity,
                params.max_similarity,
                params.match_type.as_deref(),
                params.source_book_id.as_deref(),
                params.target_book_id.as_deref(),
            ).await {
                Ok((db_matches, total)) => {
                    let matches: Vec<SentenceMatch> = db_matches.into_iter().map(|m| m.into()).collect();

                    let page_count = if params.page_size > 0 {
                        (total + params.page_size - 1) / params.page_size
                    } else {
                        1
                    };

                    let result = PlagiarismResult {
                        totals: total,
                        page_no: params.page_no,
                        page_count,
                        page_size: params.page_size,
                        list: matches,
                        batch_info: batch,
                    };

                    Ok(ResponseVO::success(Some(result), None))
                }
                Err(e) => {
                    eprintln!("从数据库获取查重结果失败: {}", e);
                    Err(ResponseVO::error(Some("获取查重结果失败".to_string())))
                }
            }
        } else {
            Err(ResponseVO::error(Some("批次不存在".to_string())))
        }
    }).await
}

// 获取批次列表
#[tauri::command]
pub async fn plagiarism_batch_list(
    params: BatchListParams
) -> Result<ResponseVO<BatchListResult>, ResponseVO<()>> {
    forward(RequestMethod::Post, "plagiarism/batches", Some(params.clone()), || async {
        let batch_manager = GLOBAL_PLAGIARISM_DETECTOR.get_batch_manager();

        let (batches, total) = batch_manager.get_batch_list(
            params.page_no,
            params.page_size,
            params.status.as_deref(),
            params.keyword.as_deref(),
        ).await;

        let page_count = if params.page_size > 0 {
            (total + params.page_size - 1) / params.page_size
        } else {
            1
        };

        let result = BatchListResult {
            totals: total,
            page_no: params.page_no,
            page_count,
            page_size: params.page_size,
            list: batches,
        };

        Ok(ResponseVO::success(Some(result), None))
    }).await
}

// 获取批次详情
#[tauri::command]
pub async fn plagiarism_batch_detail(
    batch_id: String
) -> Result<ResponseVO<PlagiarismBatch>, ResponseVO<()>> {
    forward(RequestMethod::Get, &format!("plagiarism/batch/{}", batch_id), None::<()>, || async {
        let batch_manager = GLOBAL_PLAGIARISM_DETECTOR.get_batch_manager();

        if let Some(batch) = batch_manager.get_batch(&batch_id).await {
            Ok(ResponseVO::success(Some(batch), None))
        } else {
            Err(ResponseVO::error(Some("批次不存在".to_string())))
        }
    }).await
}

// 获取批次统计信息
#[tauri::command]
pub async fn plagiarism_batch_statistics(
    batch_id: String
) -> Result<ResponseVO<BatchStatistics>, ResponseVO<()>> {
    forward(RequestMethod::Get, &format!("plagiarism/batch/{}/statistics", batch_id), None::<()>, || async {
        let batch_manager = GLOBAL_PLAGIARISM_DETECTOR.get_batch_manager();

        if let Some(statistics) = batch_manager.get_batch_statistics(&batch_id).await {
            Ok(ResponseVO::success(Some(statistics), None))
        } else {
            Err(ResponseVO::error(Some("无法获取批次统计信息".to_string())))
        }
    }).await
}

// 删除批次
#[tauri::command]
pub async fn plagiarism_delete_batch(
    batch_id: String
) -> Result<ResponseVO<bool>, ResponseVO<()>> {
    forward(RequestMethod::Delete, &format!("plagiarism/batch/{}", batch_id), None::<()>, || async {
        let batch_manager = GLOBAL_PLAGIARISM_DETECTOR.get_batch_manager();
        let success = batch_manager.delete_batch(&batch_id).await;
        Ok(ResponseVO::success(Some(success), None))
    }).await
}

// 取消正在进行的查重任务
#[tauri::command]
pub async fn plagiarism_cancel_comparison(
    batch_id: String
) -> Result<ResponseVO<bool>, ResponseVO<()>> {
    forward(RequestMethod::Post, &format!("plagiarism/batch/{}/cancel", batch_id), None::<()>, || async {
        let batch_manager = GLOBAL_PLAGIARISM_DETECTOR.get_batch_manager();
        let success = batch_manager.cancel_task(&batch_id).await;
        Ok(ResponseVO::success(Some(success), None))
    }).await
}
