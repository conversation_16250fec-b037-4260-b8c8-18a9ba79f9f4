fn clean_text(line: &str) -> String {
    // Assuming strQ2B converts full-width characters to half-width.
    // This is a placeholder as the actual implementation is unknown.
    let mut line = str_q2b(line);

    line = line.trim().to_lowercase();

    // Replace newline characters with spaces
    line = line.replace("\n", " ");

    // Remove extra spaces
    line = line.split_whitespace().collect::<Vec<&str>>().join(" ");

    // Remove underscores
    line = line.replace("_", "");

    // Remove HTML tags (placeholder, actual implementation might differ)
    line = remove_html_tag(&line);

    // Replace numbers with '@'
    let re = regex::Regex::new(r"\d+(\.\d*)?").unwrap();
    line = re.replace_all(&line, "@").to_string();

    line
}

fn str_q2b(ustring: &str) -> String {
    let mut rstring = String::new();
    for uchar in ustring.chars() {
        let mut inside_code = uchar as u32;
        if inside_code == 12288 {
            inside_code = 32;
        } else if inside_code >= 65281 && inside_code <= 65374 {
            inside_code -= 65248;
        }
        rstring.push(char::from_u32(inside_code).unwrap_or_default());
    }
    rstring
}

fn remove_html_tag(line: &str) -> String {
    let tags = [
        "<sub.*?>", "</sub>",
        "<em.*?>", "</em>",
        "<span.*?>", "</span>",
        "<img.*?>",
        "<strong.*?>", "</strong>",
        "<sup.*?>", "</sup>",
        "<div.*?>", "</div>"
    ];

    let mut result = line.to_string();
    for tag in &tags {
        let re = regex::Regex::new(tag).unwrap();
        result = re.replace_all(&result, "").to_string();
    }

    result
}
