use std::collections::{HashMap, HashSet};
use std::path::PathBuf;
use std::time::Instant;

// use hdf5::File;
use log::error;
use mongodb::Database;
use nalgebra;
use ndarray::{Array1, Array2};

use crate::app::config::Config;
use crate::app::lib::numer_util;
use crate::app::lib::numer_util::norm_vector;
use crate::app::ml::retrieval::retrieval_data::RetrievalData;
use crate::app::models::dataloader::question_loader::QuestionLoader;
use crate::app::service::paper_filter::{FilterItem, PaperFilter};

pub struct InvertedIndex {
    // InvertedIndex 的实现
    inference: bool,
    n: f64,
    avg_l: f64,
    mm: HashMap<String, Vec<(u64, usize, usize)>>,
    levedis_dicts: HashMap<u64, String>,
    data_loader: QuestionLoader,
    stop_words: HashSet<String>,
    postings_lists: HashMap<String, Vec<String>>,
    // 假设键和值都是字符串类型
    config_path: String,
    config_encoding: String,
    words_set: HashMap<String, String>,
    // 假设键和值都是字符串类型
    filter_words: Vec<char>,
    root: PathBuf,
    feats_path: PathBuf,
    k1: f64,
    k2: f64,
    b: f64,
    paper_filter: Option<PaperFilter>,
    inverted_h5: Option<File>,
    inverted_map: Option<HashMap<String, Vec<(u64, u32, u32)>>>,
    inverted_mode_h5: bool,
    feats_h5_path: PathBuf,
    db: Database,
}

// impl InvertedIndex {
//     pub(crate) fn new(stop_words: HashSet<String>, root: PathBuf, data_loader: QuestionLoader, db: Database) -> Self {
//         let filter_words: Vec<char> = "abcdefghijklmnopqrstuvwxyz".chars().collect();
//         let mut combined_stop_words = stop_words;
//         for word in filter_words.clone() {
//             combined_stop_words.insert(word.to_string());
//         }
//
//         let feats_path = root.join("feats_map.pickle");
//         let feats_h5_path = root.join("feats_map.h5");
//         let config = Config::new(".env.yml");
//         let inverted_mode_h5 = config.get_inverted_mode_h5_conf();
//         InvertedIndex {
//             stop_words: combined_stop_words,
//             postings_lists: HashMap::new(),
//             config_path: String::new(),
//             config_encoding: String::new(),
//             words_set: HashMap::new(),
//             filter_words,
//             data_loader,
//             root,
//             feats_path,
//             k1: 0.0,
//             k2: 0.0,
//             b: 0.0,
//             inference: false,
//             n: 0.0,
//             avg_l: 0.0,
//             mm: HashMap::new(),
//             paper_filter: None,
//             levedis_dicts: Default::default(),
//             inverted_h5: None,
//             inverted_map: None,
//             inverted_mode_h5,
//             feats_h5_path,
//             db,
//         }
//     }
//
//     pub(crate) fn construct_postings_lists(&self, func: fn(&str, &HashSet<String>) -> HashMap<String, u64>) {
//         // levedis
//     }
//
//     pub async fn search_for_query(&self, query_text: &str, gen_features_func: fn(&str, &HashSet<String>) -> HashMap<String, u64>, normalization: bool, mut filter_item: Option<FilterItem>, order_type: u8) -> (Vec<(u32, f64, u32, i32, u32)>, Vec<String>) {
//         // 搜索实现
//         if filter_item.is_none() {
//             filter_item = Some(FilterItem::new(0.3));
//         }
//         let feat = gen_features_func(query_text, &self.stop_words);
//         let mut hits: Vec<String> = feat.keys().map(|k| k.clone()).collect();
//         hits.sort_by(|a, b| b.len().cmp(&a.len()));
//
//         let mut ranking_scores: HashMap<u32, f64> = HashMap::new();
//         let mut union_scores: HashMap<u32, f64> = HashMap::new();
//         let mut tfidf_dict = HashMap::new();
//
//
//         for (term, value) in feat.iter() {
//             let items = self.get_feat_by_key(term);
//             let df = items.shape()[0];
//
//             if df == 0 {
//                 continue;
//             }
//             let w = f64::log2((self.n - df as f64 + 0.5) / (df as f64 + 0.5));
//             tfidf_dict.insert(term.clone(), *value as f64 * w);
//
//             let f_names: Array1<usize> = items.column(0).to_owned();
//             let tf_np: Array1<f64> = items.column(1).mapv(|x| x as f64);
//             let ld_np: Array1<f64> = items.column(2).mapv(|x| x as f64);
//
//             let k_np = self.k1 * (1.0 - self.b + self.b * ld_np / self.avg_l);
//             let s_np = (((self.k1 + 1.0) * &tf_np) / (&tf_np + &k_np)) * ((*value as f64 * (self.k2 + 1.0)) / (*value as f64 + self.k2));
//
//             for i in 0..f_names.len() {
//                 let qid = f_names[i] as u32;
//                 let ranking_score = ranking_scores.entry(qid.into()).or_insert(0.0);
//                 *ranking_score += s_np[i] * w;
//
//                 let union_score = union_scores.entry(qid.into()).or_insert(0.0);
//                 *union_score += f64::min(tf_np[i], *value as f64) * w;
//             }
//         }
//
//         if ranking_scores.is_empty() {
//             return (Vec::new(), Vec::new()); // Adjust this return value as per your needs
//         }
//
//         let total_token = tfidf_dict.values().sum::<f64>();
//         for v in union_scores.values_mut() {
//             *v /= total_token;
//             if *v > 1.0 {
//                 *v = 1.0;
//             }
//         }
//
//         let qids: Vec<_> = ranking_scores.keys().cloned().collect();
//         let mut values: Vec<_> = ranking_scores.values().cloned().collect();
//
//         if normalization {
//             values = numer_util::normalization(&values);
//         }
//
//         let max_index = values.iter().enumerate().max_by(|a, b| a.1.partial_cmp(b.1).unwrap()).map(|(i, _)| i).unwrap_or(0);
//         let top_key = qids[max_index];
//         let top_text = self.data_loader.get(top_key, self.db.clone()).await.unwrap_or_default();
//         let top_feat = gen_features_func(&top_text, &self.stop_words);
//         let rescale = self.vsm(&feat, &top_feat, true);
//         let union_weight = 0.8;
//         for (i, value) in values.iter_mut().enumerate() {
//             let key = qids[i];
//             *value = (1.0 - union_weight) * *value * rescale + union_weight * union_scores[&key];
//         }
//
//         let mut ranking_scores = Vec::new();
//
//         for (i, &value) in values.iter().enumerate() {
//             let question_id = qids.get(i).unwrap().clone().into();
//             let item = self.paper_filter.as_ref().unwrap().get_question(&filter_item.as_ref().unwrap(), question_id, value);
//             if let Some(item_val) = item {
//                 ranking_scores.push(item_val);
//             }
//         }
//
//         // Sorting based on order_type
//         match order_type {
//             1 => ranking_scores.sort_by(|a, b|
//                 b.2.cmp(&a.2).then(b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal))),
//             2 => ranking_scores.sort_by(|a, b| b.3.cmp(&a.3).then(b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal))),
//             3 => ranking_scores.sort_by(|a, b|
//                 b.4.cmp(&a.4).then(b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal))),
//             _ => ranking_scores.sort_by(|a, b|
//                 b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal)),
//         };
//         return (ranking_scores, hits);
//     }
//
//     pub fn get_feat_by_key(&self, mut key: &str) -> Array2<usize> {
//         if !self.inverted_mode_h5 {
//             if let Some(inverted_map) = &self.inverted_map {
//                 return match inverted_map.get(key) {
//                     Some(data) => Array2::from_shape_vec((data.len(), 3),
//                                                          data.clone().into_iter().map(|(a, b, c)| vec![a as usize, b as usize, (c as usize).try_into().unwrap()]).flatten().collect()).unwrap(),
//                     None => Array2::zeros((0, 3)),
//                 };
//             } else {
//                 return Array2::zeros((0, 3));
//             }
//         }
//
//         let key_string = key.replace("/", " ");
//         let key = key_string.as_str();
//         if let Some(h5_file) = &self.inverted_h5 {
//             if let Ok(dataset) = h5_file.dataset(key) {
//                 let data: Array2<usize> = dataset.read().unwrap();
//                 return data;
//             }
//         }
//
//         Array2::zeros((0, 3))
//     }
//
//     fn vsm(&self, t1_tokens_dict: &HashMap<String, u64>, t2_tokens_dict: &HashMap<String, u64>, with_weight: bool) -> f64 {
//         let t1_len: u64 = t1_tokens_dict.values().sum();
//         let t2_len: u64 = t2_tokens_dict.values().sum();
//
//         let mut tokens_set = t1_tokens_dict.keys().cloned().collect::<Vec<_>>();
//         tokens_set.extend(t2_tokens_dict.keys().cloned());
//         tokens_set.sort();
//         tokens_set.dedup();
//
//         let set_len = tokens_set.len();
//         let mut vec1 = vec![0.0; set_len];
//         let mut vec2 = vec![0.0; set_len];
//
//         for i in 0..set_len {
//             let idf = 1.0;
//             let tf1 = if with_weight {
//                 *t1_tokens_dict.get(&tokens_set[i]).unwrap_or(&0) as f64 / t1_len as f64
//             } else {
//                 if t1_tokens_dict.contains_key(&tokens_set[i]) { 1.0 } else { 0.0 }
//             };
//             let tf2 = if with_weight {
//                 *t2_tokens_dict.get(&tokens_set[i]).unwrap_or(&0) as f64 / t2_len as f64
//             } else {
//                 if t2_tokens_dict.contains_key(&tokens_set[i]) { 1.0 } else { 0.0 }
//             };
//
//             vec1[i] = tf1 * idf;
//             vec2[i] = tf2 * idf;
//         }
//
//         let d_vector1 = nalgebra::DVector::from_vec(vec1.clone());
//         let d_vector2 = nalgebra::DVector::from_vec(vec2.clone());
//         let norm1 = norm_vector(&vec1);
//         let norm2 = norm_vector(&vec2);
//         let dot_product = d_vector1.dot(&d_vector2);
//         let similarity = dot_product / (norm1 * norm2);
//         similarity
//     }
//
//
//     pub(crate) fn similar_question(&self, question: &str, func: fn(&str, &HashSet<String>) -> HashMap<String, u64>, filter_item: Option<&str>) -> Vec<String> {
//         // 相似问题的实现
//         Vec::new()
//     }
//
//     pub async fn load_param(&mut self) {
//         if self.inference {
//             return;
//         }
//         self.inference = true;
//
//         let start = Instant::now();
//         let retrieval_data = RetrievalData::new(self.feats_path.clone()).unwrap();
//         if !self.inverted_mode_h5 {
//             let (inverted_map, n, avg_l) = retrieval_data.load_inverted_map();
//             self.inverted_map = Some(inverted_map);
//             self.n = n;
//             self.avg_l = avg_l;
//         } else {
//             if !self.feats_h5_path.exists() {
//                 retrieval_data.store_to_h5();
//             }
//             match File::open(&self.feats_h5_path) {
//                 Ok(file) => {
//                     self.inverted_h5 = Some(file);
//                     let data = self.inverted_h5.as_ref().unwrap().dataset("qc_ld").unwrap().read_1d().unwrap();
//                     // Assuming `data` is a tuple or array, adjust as needed
//                     self.n = data[0];
//                     self.avg_l = data[1];
//                 }
//                 Err(_) => {
//                     error!("Error while reading HDF5 file");
//                     retrieval_data.store_to_h5();
//                     let file = File::open(&self.feats_h5_path).unwrap();
//                     self.inverted_h5 = Some(file);
//                     let data = self.inverted_h5.as_ref().unwrap().dataset("qc_ld").unwrap().read_1d().unwrap();
//                     self.n = data[0];
//                     self.avg_l = data[1];
//                 }
//             }
//         }
//         self.k1 = 1.5;
//         self.k2 = 0.0;
//         self.b = 0.75;
//
//         let pickle_root = self.root.clone();
//         let start = Instant::now();
//         self.paper_filter = Option::from(PaperFilter::new(true, pickle_root, self.db.clone()).await);
//         println!("PaperFilter::new elapse {:?}", start.elapsed());
//     }
// }