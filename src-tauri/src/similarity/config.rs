use std::collections::HashMap;
use std::env;
use std::fs::File;
use std::path::Path;

pub struct Config {
    env_path: String,
    params: HashMap<String, String>,
    user_params: HashMap<String, String>,
}

impl Config {
    pub fn new(env_path: &str) -> Self {
        let mut params = HashMap::new();
        params.insert("download_uri".to_string(), env::var("DOWNLOAD_URI").unwrap_or("http://************:8826/export/dup-check/download".to_string()));
        params.insert("mongo_uri".to_string(), env::var("MONGO_URI").unwrap_or("mongodb://localhost:27017/dup3".to_string()));
        params.insert("redis_uri".to_string(), env::var("REDIS_URI").unwrap_or("redis://localhost:6379".to_string()));
        params.insert("threads".to_string(), env::var("THREADS").unwrap_or_else(|_| num_cpus::get().to_string()));
        params.insert("auth".to_string(), env::var("AUTH").unwrap_or(true.to_string()));
        params.insert("clean_logs_hint".to_string(), env::var("CLEAN_LOGS_HINT").unwrap_or("0".to_string()));
        params.insert("env_type".to_string(), env::var("ENV_TYPE").unwrap_or("eec".to_string()));
        params.insert("mono_subject".to_string(), env::var("MONO_SUBJECT").unwrap_or(true.to_string()));
        params.insert("highlight".to_string(), env::var("HIGHLIGHT").unwrap_or(true.to_string()));
        params.insert("show_score".to_string(), env::var("SHOW_SCORE").unwrap_or(true.to_string()));
        params.insert("run_processes".to_string(), env::var("RUN_PROCESSES").unwrap_or(1.to_string()));
        params.insert("enable_export".to_string(), env::var("ENABLE_EXPORT").unwrap_or(true.to_string()));
        params.insert("D2T_TMP_DIR".to_string(), env::var("D2T_TMP_DIR").unwrap_or("/tmp/test".to_string()));
        params.insert("D2T_HOME".to_string(), env::var("D2T_HOME").unwrap_or("/mnt/d/Tools/docx2tex".to_string()));
        params.insert("CD_KEY".to_string(), env::var("CD_KEY").unwrap_or("".to_string()));
        params.insert("activation".to_string(), env::var("activation").unwrap_or("".to_string()));
        params.insert("inverted_mode_h5".to_string(), env::var("INVERTED_MODE_H5").unwrap_or("1".to_string()));
        params.insert("crypt_key".to_string(), env::var("CRYPT_KEY").unwrap_or("9474C16170E05CF97AD6928B51D4913C158172DB6743873F2BD1761997BBF2E6801A1E4607FA0916711186044D21CAC0FB60F6B0341345F58935E9589603BEA6205DE50C8FAA17B3DD98B9E8A757BF6EE26C00EF78A9533E4065D92A24AF417DA282490E2868BDFEE310EE9C0F7385EB507C0D4C3DCC6F93AB08773BC3B548B818D525B2DA448A9A4A4799C7B1EA05E7C8A555C4B7834BA490E4522C62D8067B142F016B0ABC3ADFED39D3F337D71FCF8DCD3695C956A05B30F823F0E188A8695A794ECB0B754F63292EAE9FF49DAD9B847FECA17E26F7BA42336A27CE3132A338D2FC54F1FDACC2FFB412DCB6221D66C65F1C59C58ED0DE029E5E1B648C6D2D".to_string()));

        let user_params: HashMap<String,String> = if Path::new(&env_path).exists() {
            let file = File::open(&env_path).expect("Unable to open file");
            serde_yaml::from_reader(file).expect("Error parsing the yml")
        } else {
            HashMap::new()
        };

        for (k, v) in &user_params {
            params.insert(k.clone(), v.clone());
        }

        Self {
            env_path: env_path.to_string(),
            params,
            user_params: Default::default(),
        }
    }
    pub fn get_inverted_mode_h5_conf(&self) -> bool {
        let binding = "1".to_string();
        let ret = self.params.get("inverted_mode_h5").unwrap_or(&binding);
        return ret != "0";
    }
}
