use mongodb::bson::doc;
use mongodb::Database;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct Region {
    code: String,
    pub(crate) name: String
}

impl Region {
    fn new(code: String) -> Self {
        Self { code, name: "".to_string() }
    }

    pub async fn get_by_code(code: String, database:Database) -> Option<Region> {
        let region_collection = database.collection("region");
        let region = region_collection.find_one(doc! { "code": code}, None).await.unwrap();
        region
    }

}