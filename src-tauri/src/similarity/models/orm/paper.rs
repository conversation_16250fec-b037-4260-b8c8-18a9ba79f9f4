use std::collections::HashSet;
use chrono::prelude::*;
use mongodb::bson::doc;
use mongodb::Database;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct Paper {
    #[serde(rename="_id")]
    pub(crate) id: u32,
    #[serde(rename="paperName")]
    pub(crate) paper_name: String,
    pub(crate) batch: Option<u32>,
    pub(crate) year: Option<u32>,
    #[serde(rename="paperTypeCode")]
    pub(crate) paper_type_code: Option<String>,
    #[serde(rename="regionStack")]
    pub(crate) region_stack: Option<HashSet<String>>,
    #[serde(rename="bookId")]
    pub book_id: u32,
    #[serde(rename="subjectCode")]
    pub(crate) subject_code: u32,
    #[serde(rename="regionCode")]
    pub(crate) region_code: Option<String>,
    #[serde(rename="schoolYear")]
    pub(crate) school_year: u32,
    #[serde(rename="publishedOn",with = "mongodb::bson::serde_helpers::chrono_datetime_as_bson_datetime")]
    pub published_on: DateTime<Utc>,
    #[serde(rename="questionNum")]
    pub(crate) question_num: u32
}

impl Paper {
    pub async fn get_by_id(id: u32, database:Database) -> Option<Paper> {
        let paper_collection = database.collection("paper");
        let paper = paper_collection.find_one(doc! { "_id": id }, None).await.unwrap();
        paper
    }
}