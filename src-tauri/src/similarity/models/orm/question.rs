use std::vec::Vec;

use mongodb::bson::{doc, Document};
use mongodb::Database;
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize, Default, Debug)]
pub struct Question {
    #[serde(rename = "_id")]
    pub id: u32,
    pub question_type_code: Option<String>,
    pub stem: Option<String>,
    pub choices_list: Option<Vec<String>>,
    pub material: Option<String>,
    pub difficulty: Option<f64>,
    pub answer: Option<String>,
    pub analysis: Option<String>,
    pub subject_code: Option<String>,
    pub sub_questions: Option<Vec<SubQuestion>>,
    // #[serde(rename = "updated_at")]
    // pub updated_at: DateTime<Utc>,
    #[serde(rename = "knowledgeIds")]
    pub knowledge_ids: Option<Vec<u32>>,
    #[serde(rename = "paperIds")]
    pub paper_ids: Vec<u32>,
    pub batch: Option<i32>,
}

impl Question {
    pub async fn get_by_id(qid: u32, database: Database) -> Option<Question> {
        let question_collection = database.collection("question");
        let question = question_collection.find_one(doc! { "_id": qid }, None).await.unwrap();
        question
    }
    pub fn output_for_mode(&self, mode: &str) -> String {
        match mode {
            "material" => self.material.clone().unwrap_or_default().clone(),
            "stem" => {
                let mut res = String::new();
                if self.stem.is_some() {
                    res += &self.stem.clone().unwrap();
                }
                res.push('\n');
                if let Some(sub_questions) = &self.sub_questions {
                    for sub_question in sub_questions {
                        let mut ss = String::new();
                        ss += &sub_question.stem;
                        ss.push('\n');
                        let choice_str = show_question_choices(sub_question.choices_list.as_ref());
                        if !choice_str.is_empty() {
                            ss += "\n";
                            ss += &choice_str;
                        }
                        res += &ss;
                        res.push('\n');
                    }
                }
                res
            }
            "analog" => {
                let mut res = String::new();
                if let Some(answer) = &self.answer {
                    res += answer;
                    res.push('\n');
                }
                if let Some(analysis) = &self.analysis {
                    res += analysis;
                    res.push('\n');
                }
                res
            }
            _ => self.output(),
        }
    }

    pub fn output(&self) -> String {
        let mut res = String::new();

        if let Some(material) = &self.material {
            res += material;
            res.push('\n');
        }
        if self.stem.is_some() {
            res += &self.stem.clone().unwrap()
        }

        res.push('\n');
        if let Some(options) = &self.choices_list {
            let choice_str = show_question_choices(options);
            res.push('\n');
            res += &choice_str;
        };

        if let Some(sub_questions) = &self.sub_questions {
            for sub_question in sub_questions {
                let mut ss = String::new();
                ss += &sub_question.stem;
                ss.push('\n');
                let sub_choice_str = show_question_choices(sub_question.choices_list.as_ref());
                if !sub_choice_str.is_empty() {
                    ss.push('\n');
                    ss += &sub_choice_str;
                }
                res += &ss;
                res.push('\n');
            }
        }
        res
    }
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SubQuestion {
    pub question_type_code: Option<i32>,
    pub stem: String,
    pub choices_list: Vec<String>,
    material: String,
}

impl SubQuestion {
    pub fn to_bson(&self) -> Document {
        let question_type_code = self.question_type_code.unwrap_or_default();
        doc! {
            "questionTypeCode": question_type_code,
            "stem": &self.stem,
            "choicesList": &self.choices_list
        }
    }
}

pub fn show_question_choices(choices: &Vec<String>) -> String {
    let mut res = String::new();
    let pre = b'A';
    for (i, choice) in choices.iter().enumerate() {
        res.push((pre + i as u8) as char);
        res.push_str(". ");
        res.push_str(choice);
        res.push('\n');
    }
    res
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Catalog {
    pub catalog_id: Option<i64>,
    pub code: String,
    pub text_page: i32,
}

impl Catalog {
    pub fn to_bson(&self) -> Document {
        let catalog_id = self.catalog_id.unwrap_or_default();
        doc! {
            "catalogId": catalog_id,
            "code": &self.code,
            "textPage": self.text_page
        }
    }
}


