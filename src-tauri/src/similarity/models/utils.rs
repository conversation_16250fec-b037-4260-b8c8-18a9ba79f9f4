use regex::Regex;

pub fn filter_img_latex(text: &str, highlights: Vec<String>) -> String {
    let img_latex_pattern = r#"<img[^>]*?src=["|']?(.*?)[\"|']?(?:\s.*?)?/?>|\$\$(.+?)\$\$|\$(.+?)\$|\\\[\\\]|\\\(.+?\\\)"#;

    let re = Regex::new(img_latex_pattern).unwrap();

    // Assuming this function returns a bool for simplicity
    let get_highlight_conf = || -> bool {
        // Dummy implementation
        true
    };

    if !get_highlight_conf() {
        return text.to_string();
    }

    let mut content = String::new();
    let mut last_match_end = 0;

    for cap in re.find_iter(text) {
        content.push_str(&highlight(&text[last_match_end..cap.start()], highlights.clone()));
        content.push_str(cap.as_str());
        last_match_end = cap.end();
    }

    content.push_str(&highlight(&text[last_match_end..], highlights));
    content
}

fn highlight(src: &str, kws: Vec<String>) -> String {
    if src.is_empty() || kws.is_empty() {
        return src.to_string();
    }
    let kw = &kws[0];
    let dists: Vec<String> = src.split(kw).map(|dist| highlight(dist, kws[1..].to_vec())).collect();
    let hh = format!("<span style='color:#f60;'>{}</span>", kw);
    dists.join(&hh)
}