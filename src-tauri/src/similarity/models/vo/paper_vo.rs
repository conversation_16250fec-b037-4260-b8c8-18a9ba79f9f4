use mongodb::Database;
use serde_derive::{Deserialize, Serialize};
use crate::app::models::constant_proxy::ConstantProxy;
use crate::app::models::orm::book::Book;
use crate::app::models::orm::paper::Paper;

#[derive(Debug,Serialize,Deserialize)]
pub struct PaperVo {
    name: String,
    pub(crate) id: u32,
    batch: Option<u32>,
    book: PaperBookVo,
    subject: String,
    year: u32,
    published_on: String,
    region: String,
    paper_type: String,
    question_num: u32,
    // If `detail` is true.
    questions: Option<Vec<String>>,
}


#[derive(Debug,Serialize,Deserialize)]
pub struct PaperBookVo {
    name: String,
    pub id: u32,
}
impl PaperVo {
    pub async fn from_paper(database: Database, paper: &Paper, detail: bool) -> Self {
        let book = Book::get_by_id(paper.book_id, database.clone()).await.unwrap();  // Assuming you have a method to get a Book by id.
        let book_vo = PaperBookVo {
            id: book.id,
            name: book.name.clone(),
        };

        let subject = ConstantProxy::get_subject_name(paper.subject_code.clone(),database.clone()).await.unwrap();
        let region = ConstantProxy::get_region_name(paper.region_code.clone(),database.clone()).await;
        let paper_type = ConstantProxy::get_paper_type(paper.paper_type_code.clone().unwrap().as_str(),database).await;

        let questions = if detail {
            // Fetch questions from paper and transform into the required format.
            // This is just a stub.
            Some(vec!["Q1. Sample Question".to_string()])
        } else {
            None
        };

        PaperVo {
            name: paper.paper_name.clone(),
            id: paper.id,
            batch: paper.batch,
            book: book_vo,
            subject,
            year: paper.school_year,
            // published_on: paper.published_on.format("%Y-%m-%d").to_string(),
            published_on: "".to_string(),
            region,
            paper_type,
            question_num: paper.question_num,
            questions,
        }
    }
}