use futures::future::join_all;
use mongodb::Database;
use serde::{Deserialize, Serialize};

use crate::app::models::orm::paper::Paper;
use crate::app::models::orm::question::Question;

#[derive(Debug, Serialize, Deserialize)]
pub struct QuestionVo {
    id: u32,
    stem: String,
    material: Option<String>,
    answer: Option<String>,
    analysis: Option<String>,
    choices_list: Vec<String>,
    sub_questions: Vec<SubQuestionVo>,
    papers: Vec<QuestionPaperVo>,
    pub(crate) content: String,
    pub(crate) score: f64,
    pub(crate) prefix: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QuestionPaperVo {
    id: u32,
    name: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct SubQuestionVo {
    stem: String,
    choices_list: Vec<String>,
}

impl QuestionVo {
    pub(crate) async fn from_question(question: &Question, db: Database) -> Self {
        let sub_questions = question.sub_questions.clone().unwrap_or_default().iter().map(|item| SubQuestionVo {
            stem: item.stem.clone(),
            choices_list: item.choices_list.clone(),
        }).collect::<Vec<_>>();

        let futures = question.paper_ids.clone().into_iter().map(|paper_id| {
            let database = db.clone();
            async move {
                let paper = Paper::get_by_id(paper_id, database).await.unwrap();
                QuestionPaperVo {
                    id: paper.id,
                    name: paper.paper_name.clone(),
                }
            }
        }).collect::<Vec<_>>();

        let papers = join_all(futures).await;

        QuestionVo {
            id: question.id,
            stem: question.stem.clone().unwrap_or_default(),
            material: question.material.clone(),
            answer: question.answer.clone(),
            analysis: question.analysis.clone(),
            choices_list: question.choices_list.clone().unwrap_or_default(),
            sub_questions,
            papers,
            content: "".to_string(),
            score: 0.0,
            prefix: "".to_string(),
        }
    }
}
