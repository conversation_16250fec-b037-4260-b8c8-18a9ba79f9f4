use std::io::Error;
use std::vec::Vec;
use futures::StreamExt;
use mongodb::bson::doc;
use mongodb::Database;
use crate::app::models::orm::question::Question;

pub  struct QuestionLoader {
    mode: String,
    subject_code: String,
    batch: Option<String>,
    pub dataset: Vec<(u32, String, Vec<u32>, Option<Vec<u32>>, String)>,
}

impl QuestionLoader {
    pub async fn new(subject_code: String, mode: Option<String>, batch: Option<String>, load: bool,database:Database) -> Self {
        let mut loader = QuestionLoader {
            mode: mode.unwrap_or("all".to_string()),
            subject_code,
            batch,
            dataset: vec![]
        };
        if load {
            loader.reset(database).await.expect("TODO: panic message");
        }
        loader
    }

    pub async fn get(&self, qid: u32, database:Database) -> Option<String> {
        let  question_opt = Question::get_by_id(qid, database).await;
        match question_opt {
            Some(q) => Some(q.output_for_mode(&self.mode)),
            None => None,
        }
    }

    pub async fn reset(&mut self,database: Database) -> Result<(), Error> {
        let mut filter = doc! { "subjectCode": &self.subject_code };

        if let Some(batch_value) = &self.batch {
            filter.insert("batch", batch_value);
        }
        let question_collection = database.collection::<Question>("question");
        let mut questions_cursor = question_collection.find(Some(filter), None).await.unwrap();

        self.dataset.clear();

        while let Some(question) = questions_cursor.next().await {
            match question {
                Ok(question) => {
                    let content = question.output_for_mode(&self.mode);
                    self.dataset.push((question.id, content, question.paper_ids, question.knowledge_ids, question.question_type_code.unwrap_or_default()));
                }
                Err(e) => {
                    // Handle the error as you see fit.
                    println!("Error retrieving question: {:?}", e);
                }
            }
        }

        Ok(())
    }
}