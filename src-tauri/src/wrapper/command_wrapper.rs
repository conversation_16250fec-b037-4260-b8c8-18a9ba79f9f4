use crate::commands::{delete_request_to_remote, get_request_to_remote, post_request_to_remote};
use crate::models::ResponseVO;
use serde::de::DeserializeOwned;
use serde::Serialize;
use crate::services::system_feature::{SystemFeature, UsageMode};

/// 请求方法类型
pub enum RequestMethod {
    Get,
    Post,
    Delete,
}
/// - Author: @tuip123
/// - 通用的请求转发与本地处理逻辑（本地不处理token）
///
/// # Params:
/// - `method`: 请求方法类型（`GET` 或 `POST`）。
/// - `api_path`: 如果转发，调用远端的哪个接口。
/// - `payload_or_params`: 请求远端的负载或参数。
/// - `func`: 如果不转发，调用本地的哪个方法体。
pub async fn forward<F, Fut, T, E>(
    method: RequestMethod,
    api_path: &str,
    payload_or_params: Option<E>,
    func: F,
) -> Result<ResponseVO<T>, ResponseVO<()>>
where
    T: Serialize + DeserializeOwned,
    E: Serialize,
    F: FnOnce() -> Fut,
    Fut: std::future::Future<Output = Result<ResponseVO<T>, ResponseVO<()>>>,
{
    let usage_mode: UsageMode = SystemFeature::get_sys_feat_owned()
        .unwrap_or(UsageMode::default());
    if usage_mode.is_client() {
        match method {
            RequestMethod::Get => Ok(get_request_to_remote(api_path, payload_or_params).await),
            RequestMethod::Post => Ok(post_request_to_remote(api_path, payload_or_params).await),
            RequestMethod::Delete => Ok(delete_request_to_remote(api_path, payload_or_params).await),
        }
    } else {
        func().await
    }
}
