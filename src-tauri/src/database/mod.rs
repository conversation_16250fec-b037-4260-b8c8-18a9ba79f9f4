use serde_derive::{Deserialize, Serialize};
use surrealdb::Surreal;
use surrealdb::engine::local::Db;

pub mod surreal;
pub mod assemble;
pub mod book;
pub mod catalogue;
pub mod card;
pub mod letter;
pub mod pinyin;
pub mod radical;
pub mod vocabulary;
pub mod maps;
pub mod increment_record;
pub mod inc_file_upd_record;
pub mod law;
pub mod plagiarism_batch;
pub mod sentence_match;

pub enum IndexType {
    Normal,
    Unique,
}

impl IndexType {
    pub fn get_name(&self) -> String {
        match self {
            IndexType::Normal => {"normal".to_string()}
            IndexType::Unique => {"unique".to_string()}
        }
    }

    pub fn get_value(&self) -> String {
        match self {
            IndexType::Normal => {"".to_string()}
            IndexType::Unique => {"UNIQUE".to_string()}
        }
    }
}

pub async fn create_surreal_db_index(
    db: &Surreal<Db>, table_name: &str, field_names: Vec<Vec<&str>>, index_type: IndexType
) -> Result<(), surrealdb::Error> {
    let mut response = db.query(format!("INFO FOR TABLE {table_name}")).await?;
    let table_info_op: Option<serde_json::Value> = response.take(0)?;
    let table_info = table_info_op.unwrap();
    let indexes = table_info["indexes"].as_object().unwrap();
    let mut add_index_str = vec![];
    for mut field_name in field_names {
        field_name.sort();
        let index_name = format!("{}_{}_{}_index", table_name, field_name.join("_"), index_type.get_name());
        if !indexes.contains_key(index_name.as_str()) {
            let _ = db.query(
                format!(
                    "DEFINE INDEX {} ON TABLE {} FIELDS {} {}",
                    index_name, table_name, field_name.join(","), index_type.get_value()
                )
            ).await?;
            add_index_str.push(index_name);
        }
    }
    if !add_index_str.is_empty() {
        log::info!("db table {} add index:{}",table_name,add_index_str.join(","));
    }
    Ok(())
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub(crate) struct Pagination {
    #[serde(rename = "pageNo")]
    pub opt_page_no: Option<usize>,
    #[serde(rename = "pageSize")]
    pub opt_page_size: Option<usize>,
}

impl Pagination {
    pub fn new(opt_page_no: Option<usize>, opt_page_size: Option<usize>) -> Self {
        Self {opt_page_no, opt_page_size}
    }
}