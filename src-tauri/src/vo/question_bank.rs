use crate::services::question_bank::QuestionType;
use serde_derive::{Deserialize, Serialize};

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct QuestionTypeVo {
    #[serde(rename = "id")]
    pub id: String,
    pub name: String,
    #[serde(rename = "subjectCode")]
    pub subject_code: String,
    pub code: u64,
    #[serde(rename = "renderType")]
    pub render_type: Option<i32>,
}

impl QuestionTypeVo {
    pub fn from(qt: &QuestionType) -> Self {
        Self {
            id: qt.id.to_string(),
            name: qt.name.clone(),
            subject_code: qt.subject_code.clone(),
            code: qt.code,
            render_type: qt.render_type.clone(),
        }
    }

    pub fn from_list(qts: &Vec<QuestionType>) -> Vec<Self> {
        qts.iter().map(|b| Self::from(b)).collect()
    }

    // pub fn to(&self) -> QuestionType {
    //     QuestionType {
    //         id: self.id.parse().unwrap(),
    //         name: self.name.clone(),
    //         subject_code: self.subject_code.clone(),
    //         code: self.code,
    //         render_type: self.render_type.clone(),
    //     }
    // }

    // pub fn to_list(vos: &Vec<Self>) -> Vec<QuestionType> {
    //     vos.iter().map(|b| b.to()).collect()
    // }
}
