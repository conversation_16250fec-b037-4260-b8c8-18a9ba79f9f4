// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use human_panic::setup_panic;
use crate::utils::ws_client::startup_websocket_client;
use crate::commands::book::{get_book_by_book_id, get_book_news_info_by_brand_pub_date, get_book_page_by_types};
use crate::commands::card::{get_card_content_by_book_id_card_id, get_card_infos_by_book_id};
use crate::commands::catalogue::{get_catalogues_by_book_id,get_newspaper_catalogue_by_book_id};
use crate::commands::searching::{global_search_cards, global_search_books};
use crate::commands::plagiarism::{
    plagiarism_compare, plagiarism_results, plagiarism_batch_list,
    plagiarism_batch_detail, plagiarism_batch_statistics, plagiarism_delete_batch,
    plagiarism_cancel_comparison
};
use crate::commands::increment::{get_increment_result, import_increments_queue, delete_increment_record_by_id};
use crate::commands::activate::{
    activate_parse_code, get_activate_key, get_activate_status,
    get_un_activate_info, get_activate_load_status, get_usable_code
};
use crate::commands::system_feature::{get_system_feat_details, set_user_data_dir_custom_config, set_usage_mode, get_app_data_dir, get_usage_mode};
use crate::commands::assemble::get_assembles_by_type;
use crate::commands::filter_data::book_filter::{filter_book_types, filter_subjects, filter_versions, filter_grades, filter_series, filter_authors, filter_books};
use crate::commands::dictionary::{
    get_dictionary, get_pinyin_without_tones, get_letters_with_pinyin_by_without_tone,
    get_letter_by_id, get_vocabulary_by_id, get_all_radicals, get_letter_sections_by_radical_id,
    get_letters_by_stroke_count, get_snapshots, search_resources,
};
use crate::commands::backup_recover::{
    recover_user_data, backup_user_data,
    get_bak_rcv_status_info, cancel_backup_user_data,
    cancel_recover_user_data
};
use crate::commands::maps::{get_maps, get_maps_bread_crumb, get_maps_filters, get_maps_total};
use crate::commands::law::{get_law, get_law_total};
use crate::commands::file_url::get_fe_file_url_prefix;
use crate::commands::inc_file_upd::get_inc_file_upd_records;
use database::surreal::init_db;
use tauri::async_runtime::block_on;
use tauri::Manager;
use tauri::path::BaseDirectory;
use tauri_plugin_dialog::{DialogExt, MessageDialogKind};
use crate::controller::startup_web_server;
use crate::services::backup_recover::BackupRecoverService;
// use crate::mutex::activation::init_activation;
use crate::services::filter_data::letter_filter::LetterFilter;
use crate::services::filter_data::vocabulary_filter::VocabularyFilter;
use crate::services::inc_file_upd::{init_inc_file_upd_channel, IncFileUpdService};
use crate::services::increment::{init_increment_record_channel, IncrementService};
use crate::utils::app_handle::set_app_handle;
use crate::utils::path::exists_create_app_data_dir;

mod commands;
mod models;
mod search;
mod services;
mod utils;
mod vo;
mod controller;
mod database;
mod mutex;
mod wrapper;

#[cfg(test)]
mod tests;
// struct AppState {
//     shared_data: Arc<Mutex<String>>,
// }

pub fn main() {
    setup_panic!();
    tauri::Builder::default()
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_notification::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_process::init())
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_upload::init())
        // .manage(AppState {
        //     shared_data: Arc::new(Mutex::new("Initial data".to_string())),
        // })
        .setup(|app| {
            let app_handle = app.handle();
            let _resources_path = app.path().resolve("", BaseDirectory::Resource).unwrap();

            block_on(async move {
                // 异步任务的代码
                set_app_handle(&app_handle).expect("设置全局app_handle失败");
                // 检测并创建app_data_dir
                exists_create_app_data_dir().expect("检测和创建app_data_dir失败");
                let _result = init_db().await; // 使用克隆的 AppHandle
                // init_activation().await.expect("加载激活信息失败");
                IncFileUpdService::cancel_all_files_uploading().await.expect("关闭未完结的增量包文件上传失败");
                IncrementService::cancel_all_increments_importing().await.expect("关闭未完结的增量包导入失败");
                // 初始化上传增量包消息队列
                init_inc_file_upd_channel(2000).await;
                // 初始化增量更新消息队列
                init_increment_record_channel(2000).await;
                // 启动websocket客户端监听
                startup_websocket_client().await;
                // 加载字词典相关过滤器实例
                LetterFilter::load().await.expect("加载汉字过滤器实例失败");
                VocabularyFilter::load().await.expect("加载词语过滤器实例失败");
                startup_web_server().await.expect("启动应用服务器失败");
            });

            // 获取主窗口对象
            if let Some(main_window) = app.get_webview_window("main") {
                // 克隆必要的对象到闭包内
                let app_handle = app.handle().clone();

                main_window.on_window_event(move |event| {
                    if let tauri::WindowEvent::CloseRequested { api, .. } = event {
                        let (upload, increment) = block_on(async {
                            tokio::join!(
                                    IncFileUpdService::unfinished_uploading_count(),
                                    IncrementService::unfinished_increment_count()
                                )
                        });
                        if upload > 0 || increment > 0 {
                            // 阻止关闭应用
                            api.prevent_close();
                            // 显示对话框
                            app_handle.dialog()
                                .message("增量包正在导入或上传至服务器时，不允许关闭应用，您必须等待导入或上传完成！")
                                .kind(MessageDialogKind::Warning)
                                .title("警告")
                                .show(|_|{});
                            return;
                        }
                        let result = block_on(async {
                            BackupRecoverService::in_backup_or_recover().await
                        });
                        if result {
                            // 阻止关闭应用
                            api.prevent_close();
                            // 显示对话框
                            app_handle.dialog()
                                .message("当前正在备份或恢复用户数据，不允许关闭应用，您可以取消备份或等待恢复完成，方可关闭应用")
                                .kind(MessageDialogKind::Warning)
                                .title("警告")
                                .show(|_|{});
                            return;
                        }
                    }
                });
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            get_activate_load_status,
            get_activate_status,
            get_un_activate_info,
            get_activate_key,
            activate_parse_code,
            get_usable_code,
            get_app_data_dir,
            get_fe_file_url_prefix,
            get_system_feat_details,
            set_user_data_dir_custom_config,
            get_usage_mode,
            set_usage_mode,
            get_bak_rcv_status_info,
            backup_user_data,
            recover_user_data,
            cancel_backup_user_data,
            cancel_recover_user_data,
            get_increment_result,
            import_increments_queue,
            delete_increment_record_by_id,
            get_inc_file_upd_records,
            global_search_cards,
            global_search_books,
            plagiarism_compare,
            plagiarism_results,
            plagiarism_batch_list,
            plagiarism_batch_detail,
            plagiarism_batch_statistics,
            plagiarism_delete_batch,
            plagiarism_cancel_comparison,
            get_assembles_by_type,
            filter_book_types,
            filter_subjects,
            filter_versions,
            filter_grades,
            filter_series,
            filter_authors,
            filter_books,
            get_book_by_book_id,
            get_catalogues_by_book_id,
            get_card_infos_by_book_id,
            get_newspaper_catalogue_by_book_id,
            get_card_content_by_book_id_card_id,
            get_book_page_by_types,
            get_dictionary,
            get_pinyin_without_tones,
            get_letters_with_pinyin_by_without_tone,
            get_letter_by_id,
            get_vocabulary_by_id,
            get_all_radicals,
            get_letter_sections_by_radical_id,
            get_letters_by_stroke_count,
            get_snapshots,
            search_resources,
            get_book_news_info_by_brand_pub_date,
            get_maps,
            get_law,
            get_law_total,
            get_maps_bread_crumb,
            get_maps_filters,
            get_maps_total,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
