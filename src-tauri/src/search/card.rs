use crate::search::{
    get_tokens_by_jieba, search_documents_or_sort_by_field,
    Jieba<PERSON>okenizerNSWs, PredictCardOr<PERSON>ook<PERSON>ara<PERSON>, PredictResult
};
use serde_derive::{Deserialize, Serialize};
use std::collections::{Bound, HashMap, HashSet};
use std::{fs, thread};
use std::num::NonZero;
use std::time::Instant;
use tantivy::collector::TopDocs;
use tantivy::directory::MmapDirectory;
use tantivy::query::{AllQuery, BooleanQuery, Occur, Query, QueryParser, RangeQuery, TermQuery};
use tantivy::schema::{Field, IndexRecordOption, Schema, TextFieldIndexing, TextOptions, Value, FAST, INDEXED, STORED, STRING};
use tantivy::{doc, Index, IndexWriter, Order, ReloadPolicy, TantivyDocument, Term};
use crate::database::book::BookRepository;
use crate::database::card::CardRepository;
use crate::database::catalogue::CatalogueRepository;
use crate::models::assemble::Assemble;
use crate::models::book::Book;
use crate::models::card::Card;
use crate::models::catalogue::Catalogue;
use crate::utils::path::get_retrieval_dir;
use crate::utils::time_util::{date_str_to_datetime_local, datetime_str_to_datetime_local, now_datetime_local};

struct CardFields {
    id: Field,
    book_id: Field,
    subject_code: Field,
    t_type: Field,
    author: Field,
    series: Field,
    version: Field,
    edition: Field,
    brand: Field,
    period: Field,
    grade: Field,
    publish_date: Field,
    assemble_id: Field,
    content: Field,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CardSearchResult {
    pub id: String,
    pub score: f32,
    pub page: i32,
    #[serde(rename = "type")]
    pub r#type: String,
    pub title: Option<String>,
    pub content: String,
    #[serde(rename = "subjectCode")]
    pub subject_code: String,
    #[serde(rename = "subjectName")]
    pub subject_name: String,
    #[serde(rename = "bookId")]
    pub book_id: String,
    #[serde(rename = "bookName")]
    pub book_name: String,
    pub series: Option<String>,
    #[serde(rename = "catalogueId")]
    pub catalogue_id: String,
    #[serde(rename = "catalogueCode")]
    pub catalogue_code: String,
    #[serde(rename = "catalogueSerial")]
    pub catalogue_serial: i32,
    #[serde(rename = "catalogueParentId")]
    pub catalogue_parent_id: Option<String>,
    #[serde(rename = "cataloguePathName")]
    pub catalogue_path_name: String,
    #[serde(rename = "catalogueName")]
    pub catalogue_name: String,
}

/// 打开或创建知识卡片索引
async fn open_or_create_card_index() -> Result<(CardFields, Schema, Index), Box<dyn std::error::Error>> {
    let text_field_indexing = TextFieldIndexing::default()
        .set_tokenizer("jieba")
        .set_index_option(IndexRecordOption::WithFreqsAndPositions);
    let text_options = TextOptions::default().set_indexing_options(text_field_indexing);

    let mut schema_builder = Schema::builder();
    let id = schema_builder.add_u64_field("id", INDEXED | FAST | STORED);
    let book_id = schema_builder.add_u64_field("book_id", INDEXED | FAST);
    let subject_code = schema_builder.add_text_field("subject_code", STRING | FAST);
    let t_type = schema_builder.add_text_field("t_type", STRING | FAST);
    let author = schema_builder.add_text_field("author", STRING | FAST);
    let series = schema_builder.add_text_field("series", STRING | FAST);
    let version = schema_builder.add_text_field("version", STRING | FAST);
    let edition = schema_builder.add_text_field("edition", STRING | FAST);
    let brand = schema_builder.add_text_field("brand", STRING | FAST);
    let period = schema_builder.add_i64_field("period", INDEXED | FAST);
    let grade = schema_builder.add_text_field("grade", STRING | FAST);
    let publish_date = schema_builder.add_i64_field("publish_date", INDEXED | FAST);
    let assemble_id = schema_builder.add_u64_field("assemble_id", INDEXED | FAST);
    let content = schema_builder.add_text_field("content", text_options);

    let schema = schema_builder.build();

    let retrieval_dir = get_retrieval_dir()?;
    let index_path = retrieval_dir.join("card");
    if !index_path.as_path().exists() {
        fs::create_dir_all(index_path.as_path())?;
    }

    let dir = MmapDirectory::open(index_path)?;
    let index = Index::open_or_create(dir, schema.clone())?;

    let tokenizer = JiebaTokenizerNSWs::new();
    index.tokenizers().register("jieba", tokenizer);
    let fields = CardFields {
        id,
        book_id,
        subject_code,
        t_type,
        author,
        series,
        version,
        edition,
        brand,
        period,
        grade,
        publish_date,
        assemble_id,
        content,
    };
    Ok((fields, schema, index))
}

async fn bulk_predict_card_results(
    raw_rst: Vec<(u64, f32)>,
) -> Result<Vec<CardSearchResult>, Box<dyn std::error::Error>> {
    let card_ids: Vec<u64> = raw_rst.iter().map(|rt| rt.0).collect();
    let cards: Vec<Card> = CardRepository::find_all_by_ids(&card_ids).await?;
    let mut book_ids: Vec<u64> = vec![];
    let mut seen = HashSet::new();
    for c in cards.iter() {
        if seen.insert(c.book_id) {
            book_ids.push(c.book_id);
        }
    }
    let books: Vec<Book> = BookRepository::find_all_by_ids(&book_ids).await?;
    let mut tb_map: HashMap<u64, Book> = HashMap::new();
    for tb in books {
        tb_map.insert(tb.book_id, tb);
    }

    let mut catalogue_ids: Vec<u64> = vec![];
    let mut seen = HashSet::new();
    for c in cards.iter() {
        if let Some(ref cid) = c.catalogue_id {
            if seen.insert(cid) {
                catalogue_ids.push(*cid);
            }
        }
    }
    let catalogues: Vec<Catalogue> = CatalogueRepository::find_all_by_ids(&catalogue_ids).await?;
    let mut catalogue_map: HashMap<u64, Catalogue> = HashMap::new();
    for c in catalogues {
        catalogue_map.insert(c.catalogue_id, c);
    }

    let mut card_map: HashMap<u64, Card> = HashMap::new();
    for c in cards {
        card_map.insert(c.card_id, c);
    }

    let mut card_search_results: Vec<CardSearchResult> = vec![];
    for (cid, score) in raw_rst {
        let result = card_map.get(&cid);
        if result.is_none() {
            continue;
        }
        let card = result.unwrap();
        let result = tb_map.get(&card.book_id);
        if result.is_none() {
            continue;
        }
        let tb = result.unwrap();
        if card.catalogue_id.is_none()
            || card.catalogue_id.is_some_and(|ref cid| !catalogue_map.contains_key(cid)) {
            continue;
        }
        let result = catalogue_map.get(card.catalogue_id.as_ref().unwrap());
        if result.is_none() {
            continue;
        }
        let catalogue = result.unwrap();

        card_search_results.push(CardSearchResult {
            id: card.card_id.to_string(),
            score,
            page: card.page.unwrap_or(0),
            r#type: tb.r#type.clone(),
            title: card.title.clone(),
            content: card.content.clone(),
            subject_code: tb.subject_code.clone(),
            subject_name: tb.subject_name.clone(),
            book_id: card.book_id.to_string(),
            book_name: tb.book_name.clone(),
            series: tb.series.clone(),
            catalogue_id: catalogue.catalogue_id.to_string(),
            catalogue_code: catalogue.code.clone(),
            catalogue_serial: catalogue.serial,
            catalogue_parent_id: catalogue.parent_id.map(|p| p.to_string()),
            catalogue_path_name: catalogue.path_name.clone(),
            catalogue_name: catalogue.title.clone(),
        });
    }

    Ok(card_search_results)
}

/// 创建或更新知识卡片的索引，如果文档id相同，会覆盖旧的索引
/// 必须确保cards都有相同subject_code
/// 在导入教材数据时，调用此方法
pub async fn training_card(
    cards: &Vec<Card>,
    book_map: &HashMap<u64, Book>,
    book_id_assembles_map: &HashMap<u64, Vec<&Assemble>>,
)
    -> Result<(), Box<dyn std::error::Error>> {
    if cards.is_empty() {
        return Ok(());
    }

    let (fields, _, index) = open_or_create_card_index().await?;

    let reader = index.reader()?;
    let binding = reader.searcher();
    let searcher = &binding;

    // 获取CPU核心数
    let result = thread::available_parallelism();
    let mut core_num = result.unwrap_or(NonZero::<usize>::new(4).unwrap()).get();
    if core_num > 8 {
        core_num = 8;
    }
    let memory_size = core_num * 512_000_000;

    let mut index_writer: IndexWriter = index.writer_with_num_threads(
        core_num, memory_size
    )?;

    for card in cards.iter() {
        let opt_book = book_map.get(&card.book_id);
        if opt_book.is_none() {
            println!("Cannot find book for book_id: {}", card.book_id);
            continue;
        }
        let book = opt_book.unwrap();
        let opt_assembles = book_id_assembles_map.get(&card.book_id);
        let empty_assembles = Vec::new();
        let assembles = opt_assembles.unwrap_or(&empty_assembles);

        let term = Term::from_field_u64(fields.id, card.card_id);
        // Search for the document by its ID
        let query = TermQuery::new(term.clone(), IndexRecordOption::Basic);
        let top_docs = searcher.search(&query, &TopDocs::with_limit(1))?;
        // If document exists, delete it
        if !top_docs.is_empty() {
            index_writer.delete_term(term);
        }

        let mut doc = doc!(
            fields.id => card.card_id,
            fields.book_id => card.book_id,
            fields.subject_code => book.subject_code.as_str(),
            fields.t_type => book.r#type.as_str(),
            fields.content => card.content.as_str(),
        );
        if let Some(ref author) = book.author {
            doc.add_text(fields.author, author.as_str());
        }
        if let Some(ref series) = book.series {
            doc.add_text(fields.series, series.as_str());
        }
        if let Some(ref version) = book.version {
            doc.add_text(fields.version, version.as_str());
        }
        if let Some(ref edition) = book.edition {
            doc.add_text(fields.edition, edition.as_str());
        }
        if let Some(ref brand) = book.brand {
            doc.add_text(fields.brand, brand.as_str());
        }
        if let Some(period) = book.period {
            doc.add_i64(fields.period, period as i64);
        }
        if let Some(ref grade) = book.grade {
            doc.add_text(fields.grade, grade.as_str());
        }
        if let Some(ref publish_date) = book.publish_date {
            let result = datetime_str_to_datetime_local(publish_date.as_str(), None);
            if let Ok(datetime_local) = result {
                doc.add_i64(fields.publish_date, datetime_local.timestamp_millis());
            }
        }
        for asm in assembles {
            doc.add_u64(fields.assemble_id, (**asm).assemble_id);
        }
        index_writer.add_document(doc)?;
    }

    index_writer.commit()?;
    index_writer.wait_merging_threads()?;

    Ok(())
}

/// 教材文章搜索
pub async fn predict_card(
    params: PredictCardOrBookParams,
) -> Result<PredictResult<CardSearchResult>, Box<dyn std::error::Error>> {
    let start = Instant::now();

    let (fields, _, index) = open_or_create_card_index().await?;

    let reader = index
        .reader_builder()
        .reload_policy(ReloadPolicy::OnCommitWithDelay)
        .doc_store_cache_num_blocks(10000)
        .try_into()?;

    let searcher = reader.searcher();

    let mut subqueries: Vec<(Occur, Box<dyn Query>)> = vec![];

    let mut token_texts = vec![];
    let search_text_trim = params.search_text.trim();
    if !search_text_trim.is_empty() {
        // 对关键词进行分词处理
        token_texts = get_tokens_by_jieba(&params.search_text);
        // 使用分词后的关键词进行查询
        let tokens_text = token_texts.join(" ");

        // 搜索content字段
        let query_parser = QueryParser::for_index(&index, vec![fields.content]);
        let (content_query, errors)
            = query_parser.parse_query_lenient(tokens_text.as_str());
        if !errors.is_empty() {
            println!("Error occurred when parse_query for content: {}, errors: {:#?}", params.search_text, errors);
        }
        subqueries.push((Occur::Must, content_query));
    }

    if let Some(book_id) = params.book_id {
        let sub_query = TermQuery::new(
            Term::from_field_u64(fields.book_id, book_id),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref subject_code) = params.subject_code {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.subject_code, subject_code),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref t_type) = params.t_type {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.t_type, t_type),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref author) = params.author {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.author, author),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref series) = params.series {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.series, series),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref version) = params.version {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.version, version),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref edition) = params.edition {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.edition, edition),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref brand) = params.brand {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.brand, brand),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(period) = params.period {
        let sub_query = TermQuery::new(
            Term::from_field_i64(fields.period, period as i64),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref grade) = params.grade {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.grade, grade),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref publish_year) = params.publish_year {
        let (earliest_publish_date, recent_publish_date)
            = (format!("{}-01-01", publish_year), format!("{}-12-31", publish_year));
        let (ear_ts, recent_ts) = (
            date_str_to_datetime_local(&earliest_publish_date, None)
                .map(|t| t.timestamp_millis())
                .unwrap_or(0i64),
            date_str_to_datetime_local(&recent_publish_date, None)
                .map(|t| t.timestamp_millis())
                .unwrap_or(now_datetime_local().timestamp_millis()),
        );
        // 构建范围查询
        let range_query = RangeQuery::new_i64_bounds(
            "publish_date".to_string(), Bound::Included(ear_ts), Bound::Included(recent_ts)
        );
        subqueries.push((Occur::Must, Box::new(range_query)));
    }
    if params.publish_year.is_none() && (params.earliest_publish_date.is_some() || params.recent_publish_date.is_some()) {
        let mut ear_ts = 0i64;
        if let Some(ref t_str) = params.earliest_publish_date {
            if let Ok(tl) = date_str_to_datetime_local(t_str, None) {
                ear_ts = tl.timestamp_millis();
            }
        }
        let mut recent_ts = now_datetime_local().timestamp_millis();
        if let Some(ref t_str) = params.recent_publish_date {
            if let Ok(tl) = date_str_to_datetime_local(t_str, None) {
                recent_ts = tl.timestamp_millis();
            }
        }
        // 构建范围查询
        let range_query = RangeQuery::new_i64_bounds(
            "publish_date".to_string(), Bound::Included(ear_ts), Bound::Included(recent_ts)
        );
        subqueries.push((Occur::Must, Box::new(range_query)));
    }
    if let Some(assemble_id) = params.assemble_id {
        let assemble_id = assemble_id.parse()?;
        let sub_query = TermQuery::new(
            Term::from_field_u64(fields.assemble_id, assemble_id),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }

    // 将查询结合在一起
    let combined_query: Box<dyn Query> = if !subqueries.is_empty() {
        Box::from(BooleanQuery::new(subqueries))
    } else {
        Box::from(AllQuery{})
    };

    let page_no = params.page_no.unwrap_or(0);
    let page_size = params.page_size.unwrap_or(10);
    let offset = page_no * page_size;

    let (top_docs, totals) = search_documents_or_sort_by_field::<u64>(
        &searcher, &combined_query, &params.search_text,
        page_size, offset, "assemble_id", Order::Asc
    )?;

    let mut raw_rsts: Vec<(u64, f32)> = vec![];
    for (_score, doc_address) in top_docs {
        // Retrieve the actual content of documents given its `doc_address`.
        let retrieved_doc = searcher.doc::<TantivyDocument>(doc_address)?;
        let temp_list: Vec<(u64, f32)> = retrieved_doc
            .get_all(fields.id)
            .map(|i| i.as_u64())
            .filter(|i| i.is_some())
            .map(|i| (i.unwrap(), _score))
            .collect();
        raw_rsts.extend(temp_list);
    }

    let page_count = if totals % page_size != 0 {
        totals / page_size + 1
    } else {
        totals / page_size
    };

    let duration = start.elapsed();
    let retrieval_duration = duration.as_millis();
    println!("retrieval searching time taken: {} ms", retrieval_duration);

    let result_list = bulk_predict_card_results(raw_rsts).await?;

    let duration = start.elapsed();
    let total_duration = duration.as_millis();
    println!("Predict card time taken: {} ms", total_duration);

    let result = PredictResult {
        totals,
        page_no,
        page_count,
        page_size,
        highlights: token_texts,
        retrieval_duration,
        total_duration,
        list: result_list,
    };

    Ok(result)
}

// #[test]
// fn test01() -> Result<(), Box<dyn std::error::Error>> {
//     let rt = Runtime::new()?;
//     let result: Result<PredictResult<CardSearchResult>, Box<dyn std::error::Error>> =
//         rt.block_on(async {
//             init_db_for_test().await.expect("TODO: panic message");
//             let db = get_db().await;
//             let root_dir =
//                 PathBuf::from(r"C:\Users\<USER>\AppData\Roaming\com.qctchina.proposition-assistant");
//             let params = PredictCardParams {
//                 page_no: None,
//                 page_size: None,
//                 subject_code: "00060",
//                 textbook_id: Some(1657),
//                 search_text: "财政",
//             };
//             Ok(predict_card(&root_dir, &db, params).await?)
//         });
//
//
//     Ok(())
// }
