use std::fs::File;
use std::io::{self, <PERSON><PERSON><PERSON><PERSON><PERSON>, Buf<PERSON>riter, Read, Write};

const BUF_SIZE: usize = 1024 * 8; // Define the buffer size
const CRYPT_KEY: [i8; 8] = [1, 5, -12, 100, 45, 42, -24, -128];

pub(crate) fn crypt(file_in: &str, file_out: &str) -> io::Result<()> {
    let input_file = File::open(file_in)?;
    let output_file = File::create(file_out)?;

    let mut reader = BufReader::new(input_file);
    let mut writer = BufWriter::new(output_file);

    let mut buf = [0u8; BUF_SIZE];
    let mut token = 0;
    let mut index = 0;
    loop {
        let read_len = reader.read(&mut buf)?;
        if read_len == 0 {
            break; // Exit the loop if no more data to read
        }

        if token == index {
            for i in 0..read_len {
                buf[i] ^= CRYPT_KEY[i % CRYPT_KEY.len()] as u8;
            }
            token = (token + 1) * 2;
        }
        writer.write_all(&buf[..read_len])?;
        index += 1;
    }
    Ok(())
}
