pub struct QuestionTypeUtils;

impl QuestionTypeUtils {
    /**
     * 判断题型名称是否为选择题
     * @param que_type_name 题型名称
     * @return 是否为选择题
     */
    pub fn is_choice_que(que_type_name: &str) -> bool {
        que_type_name.contains("选择题")
            || que_type_name.contains("单选")
            || que_type_name.contains("双选")
            || que_type_name.contains("多选")
    }
}

// Tests
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_is_choice_que() {
        assert!(QuestionTypeUtils::is_choice_que("选择题"));
        assert!(QuestionTypeUtils::is_choice_que("单选题"));
        assert!(QuestionTypeUtils::is_choice_que("双选题"));
        assert!(QuestionTypeUtils::is_choice_que("多选题"));
        assert!(!QuestionTypeUtils::is_choice_que("填空题"));
        assert!(!QuestionTypeUtils::is_choice_que("问答题"));
    }
}
