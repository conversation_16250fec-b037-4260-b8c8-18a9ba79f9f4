use serde::Serialize;
use std::collections::HashMap;
use std::fs::File;
use std::io::{<PERSON>urs<PERSON>, Read, Write};
use std::path::{Path, PathBuf};
use tera::{from_value, to_value, Context, Result as TeraResult, Tera, Value};
use zip::write::FileOptions;
use zip::{ZipArchive, ZipWriter};

#[derive(Debug)]
pub struct RenderConfig<T: Serialize> {
    pub blueprint_path: String,
    pub root_path: PathBuf,
    pub templates: Vec<RenderTemplate>,
    pub saved_path: String,
    pub params: T,
}

#[derive(Debug)]
pub struct RenderTemplate {
    pub name: String,
    pub path: String,
    pub path_in_zip: String,
}

impl RenderTemplate {
    pub fn hashmap(
        templates: Vec<Self>,
    ) -> Result<HashMap<String, Self>, Box<dyn std::error::Error>> {
        let mut map: HashMap<String, Self> = HashMap::new();
        for temp in templates {
            map.insert(temp.path_in_zip.clone(), temp);
        }
        Ok(map)
    }
}

fn substring_filter(value: &Value, args: &HashMap<String, Value>) -> TeraResult<Value> {
    // 从 JSON Value 转换为 &str
    let s = match from_value::<String>(value.clone()) {
        Ok(s) => s,
        Err(_) => return Err(tera::Error::msg("The value is not a valid string")),
    };

    // 获取 start 参数
    let start = match args.get("start") {
        Some(val) => from_value::<usize>(val.clone()).unwrap_or(0),
        None => 0,
    };

    // 获取 length 参数（可选）
    let length = match args.get("length") {
        Some(val) => from_value::<usize>(val.clone()).unwrap_or(s.len() - start),
        None => s.len() - start,
    };

    // 计算结束下标
    let end = usize::min(start + length, s.len());

    // 获取子字符串
    let substring = &s[start..end];

    // 转换为 JSON Value 并返回
    Ok(to_value(substring).unwrap())
}

pub fn render_docx<T: Serialize>(
    config: RenderConfig<T>,
) -> Result<(), Box<dyn std::error::Error>> {
    // 设置模板上下文
    let context = &mut Context::from_serialize(&config.params)?;
    // 创建 Tera 模板引擎
    #[cfg(target_os = "windows")]
    let mut tera = Tera::new("templates/**/*")?;
    #[cfg(not(target_os = "windows"))]
    let template_dir = config
        .root_path
        .join("templates")
        .to_string_lossy()
        .into_owned()
        .as_str()
        .to_owned()
        + "/**/*";
    #[cfg(not(target_os = "windows"))]
    let mut tera = Tera::new(template_dir.as_str())?;

    // 注册自定义过滤器
    tera.register_filter("substring", substring_filter);
    // 逐个添加模板
    for temp in config.templates.iter() {
        tera.add_template_file(Path::new(temp.path.as_str()), Some(temp.name.as_str()))?;
    }
    let temp_map = RenderTemplate::hashmap(config.templates)?;

    // 读取模板文件到内存
    let mut file = File::open(config.blueprint_path)?;
    let mut buffer = Vec::new();
    file.read_to_end(&mut buffer)?;

    // 创建内存中的 zip 文件读取器
    let reader = Cursor::new(buffer);
    let mut archive = ZipArchive::new(reader)?;

    // 创建新的 DOCX 文件
    let file = File::create(config.saved_path)?;
    let mut zip_writer = ZipWriter::new(file);
    let options = FileOptions::default().compression_method(zip::CompressionMethod::Stored);

    // 遍历 zip 文件中的所有文件
    for i in 0..archive.len() {
        let mut file = archive.by_index(i)?;
        let file_name = file.name().to_string();

        // 读取文件内容
        let mut file_content = Vec::new();
        file.read_to_end(&mut file_content)?;

        // 如果匹配到模板文件，使用模板引擎渲染，否则直接原文件写入
        match temp_map.get(file_name.as_str()) {
            None => {
                // 直接将文件内容写入新的 zip 文件中
                zip_writer.start_file(file_name, options)?;
                zip_writer.write_all(&file_content)?;
            }
            Some(temp) => {
                // 渲染模板
                let rendered = tera.render(temp.name.as_str(), &context)?;
                zip_writer.start_file(file_name, options)?;
                zip_writer.write_all(rendered.as_bytes())?;
            }
        }
    }

    // 完成 zip 文件写入
    zip_writer.finish()?;

    Ok(())
}

#[test]
fn test01() {}
