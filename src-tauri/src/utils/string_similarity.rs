use fuzzy_matcher::skim::SkimMatcherV2;
use fuzzy_matcher::FuzzyMatcher;
use similar::TextDiff;
use strsim::{jaro_winkler, normalized_levenshtein, sorensen_dice};

pub fn find_similar_strings(target: &str, strings: &[&str], threshold: f64) -> Vec<(usize, f64)> {
    if target.trim().is_empty() {
        return vec![];
    }

    strings
        .iter()
        .enumerate()
        .filter(|s| !s.1.trim().is_empty())
        .map(|(idx, &s)| (idx, normalized_levenshtein(target, s)))
        .filter(|(_, sim)| sim >= &threshold)
        .collect()
}

pub fn find_similar_strings_with_weight(target: &str, strings: &[&str], threshold: f64) -> Vec<(usize, f64)> {
    if target.trim().is_empty() {
        return vec![];
    }
    strings
        .iter()
        .enumerate()
        .filter(|s| !s.1.trim().is_empty())
        .map(|(idx, &s)| {
            let jaro_winkler_weight = 0.7;
            let levenshtein_weight = 0.3;
            let jar = jaro_winkler(target, s);
            let lev = normalized_levenshtein(target, s);
            let sim = jar*jaro_winkler_weight + lev*levenshtein_weight;
            (idx, sim)
        })
        .filter(|(_, sim)| sim >= &threshold)
        .collect()
}

pub fn find_similar_text(target: &str, strings: &[&str], threshold: f64) -> Vec<(usize, f64)> {
    if target.trim().is_empty() {
        return vec![];
    }

    strings
        .iter()
        .enumerate()
        .filter(|s| !s.1.trim().is_empty())
        .map(|(idx, &s)| (idx, calculate_similarity(target, s)))
        .filter(|(_, sim)| sim >= &threshold)
        .collect()
}

fn calculate_similarity(text1: &str, text2: &str) -> f64 {
    // 1. Fuzzy matching score
    let matcher = SkimMatcherV2::default().ignore_case();
    let fuzzy_score = matcher
        .fuzzy_match(text2, text1)
        .map(|score| score as f64 / 100.0)
        .unwrap_or(0.0);

    // 2. Jaro-Winkler similarity
    let jw_score = jaro_winkler(text1, text2);

    // 3. Normalized Levenshtein
    let lev_score = normalized_levenshtein(text1, text2);

    // 4. Sørensen-Dice coefficient
    let sorensen_score = sorensen_dice(text1, text2);

    // 5. Text difference ratio
    let diff = TextDiff::from_chars(text1, text2);
    let diff_ratio = diff.ratio();

    // 6. Contains check
    let contains = text2.to_lowercase().contains(&text1.to_lowercase());

    // 7. Calculate combined score with weights
    let combined_score = calculate_combined_score(
        fuzzy_score,
        jw_score,
        lev_score,
        sorensen_score,
        diff_ratio as f64,
        contains,
    );
    combined_score
}

fn calculate_combined_score(
    fuzzy: f64,
    jw: f64,
    lev: f64,
    sorensen: f64,
    diff_ratio: f64,
    contains: bool,
) -> f64 {
    let base_score = fuzzy * 0.3 +         // Fuzzy matching weight
        jw * 0.2 +           // Jaro-Winkler weight
        lev * 0.2 +          // Levenshtein weight
        sorensen * 0.2 +     // Sørensen-Dice weight
        diff_ratio* 0.1;
    // Diff ratio weight;

    // 如果是包含关系，提升分数
    if contains {
        (base_score + 0.2).min(1.0)
    } else {
        base_score
    }
}

// #[test]
// fn test01() {
//     let str1 = "根据幼儿活动的空间，可以把教育环境分为校园环境、家庭环境和";
//     let str2 = "22.根据幼儿活动的空间，可以把教育环境分为校园环境、家庭环境和_____环境。";
//     println!("{}", normalized_damerau_levenshtein(str1, str2));
//     println!("{}", normalized_levenshtein(str1, str2));
// }
