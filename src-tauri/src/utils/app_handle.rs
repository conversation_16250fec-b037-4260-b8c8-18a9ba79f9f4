use std::sync::RwLock;
use lazy_static::lazy_static;


lazy_static! {
    static ref G_APP_HANDLE: RwLock<Option<tauri::AppHandle>> =
        RwLock::new(None);
}


/// 在应用初始化时请先设置全局app_handle
pub fn set_app_handle(app_handle: &tauri::AppHandle) -> Result<(), Box<dyn std::error::Error>> {
    let mut opt_app_handle = G_APP_HANDLE.write()?;
    *opt_app_handle = Some(app_handle.clone());
    Ok(())
}

/// 在应用初始化时请先set_app_handle
pub fn get_app_handle() -> Result<tauri::AppHandle, Box<dyn std::error::Error>> {
    let opt_app_handle = G_APP_HANDLE.read()?;
    if opt_app_handle.is_none() {
        return Err(Box::from(anyhow::Error::msg("尚未设置app_handle")));
    }
    Ok(opt_app_handle.clone().unwrap())
}