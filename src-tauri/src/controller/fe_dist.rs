use std::path::PathBuf;
use actix_files::NamedFile;
use actix_web::{web, HttpRequest, HttpResponse, Result};
use crate::models::ResponseVO;
use crate::utils::path::get_resources_dir;

///前端打包文件代理，模仿nginx配置前端打包文件的反向代理
async fn fe_dist_files(req: HttpRequest) -> Result<HttpResponse> {
    let rel_path: PathBuf = req.match_info().query("filename").parse()?;
    let resources_dir = get_resources_dir()?;
    let fe_dist_dir = resources_dir.join("dist");
    // 将路径转换为标准形式，消除冗余的`.`或`..`
    let result = fe_dist_dir.canonicalize();
    if let Err(e) = result {
        return Ok(HttpResponse::InternalServerError().json(
            ResponseVO::<()>::error(
                Some(format!("fe_dist_dir配置错误，请检查：{:?}，msg：{}", fe_dist_dir.display().to_string(), e.to_string()))
            )
        ));
    }
    let fe_dist_dir = result?;
    let abs_path = fe_dist_dir.join(&rel_path);
    let result = abs_path.canonicalize();
    // 如果展开文件绝对路径报错（一般是文件不存在），或者绝对路径不指向一个文件，亦或者不以fe_dist_dir为前缀（避免被攻击），
    // 那么此时就返回index.html
    if result.is_err() || result.as_ref().is_ok_and(|p| !p.is_file() || !p.starts_with(&fe_dist_dir)) {
        let index_html_path = fe_dist_dir.join("index.html");
        return if index_html_path.exists() {
            let file = NamedFile::open(index_html_path)?;
            Ok(file.use_last_modified(true).disable_content_disposition().into_response(&req))
        } else {
            Ok(HttpResponse::NotFound().finish())
        }
    }
    let abs_path = result?;
    let file = NamedFile::open(abs_path)?;
    Ok(file.use_last_modified(true).disable_content_disposition().into_response(&req))
}

pub(crate) fn fe_dist_ctrl_config(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::resource("/{filename:.*}")
            .to(fe_dist_files)
    ).service(
        web::resource("/")
            .to(fe_dist_files)
    );
}