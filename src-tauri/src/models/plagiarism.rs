use serde_derive::{Deserialize, Serialize};
use surrealdb::RecordId;
use crate::models::surreal::deserialize_id_u64;
use crate::utils::snowflake_generator::SnowflakeGeneratorUtil;
use crate::commands::plagiarism::{PlagiarismBatch, SentenceMatch};
use chrono::{Utc, DateTime};

// 数据库中的查重批次模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlagiarismBatchDb {
    pub id: Option<RecordId>,
    #[serde(rename = "batchId", deserialize_with = "deserialize_id_u64")]
    pub batch_id: u64,
    pub name: String,
    pub description: Option<String>,
    #[serde(rename = "bookIds")]
    pub book_ids: String, // JSON字符串存储书籍ID列表
    #[serde(rename = "totalMatches")]
    pub total_matches: i32,
    pub status: String, // "pending", "processing", "completed", "failed"
    #[serde(rename = "createTime")]
    pub create_time: u64, // 时间戳
    #[serde(rename = "updateTime")]
    pub update_time: u64, // 时间戳
    #[serde(rename = "completedTime")]
    pub completed_time: Option<u64>, // 时间戳
    pub progress: Option<i32>,
}

// 数据库中的句子匹配结果模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SentenceMatchDb {
    pub id: Option<RecordId>,
    #[serde(rename = "matchId", deserialize_with = "deserialize_id_u64")]
    pub match_id: u64,
    #[serde(rename = "batchId", deserialize_with = "deserialize_id_u64")]
    pub batch_id: u64,
    #[serde(rename = "sourceBookId")]
    pub source_book_id: String,
    #[serde(rename = "sourceBookName")]
    pub source_book_name: String,
    #[serde(rename = "sourcePage")]
    pub source_page: i32,
    #[serde(rename = "sourceContent")]
    pub source_content: String,
    #[serde(rename = "targetBookId")]
    pub target_book_id: String,
    #[serde(rename = "targetBookName")]
    pub target_book_name: String,
    #[serde(rename = "targetPage")]
    pub target_page: i32,
    #[serde(rename = "targetContent")]
    pub target_content: String,
    pub similarity: f64,
    #[serde(rename = "matchType")]
    pub match_type: String, // "exact", "similar", "partial"
    #[serde(rename = "createTime")]
    pub create_time: u64, // 时间戳
}

impl PlagiarismBatchDb {
    pub fn new(
        name: String,
        description: Option<String>,
        book_ids: Vec<String>,
    ) -> Self {
        let now = Utc::now().timestamp_millis() as u64;
        let batch_id = SnowflakeGeneratorUtil::next();
        
        // 将书籍ID列表序列化为JSON字符串
        let book_ids_json = serde_json::to_string(&book_ids).unwrap_or_else(|_| "[]".to_string());
        
        Self {
            id: None,
            batch_id,
            name,
            description,
            book_ids: book_ids_json,
            total_matches: 0,
            status: "pending".to_string(),
            create_time: now,
            update_time: now,
            completed_time: None,
            progress: Some(0),
        }
    }

    pub fn get_book_ids(&self) -> Vec<String> {
        serde_json::from_str(&self.book_ids).unwrap_or_else(|_| Vec::new())
    }

    pub fn set_book_ids(&mut self, book_ids: Vec<String>) {
        self.book_ids = serde_json::to_string(&book_ids).unwrap_or_else(|_| "[]".to_string());
    }
}

impl SentenceMatchDb {
    pub fn new(
        batch_id: u64,
        source_book_id: String,
        source_book_name: String,
        source_page: i32,
        source_content: String,
        target_book_id: String,
        target_book_name: String,
        target_page: i32,
        target_content: String,
        similarity: f64,
        match_type: String,
    ) -> Self {
        let now = Utc::now().timestamp_millis() as u64;
        let match_id = SnowflakeGeneratorUtil::next();

        Self {
            id: None,
            match_id,
            batch_id,
            source_book_id,
            source_book_name,
            source_page,
            source_content,
            target_book_id,
            target_book_name,
            target_page,
            target_content,
            similarity,
            match_type,
            create_time: now,
        }
    }
}

// 转换函数：数据库模型 -> 命令模型
impl From<PlagiarismBatchDb> for PlagiarismBatch {
    fn from(db_batch: PlagiarismBatchDb) -> Self {
        let create_time = DateTime::from_timestamp_millis(db_batch.create_time as i64)
            .unwrap_or_else(|| Utc::now())
            .to_rfc3339();
        let update_time = DateTime::from_timestamp_millis(db_batch.update_time as i64)
            .unwrap_or_else(|| Utc::now())
            .to_rfc3339();
        let completed_time = db_batch.completed_time.map(|ts| {
            DateTime::from_timestamp_millis(ts as i64)
                .unwrap_or_else(|| Utc::now())
                .to_rfc3339()
        });

        let book_ids = db_batch.get_book_ids();

        Self {
            id: db_batch.batch_id.to_string(),
            name: db_batch.name,
            description: db_batch.description,
            book_ids,
            total_matches: db_batch.total_matches,
            status: db_batch.status,
            create_time,
            update_time,
            completed_time,
            progress: db_batch.progress,
        }
    }
}

impl From<SentenceMatchDb> for SentenceMatch {
    fn from(db_match: SentenceMatchDb) -> Self {
        let create_time = DateTime::from_timestamp_millis(db_match.create_time as i64)
            .unwrap_or_else(|| Utc::now())
            .to_rfc3339();

        Self {
            id: db_match.match_id.to_string(),
            source_book_id: db_match.source_book_id,
            source_book_name: db_match.source_book_name,
            source_page: db_match.source_page,
            source_content: db_match.source_content,
            target_book_id: db_match.target_book_id,
            target_book_name: db_match.target_book_name,
            target_page: db_match.target_page,
            target_content: db_match.target_content,
            similarity: db_match.similarity,
            match_type: db_match.match_type,
            create_time,
        }
    }
}

// 转换函数：命令模型 -> 数据库模型
impl From<SentenceMatch> for SentenceMatchDb {
    fn from(cmd_match: SentenceMatch) -> Self {
        let batch_id = cmd_match.id.parse::<u64>().unwrap_or(0); // 这里需要从批次ID获取
        let match_id = SnowflakeGeneratorUtil::next();
        let create_time = Utc::now().timestamp_millis() as u64;

        Self {
            id: None,
            match_id,
            batch_id, // 注意：这里需要正确的批次ID
            source_book_id: cmd_match.source_book_id,
            source_book_name: cmd_match.source_book_name,
            source_page: cmd_match.source_page,
            source_content: cmd_match.source_content,
            target_book_id: cmd_match.target_book_id,
            target_book_name: cmd_match.target_book_name,
            target_page: cmd_match.target_page,
            target_content: cmd_match.target_content,
            similarity: cmd_match.similarity,
            match_type: cmd_match.match_type,
            create_time,
        }
    }
}
