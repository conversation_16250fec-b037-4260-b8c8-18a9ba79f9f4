use lazy_static::lazy_static;
use serde::{Deserialize, Deserializer};
use serde_derive::Deserialize as DeserializeDerive;
use serde_json::{Map, Value};
use std::fs;
use std::sync::Mutex;
use surrealdb::engine::local::{Db, RocksDb};
use surrealdb::opt::Config;
use surrealdb::sql::Thing;
use surrealdb::Surreal;
use tauri::Manager;

const SDB_PATH: &str = "surrealDB";

#[derive(Debug, DeserializeDerive)]
pub struct Record {
    #[allow(dead_code)]
    id: Thing,
}

pub fn deserialize_id_u64<'de, D>(deserializer: D) -> Result<u64, D::Error>
where
    D: Deserializer<'de>,
{
    let value: Value = Deserialize::deserialize(deserializer)?;
    match value {
        Value::Number(n) => n
            .as_u64()
            .ok_or_else(|| serde::de::Error::custom("id转换失败：Expected a u64 value")),
        Value::String(s) => s
            .parse::<u64>()
            .map_err(|_| serde::de::Error::custom("id转换失败：Invalid u64 string")),
        Value::Object(map) => {
            if let Some(id_value) = map.get("id") {
                // !(id_value.clone());
                match id_value {
                    Value::Object(inner_map) => {
                        if let Some(inner_value) = inner_map.get("Number") {
                            match inner_value {
                                Value::Number(n) => n.as_u64().ok_or_else(|| serde::de::Error::custom("id转换失败：Expected a u64 value in the nested map")),
                                _ => Err(serde::de::Error::custom("id转换失败：Expected 'Number' to be a string or number in the nested map")),
                            }
                        } else if let Some(inner_value) = inner_map.get("String") {
                            match inner_value {
                                Value::String(s) => s.parse::<u64>().map_err(|_| serde::de::Error::custom("id转换失败：Invalid u64 string in the nested map")),
                                _ => Err(serde::de::Error::custom("id转换失败：Expected 'String' to be a string or number in the nested map")),
                            }
                        } else {
                            Err(serde::de::Error::custom(
                                "id转换失败：Expected 'Number' in the nested map",
                            ))
                        }
                    }
                    _ => Err(serde::de::Error::custom(
                        "id转换失败：Expected 'id' to be a string or number in the map",
                    )),
                }
            } else {
                Err(serde::de::Error::custom(
                    "id转换失败：Expected 'id' in the map",
                ))
            }
        }
        _ => Err(serde::de::Error::custom(
            "id转换失败：Expected a string, number, or map for ID",
        )),
    }
}

pub fn deserialize_id_string<'de, D>(deserializer: D) -> Result<String, D::Error>
where
    D: Deserializer<'de>,
{
    let value: Value = Deserialize::deserialize(deserializer)?;
    match value {
        Value::Number(n) => Ok(n.to_string()),
        Value::String(s) => Ok(s),
        Value::Object(map) => {
            if let Some(id_value) = map.get("id") {

                match id_value {
                    Value::Object(inner_map) => {
                        if let Some(inner_value) = inner_map.get("String") {
                            match inner_value {
                                Value::String(s) => Ok(s.clone()),
                                _ => Err(serde::de::Error::custom("id转换失败：Expected 'String' to be a string or number in the nested map")),
                            }
                        } else if let Some(inner_value) = inner_map.get("Number") {
                            match inner_value {
                                Value::Number(n) => Ok(n.to_string()),
                                _ => Err(serde::de::Error::custom("id转换失败：Expected 'Number' to be a string or number in the nested map")),
                            }
                        } else {
                            Err(serde::de::Error::custom(
                                "id转换失败：Expected 'Number' in the nested map",
                            ))
                        }
                    }
                    _ => Err(serde::de::Error::custom(
                        "id转换失败：Expected 'id' to be a string or number in the map",
                    )),
                }
            } else {
                Err(serde::de::Error::custom("Expected 'id' in the map"))
            }
        }
        Value::Null => Ok("".to_string()),
        _ => Err(serde::de::Error::custom(
            "Expected a string, number, or map for ID",
        )),
    }
}

pub fn deserialize_id_string_option<'de, D>(deserializer: D) -> Result<Option<String>, D::Error>
where
    D: Deserializer<'de>,
{
    // Otherwise, call the original deserialize_id_string method
    let string_value: String = deserialize_id_string(deserializer)?;
    Ok(Some(string_value))
}

lazy_static! {
    static ref DB: Mutex<Option<Surreal<Db>>> = Mutex::new(None);
}

pub async fn init_db(app_handle: tauri::AppHandle) -> Result<String, String> {
    let app_data_dir = app_handle.path().app_data_dir().unwrap();

    if !app_data_dir.exists() {
        fs::create_dir_all(&app_data_dir).unwrap();
    }

    let s_db = app_data_dir.join(SDB_PATH);
    let dba = Surreal::new::<RocksDb>((s_db, Config::default())).await;
    if dba.is_ok() {
        let db = dba.unwrap();
        db.use_ns("qct").use_db("qct").await.unwrap();
        let mut db_option = DB.lock().unwrap();
        *db_option = Some(db);
    }
    Ok("finish init".to_string())
}

// pub async fn init_db_for_test() -> Result<String, String> {
//     let mut dir = PathBuf::new();
//     dir.push(r"C:\Users\<USER>\AppData\Roaming\com.qctchina.proposition-assistant");
//     let s_db = dir.join(SDB_PATH);
//     let dba = Surreal::new::<RocksDb>((s_db, Config::default())).await;
//     if dba.is_ok() {
//         let db = dba.unwrap();
//         db.use_ns("qct").use_db("qct").await.unwrap();
//         let mut db_option = DB.lock().unwrap();
//         *db_option = Some(db);
//     }
//     Ok("finish init".to_string())
// }

pub async fn get_db() -> Surreal<Db> {
    let db_option = DB.lock().unwrap();
    let db = db_option.clone().unwrap();
    db
}

pub fn get_update_map(value: Value) -> Map<String, Value> {
    get_update_map_specify_id_field(value, "id")
}

pub fn get_update_map_specify_id_field(value: Value, id_field_name: &str) -> Map<String, Value> {
    let mut map: Map<String, Value> = Default::default();
    if let Some(obj) = value.as_object() {
        map = obj.clone();
        map.remove(id_field_name); // Remove the "id" field
    } else {
        println!("Failed to convert the task to a map");
    }
    map
}

// pub fn get_update_map_with_remove(value: Value, fields_to_remove: &[&str]) -> Map<String, Value> {
//     let mut map = get_update_map(value);
//     for field in fields_to_remove {
//         map.remove(*field); // Remove the specified field
//     }
//     map
// }

#[derive(Debug, DeserializeDerive)]
pub enum FieldValue {
    Str(String),
    Num(u64),
    Bool(bool),
}

#[derive(Debug, DeserializeDerive)]
pub struct QueryOption {
    field_name: String,
    field_value: FieldValue,
    is_fuzzy: bool,
    is_key: bool,
}

impl QueryOption {
    pub fn new(field_name: String, field_value: FieldValue, is_fuzzy: bool, is_key: bool) -> Self {
        Self {
            field_name,
            field_value,
            is_fuzzy,
            is_key,
        }
    }
}
#[derive(Debug, DeserializeDerive)]
pub struct OrderOption {
    field_name: String,
    is_desc: bool,
}

impl OrderOption {
    pub fn new(field_name: String, is_desc: bool) -> Self {
        Self {
            field_name,
            is_desc,
        }
    }
}

/// 因为surreal目前不支持动态条件，临时增加这个用以生成surrealdb sql语句的方法
/// 参数：table：表名
/// query_options：查询参数列表
/// order_options：排序参数列表
///
/// 对于查询参数：1.支持类型为string, num(u64), bool (后续扩展)
/// string时，如果是key（主键），就采用 ${table}:⟨${value}⟩的格式，注意特殊尖括号
///             不是key，就根据fuzzy模糊匹配，~或者=
///
/// num(u64)时，如果是key，则采用${table}:${value}，非key则采用= 不处理fuzzy
///
/// bool时只处理本身true和false，不处理key和fuzzy内容
pub fn get_dynamic_sql_string(
    table: String,
    query_options: Option<Vec<QueryOption>>,
    order_options: Option<Vec<OrderOption>>,
) -> String {
    let mut sql = format!("select * from {}", table);
    if !query_options.is_none() {
        let querys = query_options.unwrap();
        // 处理查询条件
        if !querys.is_empty() {
            let mut conditions = Vec::new();
            for option in querys {
                let condition = match option.field_value {
                    FieldValue::Str(ref value) => {
                        if value.is_empty() {
                            continue;
                        }
                        if option.is_key {
                            format!("{} = {}:⟨{}⟩", option.field_name, table, value)
                        } else {
                            if option.is_fuzzy {
                                format!("{} ~ '{}'", option.field_name, value)
                            } else {
                                format!("{} = '{}'", option.field_name, value)
                            }
                        }
                    }
                    FieldValue::Num(value) => {
                        if option.is_key {
                            format!("{} = {}:{}", option.field_name, table, value)
                        } else {
                            format!("{} = {}", option.field_name, value)
                        }
                    }
                    FieldValue::Bool(value) => {
                        if value {
                            format!("{} = {}", option.field_name, "true")
                        } else {
                            format!("{} = {}", option.field_name, "false")
                        }
                    }
                };
                conditions.push(condition);
            }
            if !conditions.is_empty() {
                sql.push_str(" where ");
                sql.push_str(&conditions.join(" and "));
            }
        }
    }

    // 处理排序条件
    if !order_options.is_none() {
        let orders = order_options.unwrap();
        if !orders.is_empty() {
            let mut order_clauses = Vec::new();
            for option in orders {
                let order_clause = if option.is_desc {
                    format!("{} desc", option.field_name)
                } else {
                    format!("{} asc", option.field_name)
                };
                order_clauses.push(order_clause);
            }

            if !order_clauses.is_empty() {
                sql.push_str(" order by ");
                sql.push_str(&order_clauses.join(", "));
            }
        }
    }

    sql
}

pub fn get_dynamic_count_sql_string(
    table: String,
    query_options: Option<Vec<QueryOption>>,
    order_options: Option<Vec<OrderOption>>,
) -> String {
    let base_sql: String = get_dynamic_sql_string(table, query_options, order_options);
    format!("count({})", base_sql)
}
